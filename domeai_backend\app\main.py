from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from sqlalchemy import text
import subprocess
import sys
import os
from contextlib import asynccontextmanager

from app.api.v1.api import api_router
from app.core.config import settings
from app.core.database import get_db

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Run database migrations on startup.
    This is needed for free tier deployments that don't support pre-deploy commands.
    """
    try:
        print("Running database migrations...")
        # Run Alembic migrations
        result = subprocess.run(
            [sys.executable, "-m", "alembic", "upgrade", "head"],
            cwd=os.getcwd(),
            capture_output=True,
            text=True,
            check=True
        )
        print("Database migrations completed successfully!")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"Migration failed: {e}")
        print(f"Error output: {e.stderr}")
        # Don't fail the startup - let the app try to run anyway
        pass
    except Exception as e:
        print(f"Unexpected error during migration: {e}")
        # Don't fail the startup - let the app try to run anyway
        pass

    yield  # App runs here

    # Cleanup (if needed) happens after yield


# Create FastAPI app
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=lifespan,
)

# Set up CORS middleware
# Include Hostinger Horizons origins for contest web frontend
hostinger_origins = [
    "https://*.horizons.hostinger.com",
    "https://*.app-preview.com",
    "https://eeba9c76-b129-4780-9a96-74cd92c53b65.dev12.app-preview.com",
    "https://limegreen-snail-614837.hostingersite.com"
]

# Include Lovable.dev origins for contest web frontend
lovable_origins = [
    "https://lovable.dev",
    "https://lovableproject.com",
    "http://localhost:3000",  # For local development
    "http://localhost:5173",  # Vite dev server
    # Specific Lovable domains for DomeAI contest app:
    "https://dome-ai-scam-detector.lovable.app",
    "https://preview--dome-ai-scam-detector.lovable.app"
]

# Combine configured origins with Hostinger and Lovable origins
all_origins = []
if settings.BACKEND_CORS_ORIGINS:
    all_origins.extend([str(origin) for origin in settings.BACKEND_CORS_ORIGINS])
all_origins.extend(hostinger_origins)
all_origins.extend(lovable_origins)

app.add_middleware(
    CORSMiddleware,
    allow_origins=all_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)





@app.get("/")
def root():
    """
    Root endpoint that returns a welcome message.
    """
    return {"message": "DomeAI Backend is running!"}


@app.get("/health", status_code=status.HTTP_200_OK)
def health_check(db: Session = Depends(get_db)):
    """
    Health check endpoint that tests database connection and returns a 200 OK status.
    If the database connection fails, it returns a 503 Service Unavailable status.
    """
    try:
        # Test database connection with a simple query
        db.execute(text("SELECT 1"))
        # Test pgvector extension
        db.execute(text("SELECT 'pgvector is installed' FROM pg_extension WHERE extname = 'vector'"))
        return {
            "status": "ok",
            "database": "connected",
            "pgvector": "installed"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Health check failed: {str(e)}"
        )


@app.post("/admin/fix-pgvector")
async def fix_pgvector_endpoint():
    """Temporary endpoint to fix pgvector extension and table structure"""
    try:
        import subprocess
        import os

        # Run the fix script
        result = subprocess.run(
            ["python", "fix_pgvector_production.py"],
            cwd="/opt/render/project/src",
            capture_output=True,
            text=True,
            timeout=60
        )

        return {
            "status": "completed",
            "stdout": result.stdout,
            "stderr": result.stderr,
            "return_code": result.returncode
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@app.post("/admin/add-test-kb")
async def add_test_knowledge_base():
    """Add test knowledge base chunks to verify RAG pipeline"""
    try:
        from app.services.ai_service_factory import get_ai_service
        from app.core.database import SessionLocal
        from sqlalchemy import text

        # Test knowledge base chunks
        test_chunks = [
            {
                "content": "Cashier's check overpayment scams involve receiving a check for more than the agreed amount and being asked to wire back the difference. The original check will bounce, leaving the victim responsible for the full amount.",
                "source": "kb_test_cashier_check"
            },
            {
                "content": "Zelle payment scams often involve receiving unexpected money from strangers who then ask you to send it back via a different payment method. This is designed to exploit the irreversible nature of Zelle transfers.",
                "source": "kb_test_zelle_scam"
            },
            {
                "content": "Romance scams typically involve building emotional relationships online before requesting money for emergencies, travel, or other urgent needs. The scammer often claims to be traveling, in the military, or facing a crisis.",
                "source": "kb_test_romance_scam"
            }
        ]

        db = SessionLocal()
        ai_service = get_ai_service(user_tier="premium")

        results = []

        try:
            # Clear existing test chunks
            db.execute(text("DELETE FROM knowledge_base_chunks WHERE source LIKE 'kb_test_%'"))
            db.commit()

            for i, chunk in enumerate(test_chunks):
                try:
                    # Generate embedding
                    embedding = await ai_service.get_text_embedding(text=chunk['content'])
                    embedding_str = '[' + ','.join(map(str, embedding)) + ']'

                    # Insert with proper vector casting using string formatting
                    query = f"""
                        INSERT INTO knowledge_base_chunks (content, embedding, source, created_at)
                        VALUES ('{chunk['content'].replace("'", "''")}', '{embedding_str}'::vector(1536), '{chunk['source']}', now())
                    """
                    db.execute(text(query))

                    results.append(f"✅ Added chunk {i+1}: {chunk['source']}")

                except Exception as e:
                    results.append(f"❌ Failed chunk {i+1}: {str(e)}")

            db.commit()

            # Verify insertion
            result = db.execute(text("SELECT COUNT(*) FROM knowledge_base_chunks WHERE source LIKE 'kb_test_%'"))
            count = result.scalar()

            return {
                "status": "success",
                "chunks_added": count,
                "details": results
            }

        finally:
            db.close()

    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@app.get("/admin/check-kb-status")
async def check_knowledge_base_status():
    """Check the current status of the knowledge base"""
    try:
        from app.core.database import SessionLocal
        from sqlalchemy import text

        db = SessionLocal()

        try:
            # Check total count
            result = db.execute(text("SELECT COUNT(*) FROM knowledge_base_chunks"))
            total_count = result.scalar()

            # Check by source patterns
            result = db.execute(text("""
                SELECT
                    CASE
                        WHEN source LIKE 'kb_test_%' THEN 'test_chunks'
                        WHEN source LIKE 'scam_knowledge_base/%' THEN 'real_kb_chunks'
                        ELSE 'other'
                    END as chunk_type,
                    COUNT(*) as count
                FROM knowledge_base_chunks
                GROUP BY chunk_type
                ORDER BY count DESC
            """))
            breakdown = result.fetchall()

            # Check embedding column type
            result = db.execute(text("""
                SELECT data_type, udt_name
                FROM information_schema.columns
                WHERE table_name = 'knowledge_base_chunks'
                AND column_name = 'embedding'
            """))
            column_info = result.fetchone()

            # Test vector operations
            vector_test = "unknown"
            try:
                test_vector = "[" + ",".join(["0.1"] * 1536) + "]"
                result = db.execute(text("""
                    SELECT 1 - (:test_vector::vector(1536) <-> :test_vector::vector(1536)) as similarity
                """), {"test_vector": test_vector})
                similarity = result.scalar()
                vector_test = f"working (similarity: {similarity})"
            except Exception as e:
                vector_test = f"failed: {str(e)}"

            return {
                "status": "success",
                "total_chunks": total_count,
                "breakdown": [{"type": row[0], "count": row[1]} for row in breakdown],
                "embedding_column": {
                    "data_type": column_info[0] if column_info else "unknown",
                    "udt_name": column_info[1] if column_info else "unknown"
                },
                "vector_operations": vector_test
            }

        finally:
            db.close()

    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@app.post("/admin/load-real-kb")
async def load_real_knowledge_base():
    """Load the real 57 knowledge base chunks from the JSON files"""
    try:
        import json
        import os
        from app.services.ai_service_factory import get_ai_service
        from app.core.database import SessionLocal
        from sqlalchemy import text

        # Try to find the knowledge base files
        kb_files = []
        possible_paths = [
            "/opt/render/project/src/scripts/cleaned_kb_chunks.json",
            "/opt/render/project/src/scripts/new_kb_chunks.json",
            "/opt/render/project/src/cleaned_kb_chunks.json",
            "/opt/render/project/src/new_kb_chunks.json"
        ]

        chunks = []
        for path in possible_paths:
            if os.path.exists(path):
                try:
                    with open(path, 'r') as f:
                        file_chunks = json.load(f)
                        if isinstance(file_chunks, list):
                            chunks.extend(file_chunks)
                            kb_files.append(path)
                except Exception as e:
                    continue

        if not chunks:
            return {
                "status": "error",
                "error": "No knowledge base files found",
                "searched_paths": possible_paths
            }

        db = SessionLocal()
        ai_service = get_ai_service(user_tier="premium")

        results = []
        successful = 0
        failed = 0

        try:
            # Clear existing chunks (except test ones)
            db.execute(text("DELETE FROM knowledge_base_chunks WHERE source NOT LIKE 'kb_test_%'"))
            db.commit()
            results.append(f"✅ Cleared existing non-test chunks")

            for i, chunk in enumerate(chunks):
                try:
                    content = chunk.get('content', '').strip()
                    source = chunk.get('source', f'kb_chunk_{i}')

                    if not content:
                        results.append(f"⚠️ Skipped chunk {i+1}: Empty content")
                        continue

                    # Generate embedding
                    embedding = await ai_service.get_text_embedding(text=content)
                    embedding_str = '[' + ','.join(map(str, embedding)) + ']'

                    # Insert with proper vector casting using string formatting
                    query = f"""
                        INSERT INTO knowledge_base_chunks (content, embedding, source, created_at)
                        VALUES ('{content.replace("'", "''")}', '{embedding_str}'::vector(1536), '{source}', now())
                    """
                    db.execute(text(query))

                    successful += 1
                    if i < 5:  # Show first 5 for verification
                        results.append(f"✅ Added chunk {i+1}: {source[:50]}...")

                except Exception as e:
                    failed += 1
                    results.append(f"❌ Failed chunk {i+1}: {str(e)}")

            db.commit()

            # Verify final count
            result = db.execute(text("SELECT COUNT(*) FROM knowledge_base_chunks WHERE source NOT LIKE 'kb_test_%'"))
            final_count = result.scalar()

            return {
                "status": "success",
                "files_loaded": kb_files,
                "total_chunks_processed": len(chunks),
                "successful": successful,
                "failed": failed,
                "final_count_in_db": final_count,
                "details": results[:10]  # Show first 10 results
            }

        finally:
            db.close()

    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@app.post("/admin/upload-kb-chunks")
async def upload_knowledge_base_chunks(chunks_data: dict):
    """Upload knowledge base chunks directly via API"""
    try:
        from app.services.ai_service_factory import get_ai_service
        from app.core.database import SessionLocal
        from sqlalchemy import text

        chunks = chunks_data.get('chunks', [])
        if not chunks:
            return {
                "status": "error",
                "error": "No chunks provided in request body"
            }

        db = SessionLocal()
        ai_service = get_ai_service(user_tier="premium")

        results = []
        successful = 0
        failed = 0

        try:
            # Clear existing chunks (except test ones)
            db.execute(text("DELETE FROM knowledge_base_chunks WHERE source NOT LIKE 'kb_test_%'"))
            db.commit()
            results.append(f"✅ Cleared existing non-test chunks")

            for i, chunk in enumerate(chunks):
                try:
                    content = chunk.get('content', '').strip()
                    source = chunk.get('source', f'kb_chunk_{i}')

                    if not content:
                        results.append(f"⚠️ Skipped chunk {i+1}: Empty content")
                        continue

                    # Generate embedding
                    embedding = await ai_service.get_text_embedding(text=content)
                    embedding_str = '[' + ','.join(map(str, embedding)) + ']'

                    # Insert with proper vector casting using string formatting
                    query = f"""
                        INSERT INTO knowledge_base_chunks (content, embedding, source, created_at)
                        VALUES ('{content.replace("'", "''")}', '{embedding_str}'::vector(1536), '{source}', now())
                    """
                    db.execute(text(query))

                    successful += 1
                    if i < 5:  # Show first 5 for verification
                        results.append(f"✅ Added chunk {i+1}: {source[:50]}...")

                except Exception as e:
                    failed += 1
                    results.append(f"❌ Failed chunk {i+1}: {str(e)}")

                # Commit every 10 chunks to avoid timeouts
                if (i + 1) % 10 == 0:
                    db.commit()
                    results.append(f"💾 Committed batch {i+1}")

            db.commit()

            # Verify final count
            result = db.execute(text("SELECT COUNT(*) FROM knowledge_base_chunks WHERE source NOT LIKE 'kb_test_%'"))
            final_count = result.scalar()

            return {
                "status": "success",
                "total_chunks_processed": len(chunks),
                "successful": successful,
                "failed": failed,
                "final_count_in_db": final_count,
                "details": results[:15]  # Show first 15 results
            }

        finally:
            db.close()

    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@app.get("/admin/list-users")
async def list_users_endpoint():
    """List all users in database for debugging"""
    try:
        from app.core.database import SessionLocal
        from app.models.user import User

        db = SessionLocal()

        try:
            # Get all users
            users = db.query(User).all()

            user_list = []
            for user in users:
                user_list.append({
                    "id": user.id,
                    "email": user.email,
                    "subscription_tier": user.subscription_tier,
                    "monthly_scan_allowance": user.monthly_scan_allowance,
                    "scans_this_month": user.scans_this_month,
                    "is_active": user.is_active,
                    "created_at": user.created_at.isoformat() if user.created_at else None
                })

            return {
                "status": "success",
                "total_users": len(user_list),
                "users": user_list
            }

        finally:
            db.close()

    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@app.post("/admin/update-user-premium")
async def update_user_to_premium_endpoint():
    """Update specific user to premium tier for Hostinger Horizons contest"""
    try:
        from app.core.database import SessionLocal
        from app.models.user import User
        from datetime import datetime, timezone

        # Target user email - try both variations
        TARGET_EMAILS = ["<EMAIL>", "<EMAIL>"]

        db = SessionLocal()

        try:
            user = None
            found_email = None

            # Try to find user with either email
            for email in TARGET_EMAILS:
                user = db.query(User).filter(User.email == email).first()
                if user:
                    found_email = email
                    break

            if not user:
                return {
                    "status": "error",
                    "error": f"User not found with any of these emails: {TARGET_EMAILS}"
                }

            # Store current values for logging
            old_tier = user.subscription_tier
            old_allowance = user.monthly_scan_allowance
            old_scans = user.scans_this_month

            # Update user to premium tier
            user.subscription_tier = "premium"
            user.monthly_scan_allowance = 100
            user.scans_this_month = 0
            user.expert_scan_allowance = 0
            user.expert_scans_this_month = 0
            user.updated_at = datetime.now(timezone.utc)

            # Commit changes
            db.add(user)
            db.commit()
            db.refresh(user)

            return {
                "status": "success",
                "message": f"User {found_email} successfully updated to premium tier",
                "user_id": user.id,
                "found_email": found_email,
                "changes": {
                    "subscription_tier": {"old": old_tier, "new": user.subscription_tier},
                    "monthly_scan_allowance": {"old": old_allowance, "new": user.monthly_scan_allowance},
                    "scans_this_month": {"old": old_scans, "new": user.scans_this_month},
                    "expert_scan_allowance": {"new": user.expert_scan_allowance},
                    "expert_scans_this_month": {"new": user.expert_scans_this_month}
                }
            }

        finally:
            db.close()

    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@app.get("/admin/contest-status")
async def get_contest_status():
    """Get current contest mode status"""
    return {
        "status": "success",
        "contest_mode_enabled": settings.HOSTINGER_HORIZONS_CONTEST_MODE,
        "description": "When enabled, all new users default to premium tier and all users get premium AI models"
    }


@app.post("/admin/toggle-contest-mode")
async def toggle_contest_mode():
    """Toggle contest mode on/off (for easy reversal after contest)"""
    try:
        # Toggle the setting
        settings.HOSTINGER_HORIZONS_CONTEST_MODE = not settings.HOSTINGER_HORIZONS_CONTEST_MODE

        return {
            "status": "success",
            "message": f"Contest mode {'enabled' if settings.HOSTINGER_HORIZONS_CONTEST_MODE else 'disabled'}",
            "contest_mode_enabled": settings.HOSTINGER_HORIZONS_CONTEST_MODE
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }
