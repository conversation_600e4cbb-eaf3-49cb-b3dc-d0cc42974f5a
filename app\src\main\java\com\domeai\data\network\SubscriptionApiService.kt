package com.domeai.data.network

import com.domeai.data.model.network.ChangePlanRequest
import com.domeai.data.model.network.ChangePlanResponse
import com.domeai.data.model.network.SubscriptionDetailsResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST

/**
 * API service for subscription operations
 */
interface SubscriptionApiService {
    /**
     * Get current subscription details
     */
    @GET("api/v1/subscriptions/details")
    suspend fun getSubscriptionDetails(
        @Header("Authorization") token: String
    ): Response<SubscriptionDetailsResponse>

    /**
     * Change subscription plan
     */
    @POST("api/v1/subscriptions/change-plan")
    suspend fun changePlan(
        @Header("Authorization") token: String,
        @Body request: ChangePlanRequest
    ): Response<ChangePlanResponse>
}
