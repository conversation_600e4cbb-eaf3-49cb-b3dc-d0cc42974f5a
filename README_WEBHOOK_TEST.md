# Google Play RTDN Webhook Test

This is a simple standalone FastAPI application to test the Google Play Real-Time Developer Notifications (RTDN) webhook functionality without needing the full backend environment.

## Prerequisites

Before running the test, make sure you have the following installed:

1. Python 3.9 or higher
2. FastAPI and Uvicorn:
   ```
   pip install fastapi uvicorn
   ```

## Running the Test

### Option 1: Using the Batch File (Windows)

1. Simply double-click the `test_simple_webhook.bat` file or run it from the command line:
   ```
   .\test_simple_webhook.bat
   ```

2. This will:
   - Start the FastAPI server
   - Send a test notification
   - Send a subscription purchased notification
   - Wait for you to press a key to exit
   - Stop the server when you're done

### Option 2: Manual Testing

1. Start the FastAPI server:
   ```
   python -m uvicorn simple_webhook_test:app --reload
   ```

2. In a separate terminal, send a test notification:
   ```
   curl -X POST http://localhost:8000/api/v1/webhooks/googleplay/rtdn -H "Content-Type: application/json" -d "{\"message\":{\"data\":\"eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJjb20uZG9tZWFpLnNjYW1kZXRlY3RvciIsImV2ZW50VGltZU1pbGxpcyI6IjE2MjEyMzQ1Njc4OTAiLCJ0ZXN0Tm90aWZpY2F0aW9uIjp7InZlcnNpb24iOiIxLjAifX0=\",\"messageId\":\"test-message-id\",\"publishTime\":\"2023-05-20T10:00:00.000Z\"},\"subscription\":\"projects/domeai-project/subscriptions/google-play-rtdn-subscription\"}"
   ```

3. Send a subscription purchased notification:
   ```
   curl -X POST http://localhost:8000/api/v1/webhooks/googleplay/rtdn -H "Content-Type: application/json" -d "{\"message\":{\"data\":\"eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJjb20uZG9tZWFpLnNjYW1kZXRlY3RvciIsImV2ZW50VGltZU1pbGxpcyI6IjE2MjEyMzQ1Njc4OTAiLCJzdWJzY3JpcHRpb25Ob3RpZmljYXRpb24iOnsidmVyc2lvbiI6IjEuMCIsIm5vdGlmaWNhdGlvblR5cGUiOjQsInB1cmNoYXNlVG9rZW4iOiJ0ZXN0LXB1cmNoYXNlLXRva2VuIiwic3Vic2NyaXB0aW9uSWQiOiJkb21lYWlfcHJlbWl1bV9tb250aGx5In19\",\"messageId\":\"test-subscription-message-id\",\"publishTime\":\"2023-05-20T10:00:00.000Z\"},\"subscription\":\"projects/domeai-project/subscriptions/google-play-rtdn-subscription\"}"
   ```

4. Check the server logs to see the processed notifications

## What to Look For

When you run the test, you should see log messages in the terminal where the FastAPI server is running. These logs will show:

1. The receipt of the notification
2. The decoded notification details
3. The type of notification (test, subscription, etc.)
4. For subscription notifications, the specific action (purchase, renewal, cancellation, etc.)

A successful test will show logs similar to:

```
INFO:simple_webhook_test:Received Google Play RTDN from subscription: projects/domeai-project/subscriptions/google-play-rtdn-subscription
INFO:simple_webhook_test:RTDN Version: 1.0
INFO:simple_webhook_test:Package Name: com.domeai.scamdetector
INFO:simple_webhook_test:Event Time: 1621234567890
INFO:simple_webhook_test:Test Notification
```

And for the subscription notification:

```
INFO:simple_webhook_test:Received Google Play RTDN from subscription: projects/domeai-project/subscriptions/google-play-rtdn-subscription
INFO:simple_webhook_test:RTDN Version: 1.0
INFO:simple_webhook_test:Package Name: com.domeai.scamdetector
INFO:simple_webhook_test:Event Time: 1621234567890
INFO:simple_webhook_test:Subscription Notification Type: 4
INFO:simple_webhook_test:Subscription ID: domeai_premium_monthly
INFO:simple_webhook_test:New subscription purchased
```

## Next Steps

Once you've verified that the webhook is working correctly with this simple test, you can:

1. Integrate this functionality with your full backend
2. Connect it to your database to update user subscription status
3. Implement the actual Google Play API integration for verifying purchases

## Troubleshooting

If you encounter any issues:

1. Make sure Python and the required packages are installed
2. Check that port 8000 is not being used by another application
3. Verify that the curl commands are properly formatted
4. If using Windows PowerShell, you may need to escape the quotes differently
