<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Internet permission for API calls -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- Network state permission to check connectivity -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- Foreground service permission for overlay -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- Draw over other apps permission for overlay -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <!-- Post notifications permission for Android 13+ -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- Camera permission for profile picture -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- Storage permissions for profile picture -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <!-- Google Play Billing permission -->
    <uses-permission android:name="com.android.vending.BILLING" />

    <application
        android:name=".DomeAIApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.DomeAI"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <!-- Main Activity -->
        <activity
            android:name=".presentation.main.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.DomeAI"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Overlay Test Activity -->
        <activity
            android:name=".presentation.test.OverlayTestActivity"
            android:exported="true"
            android:theme="@style/Theme.DomeAI"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- Overlay Services -->
        <service
            android:name=".service.OverlayService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <service
            android:name=".service.FloatingViewService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <service
            android:name=".service.SimpleFloatingViewService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <service
            android:name=".service.BasicOverlayService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <service
            android:name=".service.StandardOverlayService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <service
            android:name=".service.SimpleOverlayService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- Firebase Messaging Service - Commented out until we set up Firebase
        <service
            android:name=".service.DomeAIFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        -->

        <!-- WorkManager Initialization Provider -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
        </provider>

        <!-- FileProvider for camera capture -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>