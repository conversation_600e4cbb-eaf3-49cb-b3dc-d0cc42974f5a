"""create user table

Revision ID: 001
Revises:
Create Date: 2025-05-08 06:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Create users table with all fields
    op.create_table(
        'users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('hashed_password', sa.String(), nullable=False),
        sa.Column('is_active', sa.Bo<PERSON>an(), nullable=False, server_default='true'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
        # Subscription fields
        sa.Column('subscription_tier', sa.String(10), nullable=False, server_default='free'),
        sa.Column('monthly_scan_allowance', sa.Integer(), nullable=False, server_default='5'),
        sa.Column('scans_this_month', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('scan_counter_reset_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        # Expert scan fields
        sa.Column('expert_scan_allowance', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('expert_scans_this_month', sa.Integer(), nullable=False, server_default='0'),
        # Google Play subscription fields
        sa.Column('subscription_provider', sa.String(20), nullable=True),
        sa.Column('provider_subscription_id', sa.String(255), nullable=True),
        sa.Column('google_play_purchase_token', sa.Text(), nullable=True),
        sa.Column('subscription_product_id', sa.String(50), nullable=True),
        sa.Column('subscription_expiry_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('auto_renew_status', sa.Boolean(), nullable=True),
        sa.Column('is_trial_period', sa.Boolean(), nullable=True),
        sa.Column('last_rtdn_received_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # Create indexes
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_provider_subscription_id'), 'users', ['provider_subscription_id'], unique=True)
    op.create_index(op.f('ix_users_subscription_tier'), 'users', ['subscription_tier'], unique=False)


def downgrade():
    # Drop indexes
    op.drop_index(op.f('ix_users_subscription_tier'), table_name='users')
    op.drop_index(op.f('ix_users_provider_subscription_id'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')

    # Drop users table
    op.drop_table('users')
