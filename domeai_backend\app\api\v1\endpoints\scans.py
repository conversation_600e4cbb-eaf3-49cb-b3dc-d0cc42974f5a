import os
import uuid
import shutil
import logging
import base64
from datetime import datetime, timedelta, timezone
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Body, status, UploadFile, File, Form, Request
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
from app.core.config import settings
from app.tasks.scan_tasks import process_scan_task
from app.services.ai_service_factory import get_ai_service
from app.crud import crud_scan_session

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/test-debug/")
def test_debug_endpoint():
    """Test endpoint to verify our code is running"""
    logger.error("CRITICAL: TEST DEBUG ENDPOINT CALLED!")
    return {"status": "debug_endpoint_working", "message": "Our code is running!"}


@router.post("/debug-payload/")
async def debug_payload_endpoint(request: Request):
    """Debug endpoint to capture exact payload from Hostinger Horizons"""
    try:
        # Get raw body
        body = await request.body()
        logger.error("=== PAYLOAD DEBUG START ===")
        logger.error(f"Content-Type: {request.headers.get('content-type')}")
        logger.error(f"Authorization: {request.headers.get('authorization', 'NOT PROVIDED')}")
        logger.error(f"Raw body: {body}")

        # Try to parse as JSON
        try:
            import json
            json_data = json.loads(body)
            logger.error(f"Parsed JSON: {json_data}")
        except Exception as e:
            logger.error(f"Failed to parse as JSON: {e}")

        logger.error("=== PAYLOAD DEBUG END ===")

        return {
            "status": "debug_success",
            "content_type": request.headers.get('content-type'),
            "body_length": len(body),
            "has_auth": "authorization" in request.headers
        }
    except Exception as e:
        logger.error(f"Debug endpoint error: {e}")
        return {"status": "debug_error", "error": str(e)}


@router.post(
    "/",
    response_model=schemas.scan.Scan,
    status_code=status.HTTP_202_ACCEPTED
)
def create_scan(
    *,
    db: Session = Depends(deps.get_db),
    scan_in: schemas.scan.ScanCreate = Body(...),
    use_expert_scan: bool = Body(False),
    current_user: models.user.User = Depends(deps.get_current_user)
) -> models.scan.Scan:
    """
    Create a new scan request.

    The scan will be processed asynchronously by a Celery worker.
    Returns the created scan object with status "pending".

    Args:
        use_expert_scan: If True and user is on expert tier, use expert scan credits
    """
    # CRITICAL DEBUG: Log the exact payload received from Hostinger Horizons
    logger.error("=== SCAN SUBMISSION DEBUG START ===")
    logger.error(f"User: {current_user.email}")
    logger.error(f"scan_in object: {scan_in}")
    logger.error(f"scan_in.input_text: {scan_in.input_text}")
    logger.error(f"scan_in.input_url: {scan_in.input_url}")
    logger.error(f"scan_in.input_content_type: {scan_in.input_content_type}")
    logger.error(f"scan_in.user_provided_context: {scan_in.user_provided_context}")
    logger.error(f"scan_in.scan_session_id: {scan_in.scan_session_id}")
    logger.error(f"use_expert_scan: {use_expert_scan}")
    logger.error("=== SCAN SUBMISSION DEBUG END ===")

    # Check if this is an expert scan request
    is_expert_scan = use_expert_scan and current_user.subscription_tier == "expert"

    # If the reset date is more than a month old, reset the counters
    now = datetime.now(timezone.utc)
    if now - current_user.scan_counter_reset_at.replace(tzinfo=timezone.utc) > timedelta(days=30):
        current_user.scans_this_month = 0
        current_user.expert_scans_this_month = 0
        current_user.scan_counter_reset_at = now

    # Handle scan session
    scan_session_id = scan_in.scan_session_id
    is_session_followup = False

    # Get session limits based on user tier
    session_max_inputs = settings.SESSION_MAX_INPUTS_PREMIUM  # Default to premium limit
    if current_user.subscription_tier == "expert":
        session_max_inputs = settings.SESSION_MAX_INPUTS_EXPERT

    if scan_session_id:
        # Verify the session exists and belongs to the user
        session = crud_scan_session.get_scan_session(
            db=db, session_id=scan_session_id, owner_id=current_user.id
        )
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Scan session not found or does not belong to you"
            )

        # Check if the session has reached its input limit
        if current_user.subscription_tier in ["premium", "expert"]:
            # Count existing scans in the session
            existing_scans = db.query(models.scan.Scan).filter(
                models.scan.Scan.scan_session_id == scan_session_id
            ).count()

            # Check if session has reached the input limit
            if existing_scans >= session_max_inputs:
                logger.info(f"Session {scan_session_id} has reached its input limit of {session_max_inputs}")

                # Create a new session instead of using the existing one
                session = crud_scan_session.create_scan_session(db=db, owner_id=current_user.id)
                scan_session_id = session.id

                # This is a new session, not a followup, so it will consume a scan credit
                is_session_followup = False

                logger.info(f"Created new session {scan_session_id} after reaching input limit")
            else:
                # Check if the session has been inactive for too long
                now = datetime.now(timezone.utc)
                session_activity_window = timedelta(hours=settings.SESSION_ACTIVITY_WINDOW_HOURS)

                if now - session.last_activity_at > session_activity_window:
                    logger.info(f"Session {scan_session_id} has been inactive for more than {settings.SESSION_ACTIVITY_WINDOW_HOURS} hours")

                    # Create a new session instead of using the existing one
                    session = crud_scan_session.create_scan_session(db=db, owner_id=current_user.id)
                    scan_session_id = session.id

                    # This is a new session, not a followup, so it will consume a scan credit
                    is_session_followup = False

                    logger.info(f"Created new session {scan_session_id} after inactivity period")
                else:
                    # Update session activity
                    crud_scan_session.update_scan_session_activity(db=db, db_session=session)

                    # Mark as a session followup (for paid tiers, this won't consume a scan credit)
                    is_session_followup = True
        else:
            # For free tier users, always update session activity but still consume credits
            crud_scan_session.update_scan_session_activity(db=db, db_session=session)
            is_session_followup = True
    else:
        # Create a new session for this scan
        session = crud_scan_session.create_scan_session(db=db, owner_id=current_user.id)
        scan_session_id = session.id

    # Check if we should consume a scan credit
    # For paid tiers (premium/expert), session followups don't consume credits
    should_consume_credit = True
    if is_session_followup and current_user.subscription_tier in ["premium", "expert"]:
        should_consume_credit = False
        logger.info(f"Session followup scan for paid tier user - not consuming scan credit")

    # Check appropriate scan limits based on scan type
    if should_consume_credit:
        if is_expert_scan:
            # Check expert scan limits
            if current_user.expert_scans_this_month >= current_user.expert_scan_allowance:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Monthly expert scan limit reached. You can still use regular scans."
                )
            # Increment the expert scan counter
            current_user.expert_scans_this_month += 1
            logger.info(f"Using expert scan. Expert scans used: {current_user.expert_scans_this_month}/{current_user.expert_scan_allowance}")
        else:
            # Check regular scan limits
            if current_user.scans_this_month >= current_user.monthly_scan_allowance:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Monthly scan limit reached. Please upgrade your plan."
                )
            # Increment the regular scan counter
            current_user.scans_this_month += 1
            logger.info(f"Using regular scan. Scans used: {current_user.scans_this_month}/{current_user.monthly_scan_allowance}")

    # Save the updated counters
    db.commit()

    # Create scan entry in database
    raw_input_payload = scan_in.dict()
    raw_input_payload["is_expert_scan"] = is_expert_scan
    raw_input_payload["is_session_followup"] = is_session_followup

    # Convert UUID to string for JSON serialization
    if raw_input_payload.get("scan_session_id"):
        raw_input_payload["scan_session_id"] = str(raw_input_payload["scan_session_id"])
    db_scan = crud.crud_scan.create_scan_entry(
        db=db,
        scan_in=scan_in,
        owner_id=current_user.id,
        raw_input_payload=raw_input_payload,
        scan_session_id=scan_session_id
    )

    # Trigger asynchronous Celery task with user's subscription tier and expert scan flag
    process_scan_task.delay(
        scan_id=db_scan.id,
        user_tier=current_user.subscription_tier,
        is_expert_scan=is_expert_scan
    )

    return db_scan


@router.get(
    "/{scan_id}",
    response_model=schemas.scan.Scan
)
def read_scan(
    *,
    db: Session = Depends(deps.get_db),
    scan_id: int,
    current_user: models.user.User = Depends(deps.get_current_user)
) -> models.scan.Scan:
    """
    Get a specific scan by ID.

    Only returns scans owned by the current user.
    """
    scan = crud.crud_scan.get_scan(db=db, scan_id=scan_id, owner_id=current_user.id)
    if not scan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scan not found"
        )
    return scan


@router.post(
    "/upload-image/",
    response_model=schemas.scan.Scan,
    status_code=status.HTTP_202_ACCEPTED
)
async def upload_image_scan(
    *,
    db: Session = Depends(deps.get_db),
    file: UploadFile = File(...),
    user_provided_context: Optional[str] = Form(None),
    use_expert_scan: bool = Form(False),
    scan_session_id: Optional[uuid.UUID] = Form(None),
    current_user: models.user.User = Depends(deps.get_current_user)
) -> models.scan.Scan:
    """
    Create a new scan request with an uploaded image.

    The scan will be processed asynchronously by a Celery worker.
    Returns the created scan object with status "pending".

    Args:
        use_expert_scan: If True and user is on expert tier, use expert scan credits
    """
    # Process image in memory to avoid file system issues on ephemeral storage
    logger.info(f"Processing uploaded image: {file.filename}, size: {file.size} bytes")
    # Check if this is an expert scan request
    is_expert_scan = use_expert_scan and current_user.subscription_tier == "expert"

    # If the reset date is more than a month old, reset the counters
    now = datetime.now(timezone.utc)
    if now - current_user.scan_counter_reset_at.replace(tzinfo=timezone.utc) > timedelta(days=30):
        current_user.scans_this_month = 0
        current_user.expert_scans_this_month = 0
        current_user.scan_counter_reset_at = now

    # Handle scan session
    is_session_followup = False

    if scan_session_id:
        # Verify the session exists and belongs to the user
        session = crud_scan_session.get_scan_session(
            db=db, session_id=scan_session_id, owner_id=current_user.id
        )
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Scan session not found or does not belong to you"
            )

        # Update session activity
        crud_scan_session.update_scan_session_activity(db=db, db_session=session)

        # Mark as a session followup (for paid tiers, this won't consume a scan credit)
        is_session_followup = True
    else:
        # Create a new session for this scan
        session = crud_scan_session.create_scan_session(db=db, owner_id=current_user.id)
        scan_session_id = session.id

    # Check if we should consume a scan credit
    # For paid tiers (premium/expert), session followups don't consume credits
    should_consume_credit = True
    if is_session_followup and current_user.subscription_tier in ["premium", "expert"]:
        should_consume_credit = False
        logger.info(f"Session followup scan for paid tier user - not consuming scan credit")

    # Check appropriate scan limits based on scan type
    if should_consume_credit:
        if is_expert_scan:
            # Check expert scan limits
            if current_user.expert_scans_this_month >= current_user.expert_scan_allowance:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Monthly expert scan limit reached. You can still use regular scans."
                )
            # Increment the expert scan counter
            current_user.expert_scans_this_month += 1
            logger.info(f"Using expert scan. Expert scans used: {current_user.expert_scans_this_month}/{current_user.expert_scan_allowance}")
        else:
            # Check regular scan limits
            if current_user.scans_this_month >= current_user.monthly_scan_allowance:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Monthly scan limit reached. Please upgrade your plan."
                )
            # Increment the regular scan counter
            current_user.scans_this_month += 1
            logger.info(f"Using regular scan. Scans used: {current_user.scans_this_month}/{current_user.monthly_scan_allowance}")

    # Save the updated counters
    db.commit()

    # Validate file size
    if file.size > settings.MAX_UPLOAD_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File size exceeds the maximum allowed size of {settings.MAX_UPLOAD_SIZE / (1024 * 1024)} MB"
        )

    # Validate file type (basic check)
    allowed_content_types = ["image/jpeg", "image/png", "image/gif", "image/webp"]
    if file.content_type not in allowed_content_types:
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail=f"Unsupported file type: {file.content_type}. Allowed types: {', '.join(allowed_content_types)}"
        )

    # Process image in memory instead of saving to disk
    try:
        # Read file content into memory
        file_content = await file.read()
        logger.info(f"File read successfully. Size: {len(file_content)} bytes")

        # Encode to base64 for storage
        base64_image = base64.b64encode(file_content).decode('utf-8')

        # Generate unique identifier for this image
        unique_filename = f"{uuid.uuid4()}.jpg"

    except Exception as e:
        logger.error(f"Error processing file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing file: {str(e)}"
        )
    finally:
        await file.close()

    # Create scan entry in database
    scan_in = schemas.scan.ScanCreate(
        input_content_type="image_path",
        user_provided_context=user_provided_context,
        scan_session_id=scan_session_id
    )

    # Include the base64 image data in the raw_input_payload
    raw_input_payload = scan_in.dict()
    raw_input_payload["base64_image"] = base64_image
    raw_input_payload["filename"] = unique_filename
    raw_input_payload["is_expert_scan"] = is_expert_scan
    raw_input_payload["is_session_followup"] = is_session_followup

    # Convert UUID to string for JSON serialization
    if raw_input_payload.get("scan_session_id"):
        raw_input_payload["scan_session_id"] = str(raw_input_payload["scan_session_id"])

    # Create the scan entry
    db_scan = crud.crud_scan.create_scan_entry(
        db=db,
        scan_in=scan_in,
        owner_id=current_user.id,
        raw_input_payload=raw_input_payload,
        scan_session_id=scan_session_id
    )

    # Trigger asynchronous Celery task with user's subscription tier and expert scan flag
    process_scan_task.delay(
        scan_id=db_scan.id,
        user_tier=current_user.subscription_tier,
        is_expert_scan=is_expert_scan
    )

    return db_scan


@router.get(
    "/",
    response_model=List[schemas.scan.Scan]
)
def read_scans(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.user.User = Depends(deps.get_current_user)
) -> List[models.scan.Scan]:
    """
    Retrieve scans.

    Returns a list of scans owned by the current user.
    """
    scans = crud.crud_scan.get_scans_by_owner(
        db=db, owner_id=current_user.id, skip=skip, limit=limit
    )
    return scans
