[tool.poetry]
name = "domeai-backend"
version = "0.1.0"
description = "Backend API for DomeAI Scam Detector application"
authors = ["DomeAI Team"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.23.2"}
pydantic = {extras = ["email"], version = "^2.4.2"}
python-dotenv = "^1.0.0"
sqlalchemy = "^2.0.23"
psycopg2-binary = "^2.9.9"
alembic = "^1.12.1"
celery = "^5.3.4"
redis = "^5.0.1"
pydantic-settings = "^2.0.3"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
pgvector = "^0.2.3"
openai = "^1.12.0"
google-auth = "^2.23.0"
google-api-python-client = "^2.108.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
black = "^23.10.1"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.6.1"
pytest-cov = "^4.1.0"
httpx = "^0.25.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ["py39"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false
