# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment variables and secrets
.env
.env.new
.env.local
.env.production
.env.staging
*.env

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/
.env/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# Docker
.docker/

# Poetry
poetry.lock

# Alembic
alembic/versions/*.py
!alembic/versions/.gitkeep

# Service account keys and sensitive files
service-account-key.json
*.json
google-credentials.json

# Database files
*.db
*.sqlite3

# Temporary files
*.tmp
*.temp
temp/
tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Redis dump
dump.rdb

# Uploads
uploads/
uploads/*
static/uploads/
media/
