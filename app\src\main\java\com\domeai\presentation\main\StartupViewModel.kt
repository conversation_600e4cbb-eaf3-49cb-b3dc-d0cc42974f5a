package com.domeai.presentation.main

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.domeai.data.preferences.AuthPreferences
import com.domeai.data.preferences.OnboardingPreferences
import com.domeai.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for handling app startup logic
 */
@HiltViewModel
class StartupViewModel @Inject constructor(
    private val onboardingPreferences: OnboardingPreferences,
    private val authRepository: AuthRepository,
    private val authPreferences: AuthPreferences
) : ViewModel() {

    private val _startDestination = MutableStateFlow<String?>(null)
    val startDestination: StateFlow<String?> = _startDestination.asStateFlow()

    init {
        determineStartDestination()
    }

    private fun determineStartDestination() {
        viewModelScope.launch {
            try {
                // Force clear any existing auth tokens to fix the authentication bypass issue
                // This is a temporary fix to ensure users are directed to the login screen
                authPreferences.clearAuthTokens()

                // Check if onboarding is completed
                onboardingPreferences.isOnboardingCompleted.collectLatest { isCompleted ->
                    _startDestination.value = if (isCompleted) {
                        // If onboarding is completed, go to login screen
                        com.domeai.ui.navigation.Screen.Login.route
                    } else {
                        // If onboarding is not completed, go to onboarding screen
                        com.domeai.ui.navigation.Screen.Onboarding.route
                    }
                }
            } catch (e: Exception) {
                // If there's an error, default to login screen
                _startDestination.value = com.domeai.ui.navigation.Screen.Login.route
            }
        }
    }
}
