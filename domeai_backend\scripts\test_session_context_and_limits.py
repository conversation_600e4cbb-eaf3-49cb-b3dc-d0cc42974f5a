#!/usr/bin/env python
"""
Script to test session context accumulation and session usage limits.
"""

import asyncio
import logging
import sys
import os
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.core.config import settings
from app.models.user import User
from app.models.scan import Scan
from app.models.scan_session import ScanSession
from app.crud import crud_user, crud_scan, crud_scan_session
from app.services.ai_service_factory import get_ai_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Test data
TEST_MESSAGES = [
    "Hi there, I'm interested in your product. Can you tell me more about it?",
    "I'd like to buy it, but I'm currently traveling. Can I pay with a cashier's check?",
    "Great! I'll send a check for $2,000 even though the item is only $800. My assistant made a mistake. Can you wire me back the difference?",
    "Please send the money via Western Union to my cousin in Nigeria. It's urgent!"
]

async def create_test_user(db: Session, email: str, tier: str = "premium") -> User:
    """Create a test user with the specified subscription tier."""
    # Check if user already exists
    user = crud_user.get_user_by_email(db, email=email)
    if user:
        logger.info(f"User {email} already exists with ID {user.id}")
        # Update subscription tier
        user.subscription_tier = tier
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    # Create new user
    user_in = {
        "email": email,
        "password": "testpassword",
        "full_name": "Test User",
        "subscription_tier": tier,
        "scans_this_month": 0,
        "scan_allowance": 50 if tier == "premium" else 100,
        "expert_scans_this_month": 0,
        "expert_scan_allowance": 0 if tier == "free" else (10 if tier == "premium" else 20)
    }
    user = crud_user.create_user(db, obj_in=user_in)
    logger.info(f"Created user {email} with ID {user.id} and tier {tier}")
    return user

async def create_scan_in_session(
    db: Session, 
    user: User, 
    session_id: Optional[uuid.UUID], 
    message: str,
    is_expert_scan: bool = False
) -> Scan:
    """Create a scan in the specified session."""
    # Create a new session if none provided
    if not session_id:
        session = crud_scan_session.create_scan_session(db=db, owner_id=user.id)
        session_id = session.id
        logger.info(f"Created new session {session_id}")
    
    # Create scan
    scan = Scan(
        owner_id=user.id,
        status="pending",
        input_content_type="text",
        input_text=message,
        scan_session_id=session_id,
        raw_input_payload={"is_expert_scan": is_expert_scan}
    )
    
    db.add(scan)
    db.commit()
    db.refresh(scan)
    logger.info(f"Created scan {scan.id} in session {session_id}")
    
    return scan

async def process_scan(db: Session, scan: Scan, user: User) -> Dict[str, Any]:
    """Process a scan with the AI service."""
    # Update scan status to "processing"
    scan.status = "processing"
    db.add(scan)
    db.commit()
    db.refresh(scan)
    
    # Get the AI service based on user's tier
    ai_service = get_ai_service(user_tier=user.subscription_tier)
    
    # Check if this scan is part of a session
    accumulated_session_context = None
    if scan.scan_session_id:
        # Get previous scans in this session
        previous_scans = crud_scan_session.get_previous_scans_in_session(
            db=db,
            session_id=scan.scan_session_id,
            current_scan_id=scan.id
        )
        
        logger.info(f"Found {len(previous_scans)} previous scans in session {scan.scan_session_id}")
        
        # Compile accumulated session context
        if previous_scans:
            context_parts = []
            for i, prev_scan in enumerate(previous_scans):
                # Extract text from previous scan
                prev_text = None
                if prev_scan.analysis_result and "extracted_text" in prev_scan.analysis_result:
                    prev_text = prev_scan.analysis_result["extracted_text"]
                elif prev_scan.input_text:
                    prev_text = prev_scan.input_text
                
                if prev_text:
                    context_parts.append(f"[Scan {i+1}]: {prev_text}")
            
            if context_parts:
                accumulated_session_context = "\n---\n".join(context_parts)
                logger.info(f"Compiled accumulated session context ({len(context_parts)} previous scans)")
    
    # Simulate multimodal analysis
    multimodal_data = {
        "extracted_text": scan.input_text,
        "image_description": None,
        "platform_identified": "Test Platform"
    }
    
    # Generate embedding
    query_embedding = await ai_service.get_text_embedding(text=scan.input_text)
    
    # Perform RAG analysis
    final_analysis = await ai_service.perform_scam_analysis_with_rag(
        query_text=scan.input_text,
        original_image_description=multimodal_data.get("image_description"),
        original_platform_identified=multimodal_data.get("platform_identified"),
        query_embedding=query_embedding,
        accumulated_session_context=accumulated_session_context,
        db=db
    )
    
    # Update scan with result
    analysis_result = {
        "extracted_text": scan.input_text,
        "platform_identified": "Test Platform",
        "risk_score": final_analysis.risk_score,
        "detected_red_flags": final_analysis.detected_red_flags,
        "explanation": final_analysis.explanation,
        "recommendations": final_analysis.recommendations,
        "confidence_level": final_analysis.confidence_level,
        "model_used": final_analysis.model_used,
        "overall_session_assessment": final_analysis.overall_session_assessment
    }
    
    scan.analysis_result = analysis_result
    scan.status = "completed"
    db.add(scan)
    db.commit()
    db.refresh(scan)
    
    logger.info(f"Processed scan {scan.id}")
    logger.info(f"Risk score: {final_analysis.risk_score}")
    logger.info(f"Detected red flags: {final_analysis.detected_red_flags}")
    
    if final_analysis.overall_session_assessment:
        logger.info(f"Overall session assessment: {final_analysis.overall_session_assessment}")
    
    return analysis_result

async def test_session_context_accumulation():
    """Test session context accumulation with a series of scans."""
    logger.info("Testing session context accumulation")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create a test user with premium tier
        user = await create_test_user(db, email="<EMAIL>", tier="premium")
        
        # Create a new session
        session = crud_scan_session.create_scan_session(db=db, owner_id=user.id)
        logger.info(f"Created session {session.id}")
        
        # Process a series of scans in the session
        session_id = session.id
        previous_scan = None
        
        for i, message in enumerate(TEST_MESSAGES):
            logger.info(f"\n--- Processing scan {i+1} with message: {message} ---\n")
            
            # Create and process scan
            scan = await create_scan_in_session(db, user, session_id, message)
            result = await process_scan(db, scan, user)
            
            # Log the results
            logger.info(f"Scan {i+1} results:")
            logger.info(f"Risk score: {result['risk_score']}")
            logger.info(f"Explanation: {result['explanation'][:200]}...")
            
            if result.get('overall_session_assessment'):
                logger.info(f"Overall session assessment: {result['overall_session_assessment'][:200]}...")
            
            previous_scan = scan
    
    finally:
        db.close()

async def test_session_usage_limits():
    """Test session usage limits for premium and expert tiers."""
    logger.info("Testing session usage limits")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create test users with premium and expert tiers
        premium_user = await create_test_user(db, email="<EMAIL>", tier="premium")
        expert_user = await create_test_user(db, email="<EMAIL>", tier="expert")
        
        # Test premium tier limits
        logger.info("\n--- Testing premium tier session limits ---\n")
        premium_session = crud_scan_session.create_scan_session(db=db, owner_id=premium_user.id)
        premium_session_id = premium_session.id
        
        # Create scans up to the limit
        for i in range(settings.SESSION_MAX_INPUTS_PREMIUM + 2):
            logger.info(f"Creating scan {i+1} for premium user")
            scan = await create_scan_in_session(
                db, premium_user, premium_session_id, f"Premium test message {i+1}"
            )
            
            # Check if a new session was created after reaching the limit
            if i >= settings.SESSION_MAX_INPUTS_PREMIUM:
                logger.info(f"Scan {i+1} session ID: {scan.scan_session_id}")
                if scan.scan_session_id != premium_session_id:
                    logger.info(f"New session created after reaching premium tier limit of {settings.SESSION_MAX_INPUTS_PREMIUM}")
                    premium_session_id = scan.scan_session_id
        
        # Test expert tier limits
        logger.info("\n--- Testing expert tier session limits ---\n")
        expert_session = crud_scan_session.create_scan_session(db=db, owner_id=expert_user.id)
        expert_session_id = expert_session.id
        
        # Create scans up to the limit
        for i in range(settings.SESSION_MAX_INPUTS_EXPERT + 2):
            logger.info(f"Creating scan {i+1} for expert user")
            scan = await create_scan_in_session(
                db, expert_user, expert_session_id, f"Expert test message {i+1}"
            )
            
            # Check if a new session was created after reaching the limit
            if i >= settings.SESSION_MAX_INPUTS_EXPERT:
                logger.info(f"Scan {i+1} session ID: {scan.scan_session_id}")
                if scan.scan_session_id != expert_session_id:
                    logger.info(f"New session created after reaching expert tier limit of {settings.SESSION_MAX_INPUTS_EXPERT}")
                    expert_session_id = scan.scan_session_id
    
    finally:
        db.close()

async def test_session_inactivity_window():
    """Test session inactivity window."""
    logger.info("Testing session inactivity window")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create a test user with premium tier
        user = await create_test_user(db, email="<EMAIL>", tier="premium")
        
        # Create a new session
        session = crud_scan_session.create_scan_session(db=db, owner_id=user.id)
        session_id = session.id
        logger.info(f"Created session {session_id}")
        
        # Create first scan
        scan1 = await create_scan_in_session(db, user, session_id, "First message")
        
        # Manually update the session's last_activity_at to be older than the inactivity window
        inactivity_window = timedelta(hours=settings.SESSION_ACTIVITY_WINDOW_HOURS)
        old_time = datetime.now() - inactivity_window - timedelta(minutes=30)
        
        # Update the session directly in the database
        db.query(ScanSession).filter(ScanSession.id == session_id).update(
            {"last_activity_at": old_time}
        )
        db.commit()
        
        # Refresh the session
        session = crud_scan_session.get_scan_session(db=db, session_id=session_id, owner_id=user.id)
        logger.info(f"Updated session {session_id} last_activity_at to {session.last_activity_at}")
        
        # Create second scan with the same session ID
        scan2 = await create_scan_in_session(db, user, session_id, "Second message after inactivity")
        
        # Check if a new session was created
        logger.info(f"Second scan session ID: {scan2.scan_session_id}")
        if scan2.scan_session_id != session_id:
            logger.info(f"New session created after inactivity period of {settings.SESSION_ACTIVITY_WINDOW_HOURS} hours")
    
    finally:
        db.close()

async def main():
    """Run all tests."""
    logger.info("Starting session context and limits tests")
    
    await test_session_context_accumulation()
    logger.info("\n" + "="*80 + "\n")
    
    await test_session_usage_limits()
    logger.info("\n" + "="*80 + "\n")
    
    await test_session_inactivity_window()
    logger.info("\n" + "="*80 + "\n")
    
    logger.info("All tests completed")

if __name__ == "__main__":
    asyncio.run(main())
