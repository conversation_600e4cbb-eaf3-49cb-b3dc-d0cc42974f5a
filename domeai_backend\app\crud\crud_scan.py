import uuid
from typing import Dict, List, Optional, Any

from sqlalchemy.orm import Session

from app import models, schemas


def create_scan_entry(
    db: Session, *, scan_in: schemas.scan.ScanCreate, owner_id: int, raw_input_payload: Dict[str, Any],
    scan_session_id: Optional[uuid.UUID] = None
) -> models.scan.Scan:
    """
    Create a new scan record in the database with initial status "pending".

    Args:
        db: Database session
        scan_in: Scan data from request
        owner_id: ID of the user who owns this scan
        raw_input_payload: Original request data for debugging/reprocessing
        scan_session_id: Optional ID of the scan session this scan belongs to

    Returns:
        The created scan object
    """
    db_scan = models.scan.Scan(
        owner_id=owner_id,
        status="pending",
        input_text=scan_in.input_text,
        input_url=scan_in.input_url,
        input_content_type=scan_in.input_content_type,
        user_provided_context=scan_in.user_provided_context,
        raw_input_payload=raw_input_payload,
        scan_session_id=scan_session_id or scan_in.scan_session_id
    )
    db.add(db_scan)
    db.commit()
    db.refresh(db_scan)
    return db_scan


def get_scan(db: Session, *, scan_id: int, owner_id: int) -> Optional[models.scan.Scan]:
    """
    Retrieve a specific scan owned by the user.

    Args:
        db: Database session
        scan_id: ID of the scan to retrieve
        owner_id: ID of the user who owns this scan

    Returns:
        The scan object if found, None otherwise
    """
    return db.query(models.scan.Scan).filter(
        models.scan.Scan.id == scan_id,
        models.scan.Scan.owner_id == owner_id
    ).first()


def get_scans_by_owner(
    db: Session, *, owner_id: int, skip: int = 0, limit: int = 100
) -> List[models.scan.Scan]:
    """
    Retrieve a list of scans for a user.

    Args:
        db: Database session
        owner_id: ID of the user who owns the scans
        skip: Number of records to skip (for pagination)
        limit: Maximum number of records to return

    Returns:
        List of scan objects
    """
    return db.query(models.scan.Scan).filter(
        models.scan.Scan.owner_id == owner_id
    ).order_by(models.scan.Scan.created_at.desc()).offset(skip).limit(limit).all()


def update_scan_status(
    db: Session, *, db_scan: models.scan.Scan, status: str, error_message: Optional[str] = None
) -> models.scan.Scan:
    """
    Update the status of a scan (and optionally an error message).

    Args:
        db: Database session
        db_scan: Scan object to update
        status: New status value
        error_message: Optional error message if status is "failed"

    Returns:
        The updated scan object
    """
    db_scan.status = status
    if error_message:
        db_scan.error_message = error_message
    db.add(db_scan)
    db.commit()
    db.refresh(db_scan)
    return db_scan


def update_scan_result(
    db: Session, *, db_scan: models.scan.Scan, analysis_result: schemas.scan.ScanResultData
) -> models.scan.Scan:
    """
    Update the scan with the AI analysis result and set status to "completed".

    Args:
        db: Database session
        db_scan: Scan object to update
        analysis_result: AI analysis result data

    Returns:
        The updated scan object
    """
    db_scan.status = "completed"
    db_scan.analysis_result = analysis_result.dict()
    db.add(db_scan)
    db.commit()
    db.refresh(db_scan)
    return db_scan
