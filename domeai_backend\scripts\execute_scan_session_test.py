"""
<PERSON><PERSON><PERSON> to execute the core AI functionality of the scan session test.
"""

import asyncio
import base64
import json
import logging
import os
import sys
import time
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

import requests
from sqlalchemy.orm import Session

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan import Scan
from app.services.ai_service_factory import get_ai_service
from app.core.config import settings
from app import crud

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Reddit Conversational Scam Scenario Images
IMAGE_URLS = [
    "https://drive.google.com/file/d/1Rbdq60DXO8Fvvz9XHJnUWkEw29GFXzeC/view?usp=sharing",
    "https://drive.google.com/file/d/1-niQevQraY1PJzu9m79-LM-LwRGUUtxE/view?usp=sharing",
    "https://drive.google.com/file/d/1dQuE-aFYIHKHg6ZV6EoRTQw3QPXTKijX/view?usp=sharing",
    "https://drive.google.com/file/d/1fxdt5H_kfGHO2XaHQSMx7NfNFJoYMDtV/view?usp=sharing"
]

# User Context for the first scan
USER_CONTEXT = """They almost got me. My intuition is almost on point. I was so close too. Holy Shit. My intuition is "almost" on point. I finally got one. So I'm on a "dating" app. I matched with a girl in my area. She seemed a little too good to be true. So I of course proceeded with caution. When she asked me personal details, I didn't tell her much. My big mistake was to add her on my personal instagram. Onto the good part. She started getting sexual immediately. I told her I don't send pics until I meet the person. This upset her because she had already sent pics over dm. So I sent her a couple fully clothed pics of me in the mirror. This upset her, she wanted a "limp dick" picture. Red flag, what girl wants to see it limp. 😂😂 After she asked for my number and I turned that down, she turned down my offer to video call. Here's a few of the chats. I warned the people on my Instagram about a potential fake dick pic incoming. Will update if this gets more interesting."""

def download_image_from_gdrive(url: str, output_dir: str = "temp_images") -> str:
    """
    Download an image from Google Drive.

    Args:
        url: Google Drive URL
        output_dir: Directory to save the image

    Returns:
        Path to the downloaded image
    """
    logger.info(f"Downloading image from {url}")

    # Extract file ID from the URL
    file_id = url.split("/d/")[1].split("/view")[0]

    # Create the output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Construct the download URL
    download_url = f"https://drive.google.com/uc?id={file_id}"

    # Generate a unique filename
    filename = f"{output_dir}/image_{file_id}.jpg"

    # Download the file
    response = requests.get(download_url)
    with open(filename, "wb") as f:
        f.write(response.content)

    logger.info(f"Downloaded image to {filename}")
    return filename

def create_premium_user(db: Session) -> User:
    """
    Create a premium tier test user.

    Args:
        db: Database session

    Returns:
        The created user
    """
    unique_email = f"test_premium_user_{int(time.time())}@example.com"
    test_user = User(
        email=unique_email,
        hashed_password="hashed_password",
        subscription_tier="premium",
        monthly_scan_allowance=100,
        scans_this_month=0,
        scan_counter_reset_at=datetime.now(timezone.utc)
    )
    db.add(test_user)
    db.commit()
    db.refresh(test_user)
    logger.info(f"Created premium test user with ID {test_user.id}")
    return test_user

async def create_scan_in_session(
    db: Session,
    user: User,
    session_id: uuid.UUID,
    image_path: str,
    user_provided_context: Optional[str] = None,
    is_session_followup: bool = True
) -> Scan:
    """
    Create a scan in the specified session.

    Args:
        db: Database session
        user: The user who owns the scan
        session_id: ID of the scan session
        image_path: Path to the image file
        user_provided_context: Optional context provided by the user
        is_session_followup: Whether this is a follow-up scan in the session

    Returns:
        The created scan
    """
    # Create scan
    scan = Scan(
        owner_id=user.id,
        status="pending",
        input_content_type="image_path",
        user_provided_context=user_provided_context,
        raw_input_payload={"file_path": image_path, "is_session_followup": is_session_followup},
        scan_session_id=session_id
    )

    db.add(scan)
    db.commit()
    db.refresh(scan)
    logger.info(f"Created scan {scan.id} in session {session_id}")

    return scan

async def process_scan_with_real_ai(
    db: Session,
    scan: Scan,
    user: User,
    previous_scans: List[Scan] = None
) -> Dict[str, Any]:
    """
    Process a scan with the real AI service.

    Args:
        db: Database session
        scan: The scan to process
        user: The user who owns the scan
        previous_scans: List of previous scans in the session

    Returns:
        The analysis result
    """
    logger.info(f"Processing scan {scan.id} with real AI service")

    # Update scan status to "processing"
    scan.status = "processing"
    db.add(scan)
    db.commit()
    db.refresh(scan)

    # Get the AI service based on user's tier
    ai_service = get_ai_service(user_tier=user.subscription_tier)

    # Compile accumulated session context
    accumulated_session_context = None
    if previous_scans:
        context_parts = []

        for i, prev_scan in enumerate(previous_scans):
            # Extract text from previous scan
            prev_text = None
            if prev_scan.analysis_result and "extracted_text" in prev_scan.analysis_result:
                prev_text = prev_scan.analysis_result["extracted_text"]
            elif prev_scan.input_text:
                prev_text = prev_scan.input_text

            if prev_text:
                # Add scan number and text to context
                context_parts.append(f"[Scan {i+1}]: {prev_text}")

                # Add platform information if available
                if prev_scan.analysis_result and "platform_identified" in prev_scan.analysis_result:
                    platform = prev_scan.analysis_result["platform_identified"]
                    if platform:
                        context_parts[-1] += f" (Platform: {platform})"

        # Join all parts into a single context string
        if context_parts:
            accumulated_session_context = "\n---\n".join(context_parts)
            logger.info(f"Compiled accumulated session context ({len(context_parts)} previous scans)")
            logger.info(f"Accumulated session context: {accumulated_session_context}")

    # Process the scan with the AI service
    try:
        # Get the file path from the raw_input_payload
        file_path = scan.raw_input_payload.get("file_path")
        if not file_path:
            raise ValueError("File path not found in raw_input_payload")

        # Get multimodal analysis
        logger.info(f"Getting multimodal analysis for image at {file_path}")
        multimodal_result = await ai_service.get_multimodal_analysis(
            image_path=file_path
        )

        # Extract text and get embedding
        extracted_text = multimodal_result.get("extracted_text", "")
        logger.info(f"Getting text embedding for extracted text: {extracted_text[:100]}...")
        embedding = await ai_service.get_text_embedding(text=extracted_text)

        # Perform RAG analysis
        logger.info(f"Performing RAG analysis")
        rag_result = await ai_service.perform_scam_analysis_with_rag(
            query_text=extracted_text,
            query_embedding=embedding,
            original_image_description=multimodal_result.get("image_description", ""),
            original_platform_identified=multimodal_result.get("platform_identified", ""),
            accumulated_session_context=accumulated_session_context,
            db=db
        )

        # Convert ScanResultData to dict
        rag_result_dict = rag_result.__dict__

        # Combine results
        analysis_result = {
            **multimodal_result,
            **rag_result_dict
        }

        # Update scan with result
        scan.status = "completed"
        scan.analysis_result = analysis_result
        db.add(scan)
        db.commit()
        db.refresh(scan)

        logger.info(f"Completed processing of scan {scan.id}")
        logger.info(f"Analysis result: Risk Score: {analysis_result.get('risk_score', 'N/A')}")
        logger.info(f"Detected Red Flags: {analysis_result.get('detected_red_flags', [])}")
        logger.info(f"Explanation: {analysis_result.get('explanation', '')[:200]}...")
        logger.info(f"Recommendations: {analysis_result.get('recommendations', '')[:200]}...")
        logger.info(f"Confidence Level: {analysis_result.get('confidence_level', '')}")

        # Log overall session assessment if available
        if "overall_session_assessment" in rag_result_dict and rag_result_dict["overall_session_assessment"]:
            logger.info(f"Overall session assessment: {rag_result_dict['overall_session_assessment']}")
        else:
            logger.info("No overall session assessment provided")

        # Save full results to a JSON file for detailed analysis
        result_file = f"scan_{scan.id}_result.json"
        with open(result_file, "w") as f:
            json.dump(analysis_result, f, indent=2)
        logger.info(f"Full results saved to {result_file}")

        return analysis_result

    except Exception as e:
        logger.error(f"Error processing scan: {str(e)}", exc_info=True)

        # Update scan with error
        scan.status = "error"
        scan.error_message = str(e)
        db.add(scan)
        db.commit()
        db.refresh(scan)

        return {"error": str(e)}

async def execute_core_test():
    """
    Execute the core AI functionality of the scan session test.
    """
    logger.info("Executing core AI functionality of the scan session test")

    # Create a database session
    db = SessionLocal()
    try:
        # 1. Setup
        # Create a premium tier test user
        test_user = create_premium_user(db)

        # Download the test images
        image_paths = []
        for url in IMAGE_URLS:
            image_path = download_image_from_gdrive(url, "temp_images")
            image_paths.append(image_path)

        # Create a scan session
        session = crud.crud_scan_session.create_scan_session(db=db, owner_id=test_user.id)
        session_id = session.id
        logger.info(f"Created scan session with ID {session_id}")

        # Track scan credits before
        scans_before = test_user.scans_this_month
        logger.info(f"Scans before: {scans_before}")

        # 2. Process images sequentially
        all_scans = []

        # Process Image 1
        logger.info("\n=== Processing Image 1 ===\n")
        scan1 = await create_scan_in_session(
            db,
            test_user,
            session_id,
            image_paths[0],
            user_provided_context=USER_CONTEXT,
            is_session_followup=False  # First scan is not a follow-up
        )

        # Update session activity
        crud.crud_scan_session.update_scan_session_activity(db=db, db_session=session)

        # Update user scan count for first scan
        test_user.scans_this_month += 1
        db.commit()
        db.refresh(test_user)

        # Process the scan
        result1 = await process_scan_with_real_ai(db, scan1, test_user)
        all_scans.append(scan1)

        # Verify scan credit consumption for first scan
        db.refresh(test_user)
        logger.info(f"Scans after Image 1: {test_user.scans_this_month}")
        logger.info(f"Scan credit consumed: {test_user.scans_this_month - scans_before}")

        # Process Image 2
        logger.info("\n=== Processing Image 2 ===\n")
        scan2 = await create_scan_in_session(
            db,
            test_user,
            session_id,
            image_paths[1],
            is_session_followup=True  # This is a follow-up
        )

        # Update session activity
        crud.crud_scan_session.update_scan_session_activity(db=db, db_session=session)

        # Process the scan with previous scans context
        result2 = await process_scan_with_real_ai(db, scan2, test_user, all_scans)
        all_scans.append(scan2)

        # Verify scan credit consumption (should not increase for follow-ups)
        db.refresh(test_user)
        logger.info(f"Scans after Image 2: {test_user.scans_this_month}")
        logger.info(f"Additional scan credits consumed: {test_user.scans_this_month - (scans_before + 1)}")

        # Process Image 3
        logger.info("\n=== Processing Image 3 ===\n")
        scan3 = await create_scan_in_session(
            db,
            test_user,
            session_id,
            image_paths[2],
            is_session_followup=True  # This is a follow-up
        )

        # Update session activity
        crud.crud_scan_session.update_scan_session_activity(db=db, db_session=session)

        # Process the scan with previous scans context
        result3 = await process_scan_with_real_ai(db, scan3, test_user, all_scans)
        all_scans.append(scan3)

        # Verify scan credit consumption (should not increase for follow-ups)
        db.refresh(test_user)
        logger.info(f"Scans after Image 3: {test_user.scans_this_month}")
        logger.info(f"Additional scan credits consumed: {test_user.scans_this_month - (scans_before + 1)}")

        # Process Image 4
        logger.info("\n=== Processing Image 4 ===\n")
        scan4 = await create_scan_in_session(
            db,
            test_user,
            session_id,
            image_paths[3],
            is_session_followup=True  # This is a follow-up
        )

        # Update session activity
        crud.crud_scan_session.update_scan_session_activity(db=db, db_session=session)

        # Process the scan with previous scans context
        result4 = await process_scan_with_real_ai(db, scan4, test_user, all_scans)

        # Verify scan credit consumption (should not increase for follow-ups)
        db.refresh(test_user)
        logger.info(f"Scans after Image 4: {test_user.scans_this_month}")
        logger.info(f"Additional scan credits consumed: {test_user.scans_this_month - (scans_before + 1)}")

        # 3. Verify final scan credit consumption (should be just 1 for the entire session)
        logger.info(f"Final scan count: {test_user.scans_this_month}")
        logger.info(f"Total scan credits consumed: {test_user.scans_this_month - scans_before}")

        # Retrieve the final session details
        final_session = crud.crud_scan_session.get_scan_session(
            db=db, session_id=session_id, owner_id=test_user.id
        )
        logger.info(f"Session has {len(final_session.scans)} scans")

        logger.info("Core AI functionality test completed successfully")

    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(execute_core_test())
