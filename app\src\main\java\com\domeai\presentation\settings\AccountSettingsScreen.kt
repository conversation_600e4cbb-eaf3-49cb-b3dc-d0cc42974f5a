package com.domeai.presentation.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Phone
import androidx.compose.material.icons.filled.Security
import androidx.compose.material.icons.filled.Subscriptions
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.domeai.R
import androidx.hilt.navigation.compose.hiltViewModel
import com.domeai.ui.composables.VerticalSpacer

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AccountSettingsScreen(
    onNavigateBack: () -> Unit,
    onNavigateToChangePassword: () -> Unit,
    onNavigateToSubscriptionDetails: () -> Unit,
    onNavigateToPrivacySecurity: () -> Unit,
    viewModel: AccountSettingsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    var showImagePickerDialog by remember { mutableStateOf(false) }

    // Handle UI events
    LaunchedEffect(key1 = true) {
        viewModel.uiEvent.collect { event ->
            when (event) {
                is AccountSettingsUiEvent.ShowSnackbar -> {
                    snackbarHostState.showSnackbar(event.message)
                }
                is AccountSettingsUiEvent.NavigateBack -> onNavigateBack()
                is AccountSettingsUiEvent.NavigateToChangePassword -> onNavigateToChangePassword()
                is AccountSettingsUiEvent.NavigateToSubscriptionDetails -> onNavigateToSubscriptionDetails()
                is AccountSettingsUiEvent.NavigateToPrivacySecurity -> onNavigateToPrivacySecurity()
                is AccountSettingsUiEvent.OpenImagePicker -> {
                    // Show the image picker dialog
                    showImagePickerDialog = true
                }
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Account Settings") },
                navigationIcon = {
                    IconButton(onClick = { viewModel.sendAction(AccountSettingsUiAction.NavigateBack) }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Profile Section
            ProfileSection(
                name = uiState.name,
                email = uiState.email,
                profilePictureUri = uiState.profilePictureUri,
                onEditProfile = { viewModel.sendAction(AccountSettingsUiAction.EditProfile) },
                onChangeProfilePicture = { viewModel.sendAction(AccountSettingsUiAction.OpenImagePicker) }
            )

            // Account Information Section
            AccountInformationSection(
                name = uiState.name,
                email = uiState.email,
                phone = uiState.phone,
                onNameChange = { viewModel.sendAction(AccountSettingsUiAction.UpdateName(it)) },
                onPhoneChange = { viewModel.sendAction(AccountSettingsUiAction.UpdatePhone(it)) },
                onSaveChanges = { viewModel.sendAction(AccountSettingsUiAction.SaveProfileChanges) },
                isEditing = uiState.isEditingProfile
            )

            // Security Section
            SecuritySection(
                onChangePassword = { viewModel.sendAction(AccountSettingsUiAction.NavigateToChangePassword) },
                onPrivacySecurity = { viewModel.sendAction(AccountSettingsUiAction.NavigateToPrivacySecurity) }
            )

            // Notification Preferences
            NotificationSection(
                emailNotifications = uiState.emailNotificationsEnabled,
                pushNotifications = uiState.pushNotificationsEnabled,
                onEmailNotificationsChange = { viewModel.sendAction(AccountSettingsUiAction.ToggleEmailNotifications(it)) },
                onPushNotificationsChange = { viewModel.sendAction(AccountSettingsUiAction.TogglePushNotifications(it)) }
            )

            // Account Actions
            AccountActionsSection(
                onDeleteAccount = { viewModel.sendAction(AccountSettingsUiAction.DeleteAccount) }
            )
        }
    }

    // Show image picker dialog if needed
    if (showImagePickerDialog) {
        com.domeai.ui.composables.ImagePickerDialog(
            onDismiss = { showImagePickerDialog = false },
            onCameraSelected = {
                // In a real app, this would launch the camera
                // For now, we'll simulate selecting an image
                viewModel.sendAction(AccountSettingsUiAction.UpdateProfilePicture("camera_image_uri"))
            },
            onGallerySelected = {
                // In a real app, this would launch the gallery
                // For now, we'll simulate selecting an image
                viewModel.sendAction(AccountSettingsUiAction.UpdateProfilePicture("gallery_image_uri"))
            }
        )
    }
}

@Composable
fun ProfileSection(
    name: String,
    email: String,
    profilePictureUri: String = "",
    onEditProfile: () -> Unit,
    onChangeProfilePicture: () -> Unit = {}
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Profile Picture with Upload Functionality
            Box(
                modifier = Modifier
                    .size(100.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f))
                    .border(2.dp, MaterialTheme.colorScheme.primary, CircleShape)
                    .clickable { onChangeProfilePicture() },
                contentAlignment = Alignment.Center
            ) {
                // Show either the user's profile picture or a default icon
                if (profilePictureUri.isNotEmpty()) {
                    androidx.compose.foundation.Image(
                        painter = androidx.compose.ui.res.painterResource(id = R.drawable.ic_launcher_foreground), // Placeholder - would use Coil to load from URI
                        contentDescription = "Profile Picture",
                        modifier = Modifier.size(100.dp),
                        contentScale = androidx.compose.ui.layout.ContentScale.Crop
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.AccountCircle,
                        contentDescription = "Profile Picture",
                        modifier = Modifier.size(60.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                }

                // Camera icon overlay to indicate it's clickable
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .size(30.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.primary)
                        .padding(4.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "Change Profile Picture",
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.onPrimary
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Name
            Text(
                text = name,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )

            // Email
            Text(
                text = email,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Edit Profile Button
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(onClick = onEditProfile)
                    .padding(8.dp),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Edit,
                    contentDescription = "Edit Profile",
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.size(8.dp))
                Text(
                    text = "Edit Profile",
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Composable
fun AccountInformationSection(
    name: String,
    email: String,
    phone: String,
    onNameChange: (String) -> Unit,
    onPhoneChange: (String) -> Unit,
    onSaveChanges: () -> Unit,
    isEditing: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Account Information",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // If editing, show editable fields, otherwise show read-only information
            if (isEditing) {
                EditableAccountInfo(
                    name = name,
                    email = email,
                    phone = phone,
                    onNameChange = onNameChange,
                    onPhoneChange = onPhoneChange,
                    onSaveChanges = onSaveChanges
                )
            } else {
                AccountInfoItem(
                    icon = Icons.Default.Person,
                    label = "Name",
                    value = name
                )

                HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))

                AccountInfoItem(
                    icon = Icons.Default.Email,
                    label = "Email",
                    value = email
                )

                HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))

                AccountInfoItem(
                    icon = Icons.Default.Phone,
                    label = "Phone",
                    value = phone.ifEmpty { "Not provided" }
                )
            }
        }
    }
}

@Composable
fun EditableAccountInfo(
    name: String,
    email: String,
    phone: String,
    onNameChange: (String) -> Unit,
    onPhoneChange: (String) -> Unit,
    onSaveChanges: () -> Unit
) {
    // Name field
    androidx.compose.material3.OutlinedTextField(
        value = name,
        onValueChange = onNameChange,
        label = { Text("Name") },
        modifier = Modifier.fillMaxWidth(),
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = "Name"
            )
        }
    )

    Spacer(modifier = Modifier.height(16.dp))

    // Email field (read-only)
    androidx.compose.material3.OutlinedTextField(
        value = email,
        onValueChange = { },
        label = { Text("Email") },
        modifier = Modifier.fillMaxWidth(),
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Email,
                contentDescription = "Email"
            )
        },
        readOnly = true,
        enabled = false
    )

    Spacer(modifier = Modifier.height(16.dp))

    // Phone field
    androidx.compose.material3.OutlinedTextField(
        value = phone,
        onValueChange = onPhoneChange,
        label = { Text("Phone") },
        modifier = Modifier.fillMaxWidth(),
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Phone,
                contentDescription = "Phone"
            )
        }
    )

    Spacer(modifier = Modifier.height(16.dp))

    // Save button
    androidx.compose.material3.Button(
        onClick = onSaveChanges,
        modifier = Modifier.fillMaxWidth()
    ) {
        Text("Save Changes")
    }
}

@Composable
fun AccountInfoItem(
    icon: ImageVector,
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.size(16.dp))

        Column {
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )

            Text(
                text = value,
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@Composable
fun SecuritySection(
    onChangePassword: () -> Unit,
    onPrivacySecurity: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Security",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Change Password
            SettingsItem(
                icon = Icons.Default.Lock,
                title = "Change Password",
                description = "Update your password",
                onClick = onChangePassword
            )

            HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))

            // Privacy & Security
            SettingsItem(
                icon = Icons.Default.Security,
                title = "Privacy & Security",
                description = "Manage your privacy settings",
                onClick = onPrivacySecurity
            )
        }
    }
}

@Composable
fun SubscriptionSection(
    subscriptionType: String,
    expiryDate: String,
    onManageSubscription: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Subscription",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Subscription Info
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Subscriptions,
                    contentDescription = "Subscription",
                    tint = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.size(16.dp))

                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Current Plan",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )

                    Text(
                        text = subscriptionType,
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Bold
                    )

                    if (expiryDate.isNotEmpty()) {
                        Text(
                            text = "Expires on $expiryDate",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Manage Subscription Button
            androidx.compose.material3.OutlinedButton(
                onClick = onManageSubscription,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Manage Subscription")
            }
        }
    }
}

@Composable
fun NotificationSection(
    emailNotifications: Boolean,
    pushNotifications: Boolean,
    onEmailNotificationsChange: (Boolean) -> Unit,
    onPushNotificationsChange: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Notification Preferences",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Email Notifications
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Email,
                    contentDescription = "Email Notifications",
                    tint = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.size(16.dp))

                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Email Notifications",
                        style = MaterialTheme.typography.bodyLarge
                    )

                    Text(
                        text = "Receive notifications via email",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }

                androidx.compose.material3.Switch(
                    checked = emailNotifications,
                    onCheckedChange = onEmailNotificationsChange
                )
            }

            HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))

            // Push Notifications
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Notifications,
                    contentDescription = "Push Notifications",
                    tint = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.size(16.dp))

                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Push Notifications",
                        style = MaterialTheme.typography.bodyLarge
                    )

                    Text(
                        text = "Receive notifications on your device",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }

                androidx.compose.material3.Switch(
                    checked = pushNotifications,
                    onCheckedChange = onPushNotificationsChange
                )
            }
        }
    }
}

@Composable
fun AccountActionsSection(
    onDeleteAccount: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Account Actions",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Delete Account Button
            androidx.compose.material3.OutlinedButton(
                onClick = onDeleteAccount,
                modifier = Modifier.fillMaxWidth(),
                colors = androidx.compose.material3.ButtonDefaults.outlinedButtonColors(
                    contentColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text("Delete Account")
            }
        }
    }
}

@Composable
fun SettingsItem(
    icon: ImageVector,
    title: String,
    description: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.size(16.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge
            )

            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }

        Icon(
            imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
            contentDescription = "Navigate",
            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
        )
    }
}
