"""
Configuration for Google Play Billing integration.
"""
from typing import Dict, Any

# Mapping of Google Play SKUs to internal subscription tiers
GOOGLE_PLAY_SKU_TO_TIER_MAP = {
    # Premium tier
    "domeai_placeholder_premium_monthly": "premium",
    "domeai_placeholder_premium_yearly": "premium",
    # Expert tier
    "domeai_placeholder_expert_monthly": "expert",
    "domeai_placeholder_expert_yearly": "expert",
    # Free tier (if managed as a $0 product)
    "domeai_placeholder_free": "free"
}

# Allowances for each subscription tier
TIER_ALLOWANCES = {
    "free": {"monthly_scans": 5, "expert_scans": 0},
    "premium": {"monthly_scans": 100, "expert_scans": 0},
    "expert": {"monthly_scans": 100, "expert_scans": 20},
}

# Google Play subscription notification types
SUBSCRIPTION_NOTIFICATION_TYPES = {
    1: "SUBSCRIPTION_RECOVERED",
    2: "SUBSCRIPTION_RENEWED",
    3: "SUBSCRIPTION_CANCELED",
    4: "SUBSCRIPTION_PURCHASED",
    5: "SUBSCRIPTION_ON_HOLD",
    6: "SUBSCRIPTION_IN_GRACE_PERIOD",
    7: "SUBSCRIPTION_RESTARTED",
    8: "SUBSCRIPTION_PRICE_CHANGE_CONFIRMED",
    9: "SUBSCRIPTION_DEFERRED",
    10: "SUBSCRIPTION_PAUSED",
    11: "SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED",
    12: "SUBSCRIPTION_REVOKED",
    13: "SUBSCRIPTION_EXPIRED"
}

# Mock Google Play API responses for testing
def get_mock_subscription_data(product_id: str, purchase_token: str) -> Dict[str, Any]:
    """
    Get a mock Google Play API response for a subscription.
    
    Args:
        product_id: The product ID (SKU)
        purchase_token: The purchase token
        
    Returns:
        A mock Google Play API response
    """
    import time
    
    # Current time in milliseconds
    current_time_millis = int(time.time() * 1000)
    
    # Mock response for a subscription
    return {
        "kind": "androidpublisher#subscriptionPurchaseV2",
        "startTime": str(current_time_millis - 30 * 24 * 60 * 60 * 1000),  # 30 days ago
        "expiryTime": str(current_time_millis + 30 * 24 * 60 * 60 * 1000),  # 30 days from now
        "autoRenewing": True,
        "priceCurrencyCode": "USD",
        "priceAmountMicros": "*********",  # $9.99
        "countryCode": "US",
        "developerPayload": "",
        "cancelReason": 0,
        "orderId": f"GPA.1234-5678-9012-{purchase_token[:8]}",
        "purchaseType": 0,
        "acknowledgementState": 1,  # 1 for ACKNOWLEDGED
        "purchaseState": 0,  # 0 for PURCHASED, 1 for CANCELED, 2 for PENDING
        "regionCode": "US",
        "externalAccountId": "user_123456",  # This would be your user ID if set during purchase
        "promotionType": 0,
        "promotionCode": "",
        "obfuscatedExternalAccountId": "user_123456",
        "obfuscatedExternalProfileId": "",
        "linkedPurchaseToken": "",
        "pauseReason": 0,
        "productId": product_id,
        "startTimeMillis": str(current_time_millis - 30 * 24 * 60 * 60 * 1000),  # 30 days ago
        "expiryTimeMillis": str(current_time_millis + 30 * 24 * 60 * 60 * 1000),  # 30 days from now
    }
