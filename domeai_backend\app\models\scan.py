import uuid
from sqlalchemy import <PERSON>um<PERSON>, Inte<PERSON>, String, Text, ForeignKey, DateTime, JSON
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.core.database import Base


class Scan(Base):
    """SQLAlchemy model for scans."""
    __tablename__ = "scans"

    id = Column(Integer, primary_key=True, index=True)
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    status = Column(String, nullable=False, default="pending", index=True)
    input_text = Column(Text, nullable=True)
    input_url = Column(String, nullable=True)
    input_content_type = Column(String, nullable=False)  # e.g., "text", "image_path", "url"
    user_provided_context = Column(Text, nullable=True)
    raw_input_payload = Column(JSON, nullable=True)  # Store original request, useful for debugging/reprocessing
    analysis_result = Column(JSON, nullable=True)  # Store the structured AI analysis output
    error_message = Column(Text, nullable=True)  # If processing failed
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Add scan_session_id column
    scan_session_id = Column(UUID(as_uuid=True), ForeignKey("scan_sessions.id"), nullable=True, index=True)

    # Relationships
    owner = relationship("User", back_populates="scans")
    session = relationship("ScanSession", back_populates="scans")
