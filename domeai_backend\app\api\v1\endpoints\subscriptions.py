"""
Subscription management endpoints.
"""
import logging
from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session

from app.api import deps
from app.core.database import get_db
from app.core.google_play_config import TIER_ALLOWANCES
from app.models.user import User
from app.crud.crud_user import get_user_by_email

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()


@router.get(
    "/details",
    status_code=status.HTTP_200_OK
)
def get_subscription_details(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get the current user's subscription details.
    
    Args:
        current_user: The current authenticated user.
        db: Database session.
        
    Returns:
        Dict[str, Any]: The user's subscription details.
    """
    return {
        "subscription_tier": current_user.subscription_tier,
        "monthly_scan_allowance": current_user.monthly_scan_allowance,
        "scans_this_month": current_user.scans_this_month,
        "expert_scan_allowance": current_user.expert_scan_allowance,
        "expert_scans_this_month": current_user.expert_scans_this_month,
        "subscription_expiry_date": current_user.subscription_expiry_date,
        "auto_renew_status": current_user.auto_renew_status
    }


@router.post(
    "/change-plan",
    status_code=status.HTTP_200_OK
)
def change_subscription_plan(
    *,
    db: Session = Depends(get_db),
    email: str = Body(...),
    new_plan: str = Body(...),
    current_user: User = Depends(deps.get_current_user)
) -> Dict[str, Any]:
    """
    Change the subscription plan for a test user.
    
    This endpoint is specifically for testing purposes and only works
    for the test user (<EMAIL>).
    
    Args:
        db: Database session.
        email: The email of the test user.
        new_plan: The new subscription plan (basic, premium, or expert).
        current_user: The current authenticated user.
        
    Returns:
        Dict[str, Any]: The updated subscription details.
    """
    # Validate the new plan
    if new_plan not in ["basic", "premium", "expert"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid plan. Must be one of: basic, premium, expert."
        )
    
    # Only allow changing plan for the test user
    if email != "<EMAIL>":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Plan changing is only allowed for the test user."
        )
    
    # Get the test user
    user = get_user_by_email(db, email=email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Test user not found."
        )
    
    # Update the user's subscription tier
    user.subscription_tier = new_plan
    
    # Update scan allowances based on the new tier
    user.monthly_scan_allowance = TIER_ALLOWANCES[new_plan]["monthly_scans"]
    user.expert_scan_allowance = TIER_ALLOWANCES[new_plan]["expert_scans"]
    
    # Reset scan counters
    user.scans_this_month = 0
    user.expert_scans_this_month = 0
    
    # Save changes
    db.add(user)
    db.commit()
    db.refresh(user)
    
    # Log the plan change
    logger.info(f"Changed subscription plan for user {user.id} to {new_plan}")
    
    # Return the updated subscription details
    return {
        "status": "success",
        "message": f"Subscription plan changed to {new_plan}",
        "subscription_tier": user.subscription_tier,
        "monthly_scan_allowance": user.monthly_scan_allowance,
        "expert_scan_allowance": user.expert_scan_allowance
    }
