"""
<PERSON><PERSON><PERSON> to fix the webhook endpoint registration issue.

This script will:
1. Create a simple FastAPI app with the correct webhook endpoint
2. Test the endpoint with a sample Google Play RTDN
"""
from fastapi import FastAPI, Request
import uvicorn
import base64
import json
import logging
from typing import Dict, Any, Optional

from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="Google Play RTDN Webhook Test")

# Define Pydantic models for Google Play notifications
class PubSubMessage(BaseModel):
    data: str
    messageId: str
    publishTime: str
    attributes: Optional[Dict[str, str]] = None

class PubSubMessageData(BaseModel):
    message: PubSubMessage
    subscription: str

@app.get("/")
def root():
    """Root endpoint that returns a welcome message."""
    return {"message": "Google Play RTDN Webhook Test is running!"}

@app.post("/api/v1/webhooks/googleplay/rtdn")
async def google_play_rtdn(payload: PubSubMessageData):
    """
    Receive and process Google Play Real-Time Developer Notifications (RTDNs).
    """
    logger.info(f"Received Google Play RTDN from subscription: {payload.subscription}")
    
    try:
        # Extract and decode the base64-encoded data
        encoded_data = payload.message.data
        decoded_data = base64.b64decode(encoded_data).decode("utf-8")
        
        # Parse the decoded data as JSON
        notification_data = json.loads(decoded_data)
        
        # Log the notification details
        logger.info(f"Decoded notification: {notification_data}")
        
        if "testNotification" in notification_data:
            logger.info("Test Notification")
        elif "subscriptionNotification" in notification_data:
            logger.info(f"Subscription Notification Type: {notification_data['subscriptionNotification']['notificationType']}")
            logger.info(f"Subscription ID: {notification_data['subscriptionNotification']['subscriptionId']}")
        
        # Return a success response
        return {"status": "success", "message": "RTDN received and processed"}
    
    except Exception as e:
        logger.error(f"Error processing Google Play RTDN: {str(e)}", exc_info=True)
        return {"status": "error", "message": str(e)}

if __name__ == "__main__":
    logger.info("Starting Google Play RTDN Webhook Test server...")
    logger.info("Access the API documentation at http://localhost:8000/docs")
    logger.info("To test the webhook, send a POST request to http://localhost:8000/api/v1/webhooks/googleplay/rtdn")
    uvicorn.run(app, host="0.0.0.0", port=8000)
