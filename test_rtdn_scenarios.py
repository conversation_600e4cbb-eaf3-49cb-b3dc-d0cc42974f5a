"""
Test RTDN scenarios for Google Play subscriptions.

This script tests the following scenarios:
1. New Premium Subscription Purchase
2. Premium Subscription Cancellation
3. Premium Subscription Expiry
"""
import sys
import os
import logging
import json
import base64
import requests
import time
from datetime import datetime, timezone, timedelta

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    from app.models.user import User
    from app.core.config import settings
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure you're running this script from the correct directory.")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Test data
TEST_EMAIL = "<EMAIL>"
TEST_PURCHASE_TOKEN = "test_premium_purchase_token"
TEST_SUBSCRIPTION_ID = "domeai_placeholder_premium_monthly"
TEST_ORDER_ID = "GPA.1234-5678-9012-3456"
WEBHOOK_URL = "http://localhost:8000/api/v1/webhooks/googleplay/rtdn"


def get_user_by_email(email):
    """
    Get a user by email.
    
    Args:
        email: The email of the user to get.
        
    Returns:
        The user if found, None otherwise.
    """
    try:
        # Create database connection
        engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # Get the user
        user = db.query(User).filter(User.email == email).first()
        return user
        
    except Exception as e:
        logger.error(f"Error getting user by email: {str(e)}")
        return None


def get_user_state(user_id):
    """
    Get the current state of a user.
    
    Args:
        user_id: The ID of the user to get the state for.
        
    Returns:
        A dictionary containing the user's state.
    """
    try:
        # Create database connection
        engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # Get the user
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            logger.error(f"User with ID {user_id} not found.")
            return None
        
        # Create a dictionary with the user's state
        user_state = {
            "id": user.id,
            "email": user.email,
            "subscription_provider": user.subscription_provider,
            "provider_subscription_id": user.provider_subscription_id,
            "google_play_purchase_token": user.google_play_purchase_token,
            "subscription_product_id": user.subscription_product_id,
            "subscription_tier": user.subscription_tier,
            "subscription_expiry_date": str(user.subscription_expiry_date) if user.subscription_expiry_date else None,
            "auto_renew_status": user.auto_renew_status,
            "is_trial_period": user.is_trial_period,
            "monthly_scan_allowance": user.monthly_scan_allowance,
            "expert_scan_allowance": user.expert_scan_allowance,
            "scans_this_month": user.scans_this_month,
            "expert_scans_this_month": user.expert_scans_this_month,
            "scan_counter_reset_at": str(user.scan_counter_reset_at) if user.scan_counter_reset_at else None,
            "last_rtdn_received_at": str(user.last_rtdn_received_at) if user.last_rtdn_received_at else None,
            "external_account_id": user.external_account_id
        }
        
        return user_state
        
    except Exception as e:
        logger.error(f"Error getting user state: {str(e)}")
        return None


def create_subscription_purchased_notification():
    """
    Create a subscription purchased notification payload.
    
    Returns:
        A dictionary containing a subscription purchased notification
    """
    # Create a subscription purchased notification
    developer_notification = {
        "version": "1.0",
        "packageName": "com.domeai.scamdetector",
        "eventTimeMillis": str(int(time.time() * 1000)),
        "subscriptionNotification": {
            "version": "1.0",
            "notificationType": 4,  # SUBSCRIPTION_PURCHASED
            "purchaseToken": TEST_PURCHASE_TOKEN,
            "subscriptionId": TEST_SUBSCRIPTION_ID
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_subscription_canceled_notification():
    """
    Create a subscription canceled notification payload.
    
    Returns:
        A dictionary containing a subscription canceled notification
    """
    # Create a subscription canceled notification
    developer_notification = {
        "version": "1.0",
        "packageName": "com.domeai.scamdetector",
        "eventTimeMillis": str(int(time.time() * 1000)),
        "subscriptionNotification": {
            "version": "1.0",
            "notificationType": 3,  # SUBSCRIPTION_CANCELED
            "purchaseToken": TEST_PURCHASE_TOKEN,
            "subscriptionId": TEST_SUBSCRIPTION_ID
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_subscription_expired_notification():
    """
    Create a subscription expired notification payload.
    
    Returns:
        A dictionary containing a subscription expired notification
    """
    # Create a subscription expired notification
    developer_notification = {
        "version": "1.0",
        "packageName": "com.domeai.scamdetector",
        "eventTimeMillis": str(int(time.time() * 1000)),
        "subscriptionNotification": {
            "version": "1.0",
            "notificationType": 13,  # SUBSCRIPTION_EXPIRED
            "purchaseToken": TEST_PURCHASE_TOKEN,
            "subscriptionId": TEST_SUBSCRIPTION_ID
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_pubsub_message(developer_notification):
    """
    Create a Pub/Sub message from a developer notification.
    
    Args:
        developer_notification: The developer notification to encode
        
    Returns:
        A dictionary containing a Pub/Sub message
    """
    # Encode the notification as base64
    encoded_data = base64.b64encode(json.dumps(developer_notification).encode("utf-8")).decode("utf-8")
    
    # Create the Pub/Sub message
    pubsub_message = {
        "message": {
            "data": encoded_data,
            "messageId": "test-message-id",
            "publishTime": datetime.now(timezone.utc).isoformat()
        },
        "subscription": "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
    }
    
    return pubsub_message


def send_notification(notification_payload):
    """
    Send a notification to the webhook endpoint.
    
    Args:
        notification_payload: The notification payload to send
        
    Returns:
        The response from the webhook endpoint
    """
    try:
        # Send the request
        response = requests.post(
            WEBHOOK_URL,
            json=notification_payload,
            headers={"Content-Type": "application/json"}
        )
        
        logger.info(f"Status code: {response.status_code}")
        logger.info(f"Response: {response.text}")
        
        return response
        
    except Exception as e:
        logger.error(f"Error sending notification: {str(e)}")
        return None


def test_subscription_purchased():
    """
    Test the subscription purchased scenario.
    """
    logger.info("Testing subscription purchased scenario")
    
    # Get the user
    user = get_user_by_email(TEST_EMAIL)
    if not user:
        logger.error(f"User {TEST_EMAIL} not found.")
        return
    
    # Get the initial state
    initial_state = get_user_state(user.id)
    logger.info(f"Initial state: {json.dumps(initial_state, indent=2)}")
    
    # Send the notification
    notification_payload = create_subscription_purchased_notification()
    response = send_notification(notification_payload)
    
    if not response or response.status_code != 200:
        logger.error("Failed to send notification.")
        return
    
    # Wait for the notification to be processed
    time.sleep(2)
    
    # Get the final state
    final_state = get_user_state(user.id)
    logger.info(f"Final state: {json.dumps(final_state, indent=2)}")
    
    # Verify the changes
    verify_subscription_purchased(initial_state, final_state)


def test_subscription_canceled():
    """
    Test the subscription canceled scenario.
    """
    logger.info("Testing subscription canceled scenario")
    
    # Get the user
    user = get_user_by_email(TEST_EMAIL)
    if not user:
        logger.error(f"User {TEST_EMAIL} not found.")
        return
    
    # Get the initial state
    initial_state = get_user_state(user.id)
    logger.info(f"Initial state: {json.dumps(initial_state, indent=2)}")
    
    # Send the notification
    notification_payload = create_subscription_canceled_notification()
    response = send_notification(notification_payload)
    
    if not response or response.status_code != 200:
        logger.error("Failed to send notification.")
        return
    
    # Wait for the notification to be processed
    time.sleep(2)
    
    # Get the final state
    final_state = get_user_state(user.id)
    logger.info(f"Final state: {json.dumps(final_state, indent=2)}")
    
    # Verify the changes
    verify_subscription_canceled(initial_state, final_state)


def test_subscription_expired():
    """
    Test the subscription expired scenario.
    """
    logger.info("Testing subscription expired scenario")
    
    # Get the user
    user = get_user_by_email(TEST_EMAIL)
    if not user:
        logger.error(f"User {TEST_EMAIL} not found.")
        return
    
    # Get the initial state
    initial_state = get_user_state(user.id)
    logger.info(f"Initial state: {json.dumps(initial_state, indent=2)}")
    
    # Send the notification
    notification_payload = create_subscription_expired_notification()
    response = send_notification(notification_payload)
    
    if not response or response.status_code != 200:
        logger.error("Failed to send notification.")
        return
    
    # Wait for the notification to be processed
    time.sleep(2)
    
    # Get the final state
    final_state = get_user_state(user.id)
    logger.info(f"Final state: {json.dumps(final_state, indent=2)}")
    
    # Verify the changes
    verify_subscription_expired(initial_state, final_state)


def verify_subscription_purchased(initial_state, final_state):
    """
    Verify the changes for the subscription purchased scenario.
    
    Args:
        initial_state: The initial state of the user
        final_state: The final state of the user
    """
    # Verify the changes
    assert final_state["subscription_tier"] == "premium", f"Expected subscription_tier to be 'premium', got '{final_state['subscription_tier']}'"
    assert final_state["monthly_scan_allowance"] == 100, f"Expected monthly_scan_allowance to be 100, got {final_state['monthly_scan_allowance']}"
    assert final_state["expert_scan_allowance"] == 0, f"Expected expert_scan_allowance to be 0, got {final_state['expert_scan_allowance']}"
    assert final_state["scans_this_month"] == 0, f"Expected scans_this_month to be 0, got {final_state['scans_this_month']}"
    assert final_state["expert_scans_this_month"] == 0, f"Expected expert_scans_this_month to be 0, got {final_state['expert_scans_this_month']}"
    assert final_state["scan_counter_reset_at"] != initial_state["scan_counter_reset_at"], "Expected scan_counter_reset_at to be updated"
    assert final_state["subscription_expiry_date"] is not None, "Expected subscription_expiry_date to be set"
    assert final_state["auto_renew_status"] is True, f"Expected auto_renew_status to be True, got {final_state['auto_renew_status']}"
    assert final_state["google_play_purchase_token"] == TEST_PURCHASE_TOKEN, f"Expected google_play_purchase_token to be '{TEST_PURCHASE_TOKEN}', got '{final_state['google_play_purchase_token']}'"
    assert final_state["provider_subscription_id"] is not None, "Expected provider_subscription_id to be set"
    
    logger.info("Subscription purchased verification passed!")


def verify_subscription_canceled(initial_state, final_state):
    """
    Verify the changes for the subscription canceled scenario.
    
    Args:
        initial_state: The initial state of the user
        final_state: The final state of the user
    """
    # Verify the changes
    assert final_state["auto_renew_status"] is False, f"Expected auto_renew_status to be False, got {final_state['auto_renew_status']}"
    assert final_state["subscription_tier"] == initial_state["subscription_tier"], f"Expected subscription_tier to remain '{initial_state['subscription_tier']}', got '{final_state['subscription_tier']}'"
    assert final_state["monthly_scan_allowance"] == initial_state["monthly_scan_allowance"], f"Expected monthly_scan_allowance to remain {initial_state['monthly_scan_allowance']}, got {final_state['monthly_scan_allowance']}"
    assert final_state["expert_scan_allowance"] == initial_state["expert_scan_allowance"], f"Expected expert_scan_allowance to remain {initial_state['expert_scan_allowance']}, got {final_state['expert_scan_allowance']}"
    assert final_state["subscription_expiry_date"] == initial_state["subscription_expiry_date"], f"Expected subscription_expiry_date to remain '{initial_state['subscription_expiry_date']}', got '{final_state['subscription_expiry_date']}'"
    
    logger.info("Subscription canceled verification passed!")


def verify_subscription_expired(initial_state, final_state):
    """
    Verify the changes for the subscription expired scenario.
    
    Args:
        initial_state: The initial state of the user
        final_state: The final state of the user
    """
    # Verify the changes
    assert final_state["subscription_tier"] == "free", f"Expected subscription_tier to be 'free', got '{final_state['subscription_tier']}'"
    assert final_state["monthly_scan_allowance"] == 5, f"Expected monthly_scan_allowance to be 5, got {final_state['monthly_scan_allowance']}"
    assert final_state["expert_scan_allowance"] == 0, f"Expected expert_scan_allowance to be 0, got {final_state['expert_scan_allowance']}"
    assert final_state["auto_renew_status"] is False, f"Expected auto_renew_status to be False, got {final_state['auto_renew_status']}"
    
    logger.info("Subscription expired verification passed!")


if __name__ == "__main__":
    # Test the scenarios
    test_subscription_purchased()
    print("\n" + "-" * 80 + "\n")
    test_subscription_canceled()
    print("\n" + "-" * 80 + "\n")
    test_subscription_expired()
