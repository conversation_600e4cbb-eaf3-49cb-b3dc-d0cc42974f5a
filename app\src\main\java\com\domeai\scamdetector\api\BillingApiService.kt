package com.domeai.scamdetector.api

import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * API service for billing operations.
 */
interface BillingApiService {
    
    /**
     * Verify a Google Play purchase with the backend.
     */
    @POST("api/v1/billing/verify-purchase")
    suspend fun verifyPurchase(@Body request: VerifyPurchaseRequest): Response<VerifyPurchaseResponse>
}

/**
 * Request to verify a Google Play purchase.
 */
data class VerifyPurchaseRequest(
    val productId: String,
    val purchaseToken: String,
    val orderId: String? = null
)

/**
 * Response from verifying a Google Play purchase.
 */
data class VerifyPurchaseResponse(
    val isValid: Boolean,
    val subscriptionTier: String,
    val expiryDate: String,
    val message: String? = null
)
