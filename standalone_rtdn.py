"""
Standalone FastAPI app for testing Google Play RTDN webhook.
"""
import base64
import json
import logging
from typing import Any, Dict, Optional

from fastapi import FastAP<PERSON>, status
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="Standalone Google Play RTDN Webhook Test")

# Define Pydantic models
class PubSubMessage(BaseModel):
    data: str
    messageId: str
    publishTime: str
    attributes: Optional[Dict[str, str]] = None

class PubSubMessageData(BaseModel):
    message: PubSubMessage
    subscription: str

@app.get("/")
def root():
    return {"message": "Standalone RTDN webhook test is running!"}

@app.post("/webhook")
async def webhook(payload: PubSubMessageData):
    logger.info(f"Received webhook: {payload}")
    
    try:
        # Extract and decode the base64-encoded data
        encoded_data = payload.message.data
        decoded_data = base64.b64decode(encoded_data).decode("utf-8")
        
        # Parse the decoded data as JSON
        notification_data = json.loads(decoded_data)
        
        logger.info(f"Decoded notification: {notification_data}")
        
        # Return a success response
        return {"status": "success", "message": "Webhook received and processed"}
    
    except Exception as e:
        logger.error(f"Error processing webhook: {str(e)}", exc_info=True)
        return {"status": "error", "message": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
