package com.domeai.data.repository

import com.domeai.data.model.AuthResult
import com.domeai.data.model.ForgotPasswordRequest
import com.domeai.data.model.LoginCredentials
import com.domeai.data.model.SignUpCredentials
import com.domeai.data.model.User
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for authentication operations
 */
interface AuthRepository {
    /**
     * Login with email and password
     */
    suspend fun login(credentials: LoginCredentials): Flow<AuthResult>
    
    /**
     * Register a new user
     */
    suspend fun signUp(credentials: SignUpCredentials): Flow<AuthResult>
    
    /**
     * Request password reset
     */
    suspend fun forgotPassword(request: ForgotPasswordRequest): Flow<AuthResult>
    
    /**
     * Get the current authenticated user
     */
    suspend fun getCurrentUser(): User?
    
    /**
     * Check if user is authenticated
     */
    suspend fun isAuthenticated(): <PERSON><PERSON>an
    
    /**
     * Logout the current user
     */
    suspend fun logout()
}
