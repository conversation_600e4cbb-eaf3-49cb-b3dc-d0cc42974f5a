#!/usr/bin/env python
"""
<PERSON>ript to test the full RAG pipeline with the Tech Support Scam scenario.
"""

import asyncio
import json
import logging
import sys

# Add the parent directory to the path so we can import from app
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.database import SessionLocal
from app.services.ai_services import OpenAIModelService
from app.core.config import settings
from app.schemas.scan import ScanResultData

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

async def test_tech_support_scam():
    """Test the full RAG pipeline with the Tech Support Scam scenario."""
    db = SessionLocal()
    try:
        # Initialize the OpenAI service
        ai_service = OpenAIModelService(api_key=settings.OPENAI_API_KEY)
        
        # Tech support scam text
        text_content = """
        WARNING! Your computer is infected with 3 viruses! Call Microsoft Support Immediately at ************** to prevent data loss. Your personal information is at risk. If you close this window, your computer will be disabled. Our certified Microsoft technicians can remove the viruses remotely for a one-time fee of $299.99. Act now to protect your data!
        """
        
        # Source metadata
        source_app = "Web Browser"
        source_url = "https://fake-security-alert.com/warning"
        
        # Generate text embedding
        logger.info("Generating text embedding...")
        text_embedding = await ai_service.get_text_embedding(text=text_content)
        
        # Convert the embedding to a PostgreSQL vector literal
        embedding_literal = "[" + ",".join(str(x) for x in text_embedding) + "]"
        
        # Find similar KB chunks using direct SQL
        logger.info("Finding similar KB chunks using direct SQL...")
        
        # Execute a raw SQL query directly
        conn = db.connection()
        cursor = conn.connection.cursor()
        cursor.execute(f"""
        SELECT id, source, content, embedding <-> '{embedding_literal}'::vector AS distance
        FROM knowledge_base_chunks
        ORDER BY distance ASC
        LIMIT 3
        """)
        
        # Fetch the results
        similar_chunks_raw = cursor.fetchall()
        
        # Log the similar chunks
        logger.info(f"Found {len(similar_chunks_raw)} similar chunks:")
        
        # Create a list to store the similar chunks
        similar_chunks = []
        for i, row in enumerate(similar_chunks_raw):
            chunk_id, source, content, distance = row
            logger.info(f"Chunk {i+1}: ID {chunk_id}, Source: {source}, Distance: {distance}")
            logger.info(f"Content: {content[:150]}...")
            
            # Add the chunk to the list
            similar_chunks.append({
                "id": chunk_id,
                "source": source,
                "content": content,
                "distance": distance
            })
        
        # Extract content from similar chunks for the prompt
        knowledge_context = "\n\n".join([
            f"Source: {chunk['source']}\n{chunk['content']}"
            for chunk in similar_chunks
        ])
        
        # Construct the prompt
        prompt = f"""
        You are an expert scam detector AI. Analyze the following content for potential scam indicators.
        
        CONTENT TO ANALYZE:
        {text_content}
        
        SOURCE APP: {source_app}
        SOURCE URL: {source_url}
        
        RELEVANT KNOWLEDGE BASE INFORMATION:
        {knowledge_context}
        
        Based on the content and the knowledge base information, provide a comprehensive scam analysis in JSON format with the following fields:
        
        1. risk_score: A number between 0.0 and 1.0, where 0.0 is definitely safe and 1.0 is definitely a scam.
        2. explanation: A detailed explanation of why you think this is or isn't a scam.
        3. red_flags: A list of specific red flags or suspicious elements in the content.
        4. recommendations: Specific recommendations for the user.
        5. confidence: Your confidence level in this analysis (Low, Medium, High).
        
        Format your response as a valid JSON object with these fields.
        """
        
        # Log the prompt
        logger.info("Prompt sent to GPT-4.1:")
        logger.info(prompt)
        
        # Call the AI service to analyze the content
        logger.info("Calling GPT-4.1...")
        response = await ai_service.client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": "You are an expert scam detector AI."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            response_format={"type": "json_object"}
        )
        
        # Extract the response text
        response_text = response.choices[0].message.content
        
        # Log the raw response
        logger.info("Raw response from GPT-4.1:")
        logger.info(response_text)
        
        # Parse the JSON response
        try:
            response_json = json.loads(response_text)
            
            # Create a ScanResultData object
            recommendations = response_json.get("recommendations", "")
            if isinstance(recommendations, list):
                recommendations = "\n".join(recommendations)
                
            result = ScanResultData(
                risk_score=response_json.get("risk_score", 0.0),
                explanation=response_json.get("explanation", ""),
                detected_red_flags=response_json.get("red_flags", []),
                recommendations=recommendations
            )
            
            # Store confidence separately
            confidence = response_json.get("confidence", "Medium")
            
            # Log the result
            logger.info("Scam analysis result:")
            logger.info(f"Risk score: {result.risk_score}")
            logger.info(f"Explanation: {result.explanation}")
            logger.info(f"Red flags: {result.detected_red_flags}")
            logger.info(f"Recommendations: {result.recommendations}")
            logger.info(f"Confidence: {confidence}")
            
            # Save the results to a file
            with open("tech_support_scam_results.json", "w") as f:
                json.dump({
                    "similar_chunks": [
                        {
                            "id": chunk["id"],
                            "source": chunk["source"],
                            "content": chunk["content"][:150] + "...",
                            "distance": chunk["distance"]
                        }
                        for chunk in similar_chunks
                    ],
                    "prompt": prompt,
                    "raw_response": response_text,
                    "result": {
                        "risk_score": result.risk_score,
                        "explanation": result.explanation,
                        "red_flags": result.detected_red_flags,
                        "recommendations": result.recommendations,
                        "confidence": confidence
                    }
                }, f, indent=2)
            
            logger.info("Results saved to tech_support_scam_results.json")
        
        except json.JSONDecodeError:
            logger.error("Failed to parse JSON response")
            # Return a default result
            logger.error("Failed to save results")
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_tech_support_scam())
