import requests

base_url = "https://domeai-backend.onrender.com"

def run_pgvector_fix():
    """Run the pgvector fix to convert FLOAT[] to vector(1536)"""
    print("🔧 Running pgvector fix...")
    try:
        response = requests.post(f"{base_url}/admin/fix-pgvector", timeout=120)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Fix completed!")
            print(f"Status: {result.get('status')}")
            print(f"Return Code: {result.get('return_code')}")
            print("\n--- STDOUT ---")
            print(result.get('stdout', 'No output'))
            print("\n--- STDERR ---")
            print(result.get('stderr', 'No errors'))
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    run_pgvector_fix()
