package com.domeai.presentation.auth

import androidx.lifecycle.viewModelScope
import com.domeai.data.model.AuthResult
import com.domeai.data.model.LoginCredentials
import com.domeai.data.model.ValidationResult
import com.domeai.data.repository.AuthRepository
import com.domeai.data.util.Validator
import com.domeai.presentation.common.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * UI State for Login Screen
 */
data class LoginUiState(
    val email: String = "",
    val password: String = "",
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val isLoginSuccessful: Boolean = false,
    val emailError: String? = null,
    val passwordError: String? = null,
    val isFormValid: Boolean = false
) : com.domeai.presentation.common.UiState

/**
 * UI Events for Login Screen
 */
sealed class LoginUiEvent : com.domeai.presentation.common.UiEvent {
    data object NavigateToSignUp : LoginUiEvent()
    data object NavigateToForgotPassword : LoginUiEvent()
    data object NavigateToMain : LoginUiEvent()
}

/**
 * UI Actions for Login Screen
 */
sealed class LoginUiAction : com.domeai.presentation.common.UiAction {
    data class UpdateEmail(val email: String) : LoginUiAction()
    data class UpdatePassword(val password: String) : LoginUiAction()
    data object Login : LoginUiAction()
    data object NavigateToSignUp : LoginUiAction()
    data object NavigateToForgotPassword : LoginUiAction()
}

/**
 * ViewModel for Login Screen
 */
@HiltViewModel
class LoginViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val validator: Validator
) : BaseViewModel<LoginUiState, LoginUiEvent, LoginUiAction>() {

    override fun createInitialState(): LoginUiState = LoginUiState()

    override fun handleAction(action: LoginUiAction) {
        when (action) {
            is LoginUiAction.UpdateEmail -> updateEmail(action.email)
            is LoginUiAction.UpdatePassword -> updatePassword(action.password)
            is LoginUiAction.Login -> login()
            is LoginUiAction.NavigateToSignUp -> navigateToSignUp()
            is LoginUiAction.NavigateToForgotPassword -> navigateToForgotPassword()
        }
    }

    private fun updateEmail(email: String) {
        updateState { it.copy(email = email, emailError = null) }
        validateForm()
    }

    private fun updatePassword(password: String) {
        updateState { it.copy(password = password, passwordError = null) }
        validateForm()
    }

    private fun validateForm() {
        val currentState = uiState.value
        val emailValid = validator.validateEmail(currentState.email) is ValidationResult.Valid
        val passwordValid = currentState.password.isNotBlank()

        val isFormValid = emailValid && passwordValid

        // Clear error message if form is valid
        val errorMessage = if (isFormValid) null else currentState.errorMessage

        updateState {
            it.copy(
                isFormValid = isFormValid,
                errorMessage = errorMessage
            )
        }
    }

    private fun login() {
        val currentState = uiState.value
        val emailValidation = validator.validateEmail(currentState.email)
        val passwordValidation = validator.validatePassword(currentState.password)

        val hasErrors = handleValidationResults(emailValidation, passwordValidation)

        if (hasErrors) return

        viewModelScope.launch {
            val credentials = LoginCredentials(
                email = currentState.email,
                password = currentState.password
            )

            authRepository.login(credentials).collectLatest { result ->
                when (result) {
                    is AuthResult.Loading -> {
                        updateState { it.copy(isLoading = true, errorMessage = null) }
                    }
                    is AuthResult.Success -> {
                        updateState {
                            it.copy(
                                isLoading = false,
                                isLoginSuccessful = true,
                                errorMessage = null
                            )
                        }
                        // Navigate to main screen
                        sendEvent(LoginUiEvent.NavigateToMain)
                    }
                    is AuthResult.Error -> {
                        updateState {
                            it.copy(
                                isLoading = false,
                                errorMessage = result.message
                            )
                        }
                    }
                    else -> { /* Ignore other states */ }
                }
            }
        }
    }

    private fun handleValidationResults(
        emailValidation: ValidationResult,
        passwordValidation: ValidationResult
    ): Boolean {
        var hasErrors = false

        if (emailValidation is ValidationResult.Invalid) {
            updateState { it.copy(emailError = emailValidation.errorMessage) }
            hasErrors = true
        }

        if (passwordValidation is ValidationResult.Invalid) {
            updateState { it.copy(passwordError = passwordValidation.errorMessage) }
            hasErrors = true
        }

        return hasErrors
    }

    private fun navigateToSignUp() {
        sendEvent(LoginUiEvent.NavigateToSignUp)
    }

    private fun navigateToForgotPassword() {
        sendEvent(LoginUiEvent.NavigateToForgotPassword)
    }
}
