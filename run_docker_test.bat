@echo off
echo Running DomeAI Session Test in Docker Container

REM Find the correct container ID for domeai_backend
for /f "tokens=1" %%i in ('docker ps ^| findstr domeai_backend') do (
    set CONTAINER_ID=%%i
    goto :found_container
)

:not_found
echo No running domeai_backend container found!
echo Please make sure the Docker container is running.
goto :end

:found_container
echo Found container: %CONTAINER_ID%

REM Copy the test script to the container
echo Copying test script to container...
docker cp domeai_backend\scripts\docker_session_test.py %CONTAINER_ID%:/app/scripts/

REM Run the test script inside the container
echo Running test script inside container...
docker exec -it %CONTAINER_ID% python /app/scripts/docker_session_test.py

:end
pause
