package com.domeai.data.repository

import com.domeai.data.model.BillingCycle
import com.domeai.data.model.PaymentRecord
import com.domeai.data.model.PaymentStatus
import com.domeai.data.model.Subscription
import com.domeai.data.model.SubscriptionStatus
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import java.util.Date
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Fake implementation of SubscriptionRepository for testing and development
 */
@Singleton
class FakeSubscriptionRepository @Inject constructor() : SubscriptionRepository {

    private val subscriptionFlow = MutableStateFlow(
        Subscription(
            id = "sub123",
            userId = "user123",
            planName = "Premium Plan",
            price = 9.99,
            billingCycle = BillingCycle.MONTHLY,
            startDate = Date(),
            endDate = Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000), // 30 days from now
            isAutoRenewEnabled = true,
            status = SubscriptionStatus.ACTIVE,
            scansThisMonth = 3,
            expertScansThisMonth = 0,
            monthlyScanAllowance = 100,
            expertScanAllowance = 0
        )
    )

    private val paymentHistoryFlow = MutableStateFlow(
        listOf(
            PaymentRecord(
                id = "pay123",
                subscriptionId = "sub123",
                userId = "user123",
                amount = 9.99,
                description = "Premium Plan - Monthly",
                date = Date(System.currentTimeMillis() - 30L * 24 * 60 * 60 * 1000), // 30 days ago
                status = PaymentStatus.COMPLETED
            ),
            PaymentRecord(
                id = "pay124",
                subscriptionId = "sub123",
                userId = "user123",
                amount = 9.99,
                description = "Premium Plan - Monthly",
                date = Date(System.currentTimeMillis() - 60L * 24 * 60 * 60 * 1000), // 60 days ago
                status = PaymentStatus.COMPLETED
            ),
            PaymentRecord(
                id = "pay125",
                subscriptionId = "sub123",
                userId = "user123",
                amount = 9.99,
                description = "Premium Plan - Monthly",
                date = Date(System.currentTimeMillis() - 90L * 24 * 60 * 60 * 1000), // 90 days ago
                status = PaymentStatus.COMPLETED
            )
        )
    )

    override suspend fun getCurrentSubscription(forceRefresh: Boolean): Subscription {
        // In a fake repository, forceRefresh doesn't do anything
        return subscriptionFlow.value
    }

    override suspend fun updateSubscriptionPlan(planName: String): Subscription {
        val currentSubscription = subscriptionFlow.value

        val (price, billingCycle) = when (planName) {
            "Free Plan" -> 0.0 to BillingCycle.FREE
            "Premium Plan" -> 9.99 to BillingCycle.MONTHLY
            "Annual Plan" -> 99.99 to BillingCycle.ANNUAL
            else -> 0.0 to BillingCycle.FREE
        }

        val endDate = when (billingCycle) {
            BillingCycle.FREE -> Date(Long.MAX_VALUE)
            BillingCycle.MONTHLY -> Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000)
            BillingCycle.ANNUAL -> Date(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000)
        }

        val updatedSubscription = currentSubscription.copy(
            planName = planName,
            price = price,
            billingCycle = billingCycle,
            startDate = Date(),
            endDate = endDate,
            status = SubscriptionStatus.ACTIVE
        )

        subscriptionFlow.value = updatedSubscription

        // Add a payment record if it's not a free plan
        if (planName != "Free Plan") {
            val newPayment = PaymentRecord(
                id = UUID.randomUUID().toString(),
                subscriptionId = currentSubscription.id,
                userId = currentSubscription.userId,
                amount = price,
                description = "$planName - ${billingCycle.name.lowercase().capitalize()}",
                date = Date(),
                status = PaymentStatus.COMPLETED
            )

            val currentPayments = paymentHistoryFlow.value.toMutableList()
            currentPayments.add(0, newPayment)
            paymentHistoryFlow.value = currentPayments
        }

        return updatedSubscription
    }

    override suspend fun setAutoRenew(enabled: Boolean): Subscription {
        val updatedSubscription = subscriptionFlow.value.copy(
            isAutoRenewEnabled = enabled
        )
        subscriptionFlow.value = updatedSubscription
        return updatedSubscription
    }

    override suspend fun cancelSubscription(): Subscription {
        val updatedSubscription = subscriptionFlow.value.copy(
            planName = "Free Plan",
            price = 0.0,
            billingCycle = BillingCycle.FREE,
            isAutoRenewEnabled = false,
            status = SubscriptionStatus.CANCELLED
        )
        subscriptionFlow.value = updatedSubscription
        return updatedSubscription
    }

    override suspend fun getPaymentHistory(): List<PaymentRecord> {
        return paymentHistoryFlow.value
    }

    override fun getPaymentHistoryFlow(): Flow<List<PaymentRecord>> {
        return paymentHistoryFlow
    }
}

private fun String.capitalize(): String {
    return if (this.isEmpty()) this else this[0].uppercase() + this.substring(1)
}
