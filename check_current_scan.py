#!/usr/bin/env python3
"""
Check the most recent scan status to see if it's completed
"""
import requests
import json

API_BASE_URL = "https://domeai-backend.onrender.com"

def check_recent_scans():
    # Login first
    login_url = f"{API_BASE_URL}/api/v1/auth/login"
    login_data = {
        "username": "<EMAIL>",
        "password": "@#48CrVGt3"
    }
    
    print("Logging in...")
    response = requests.post(login_url, data=login_data)
    if response.status_code != 200:
        print(f"Login failed: {response.status_code}")
        return
    
    token = response.json()["access_token"]
    print("✅ Login successful")
    
    # Check recent scans (100, 101, 102, etc.)
    headers = {"Authorization": f"Bearer {token}"}
    
    for scan_id in [100, 101, 102, 103]:
        scan_url = f"{API_BASE_URL}/api/v1/scans/{scan_id}"
        print(f"\n--- Checking <PERSON>an {scan_id} ---")
        
        response = requests.get(scan_url, headers=headers)
        
        if response.status_code == 200:
            try:
                scan_data = response.json()
                print(f"✅ Scan {scan_id} found")
                print(f"  Status: {scan_data.get('status')}")
                print(f"  Created: {scan_data.get('created_at')}")
                print(f"  Content Type: {scan_data.get('input_content_type')}")
                
                if scan_data.get('user_provided_context'):
                    context = scan_data.get('user_provided_context')[:50] + "..."
                    print(f"  User Context: {context}")
                
                analysis = scan_data.get('analysis_result')
                if analysis:
                    print(f"  ✅ Analysis Complete - Risk Score: {analysis.get('risk_score')}")
                else:
                    print(f"  ⏳ Analysis Pending")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
        elif response.status_code == 404:
            print(f"❌ Scan {scan_id} not found")
        else:
            print(f"❌ Error {response.status_code}: {response.text}")

def test_polling_manually():
    """Manually test the polling mechanism"""
    login_url = f"{API_BASE_URL}/api/v1/auth/login"
    login_data = {
        "username": "<EMAIL>",
        "password": "@#48CrVGt3"
    }
    
    response = requests.post(login_url, data=login_data)
    token = response.json()["access_token"]
    
    # Test polling scan 100 (which should be completed)
    scan_id = 100
    scan_url = f"{API_BASE_URL}/api/v1/scans/{scan_id}"
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"\n=== Manual Polling Test for Scan {scan_id} ===")
    print(f"URL: {scan_url}")
    print(f"Headers: Authorization: Bearer {token[:20]}...")
    
    response = requests.get(scan_url, headers=headers)
    
    print(f"Response Status: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"✅ Polling successful!")
            print(f"Status: {data.get('status')}")
            print(f"Analysis Present: {'Yes' if data.get('analysis_result') else 'No'}")
            
            if data.get('status') == 'completed':
                print(f"🎉 Scan completed - should display results!")
                analysis = data.get('analysis_result', {})
                print(f"Risk Score: {analysis.get('risk_score')}")
                print(f"Red Flags: {len(analysis.get('detected_red_flags', []))}")
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            print(f"Raw response: {response.text[:200]}...")
    else:
        print(f"❌ Polling failed: {response.text}")

if __name__ == "__main__":
    check_recent_scans()
    test_polling_manually()
