from fastapi import FastAPI
from pydantic import BaseModel
from typing import Dict, Any

app = FastAPI()

class WebhookPayload(BaseModel):
    message: Dict[str, Any]
    subscription: str

@app.get("/")
def root():
    return {"message": "Simple webhook test is running!"}

@app.post("/webhook")
async def webhook(payload: WebhookPayload):
    print(f"Received webhook: {payload}")
    return {"status": "success", "message": "Webhook received"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
