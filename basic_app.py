from fastapi import FastAPI, Request

app = FastAPI()

@app.get("/")
def root():
    return {"message": "Basic FastAPI app is running!"}

@app.post("/webhook")
async def webhook(request: Request):
    data = await request.json()
    print(f"Received webhook: {data}")
    return {"status": "success", "message": "Webhook received"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
