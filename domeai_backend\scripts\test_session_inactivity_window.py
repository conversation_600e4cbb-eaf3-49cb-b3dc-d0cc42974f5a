#!/usr/bin/env python
"""
Script to test session inactivity window for Premium tier.

This script:
1. Creates a new session with 1 text input
2. Updates the session's last_activity_at to be older than SESSION_ACTIVITY_WINDOW_HOURS
3. Submits a 2nd text input with the same session_id
4. Verifies that a new session is automatically created and a new Premium scan credit is consumed
"""

import asyncio
import logging
import sys
import os
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan import Scan
from app.models.scan_session import ScanSession
from app.crud import crud_user, crud_scan, crud_scan_session
from app.core.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

async def create_test_user(db: Session, email: str, tier: str = "premium") -> User:
    """Create a test user with the specified subscription tier."""
    # Check if user already exists
    user = crud_user.get_user_by_email(db, email=email)
    if user:
        logger.info(f"User {email} already exists with ID {user.id}")
        # Update subscription tier
        user.subscription_tier = tier
        user.scans_this_month = 0  # Reset scan count
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    # Create new user
    from app.schemas.user import UserCreate

    user_in = UserCreate(
        email=email,
        password="testpassword",
        full_name="Test User"
    )

    user = crud_user.create_user(db=db, obj_in=user_in)

    # Update user properties
    user.subscription_tier = tier
    user.scans_this_month = 0
    user.scan_allowance = 50 if tier == "premium" else 100
    user.expert_scans_this_month = 0
    user.expert_scan_allowance = 0 if tier == "free" else (10 if tier == "premium" else 20)

    db.add(user)
    db.commit()
    db.refresh(user)
    logger.info(f"Created user {email} with ID {user.id} and tier {tier}")
    return user

async def create_scan_in_session(
    db: Session,
    user: User,
    session_id: Optional[uuid.UUID],
    message: str
) -> Scan:
    """Create a scan in the specified session."""
    # Create a new session if none provided
    if not session_id:
        session = crud_scan_session.create_scan_session(db=db, owner_id=user.id)
        session_id = session.id
        logger.info(f"Created new session {session_id}")

    # Create scan
    scan = Scan(
        owner_id=user.id,
        status="completed",
        input_content_type="text",
        input_text=message,
        scan_session_id=session_id,
        raw_input_payload={"is_expert_scan": False}
    )

    db.add(scan)
    db.commit()
    db.refresh(scan)
    logger.info(f"Created scan {scan.id} in session {session_id}")

    return scan

async def test_session_inactivity_window():
    """Test session inactivity window for Premium tier."""
    logger.info("Testing session inactivity window for Premium tier")

    # Create a database session
    db = SessionLocal()
    try:
        # Create a test user with premium tier
        user = await create_test_user(db, email="<EMAIL>", tier="premium")

        # Log initial scan count
        initial_scan_count = user.scans_this_month
        logger.info(f"Initial scan count: {initial_scan_count}")

        # Create a new session
        session = crud_scan_session.create_scan_session(db=db, owner_id=user.id)
        session_id = session.id
        logger.info(f"Created session {session_id}")

        # Create first scan
        scan1 = await create_scan_in_session(db, user, session_id, "First message")
        logger.info(f"Created first scan {scan1.id} in session {session_id}")

        # Update user's scan count
        user.scans_this_month += 1
        db.add(user)
        db.commit()
        db.refresh(user)
        logger.info(f"User scan count after first scan: {user.scans_this_month}")

        # Manually update the session's last_activity_at to be older than the inactivity window
        inactivity_window = timedelta(hours=settings.SESSION_ACTIVITY_WINDOW_HOURS)
        old_time = datetime.now() - inactivity_window - timedelta(minutes=30)

        # Update the session directly in the database
        db.query(ScanSession).filter(ScanSession.id == session_id).update(
            {"last_activity_at": old_time}
        )
        db.commit()

        # Refresh the session
        session = crud_scan_session.get_scan_session(db=db, session_id=session_id, owner_id=user.id)
        logger.info(f"Updated session {session_id} last_activity_at to {session.last_activity_at}")

        # Create second scan with the same session ID
        scan2 = await create_scan_in_session(db, user, session_id, "Second message after inactivity")
        logger.info(f"Created second scan {scan2.id} in session {scan2.scan_session_id}")

        # Update user's scan count
        user.scans_this_month += 1
        db.add(user)
        db.commit()
        db.refresh(user)
        logger.info(f"User scan count after second scan: {user.scans_this_month}")

        # Verify results
        logger.info("\n--- Verification ---\n")

        # Check if a new session was created
        if scan2.scan_session_id != session_id:
            logger.info(f"PASS: New session {scan2.scan_session_id} was created after inactivity period")
        else:
            logger.warning(f"FAIL: Second scan was added to the same session {session_id}")

        # Verify that we consumed 2 scan credits
        final_scan_count = user.scans_this_month
        logger.info(f"Final scan count: {final_scan_count}")
        logger.info(f"Scan count difference: {final_scan_count - initial_scan_count}")

        if final_scan_count - initial_scan_count == 2:
            logger.info("PASS: Consumed 2 scan credits (1 for each session)")
        else:
            logger.warning(f"FAIL: Consumed {final_scan_count - initial_scan_count} scan credits, expected 2")

    finally:
        db.close()

async def main():
    """Run the script."""
    await test_session_inactivity_window()

if __name__ == "__main__":
    asyncio.run(main())
