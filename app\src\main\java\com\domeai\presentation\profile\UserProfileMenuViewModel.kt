package com.domeai.presentation.profile

import androidx.lifecycle.ViewModel
import com.domeai.data.repository.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class UserProfileMenuViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {
    // This is a simple ViewModel for now
    // In a real app, it would load user profile data, subscription info, etc.
}
