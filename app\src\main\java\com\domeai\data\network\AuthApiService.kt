package com.domeai.data.network

import com.domeai.data.model.network.LoginRequest
import com.domeai.data.model.network.RegisterRequest
import com.domeai.data.model.network.TokenResponse
import com.domeai.data.model.network.UserResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

/**
 * API service for authentication operations
 */
interface AuthApiService {
    /**
     * Register a new user
     */
    @POST("api/v1/auth/register")
    suspend fun register(@Body request: RegisterRequest): Response<UserResponse>
    
    /**
     * Login with email and password
     */
    @POST("api/v1/auth/login")
    suspend fun login(@Body request: LoginRequest): Response<TokenResponse>
    
    /**
     * Get current user information
     */
    @GET("api/v1/auth/me")
    suspend fun getCurrentUser(): Response<UserResponse>
}
