package com.domeai.data.preferences

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Preference keys for authentication
 */
private object AuthPreferenceKeys {
    val ACCESS_TOKEN = stringPreferencesKey("access_token")
    val TOKEN_TYPE = stringPreferencesKey("token_type")
}

/**
 * Repository for managing authentication preferences
 */
@Singleton
class AuthPreferences @Inject constructor(
    private val dataStore: DataStore<Preferences>
) {
    /**
     * Get the current access token
     */
    val accessToken: Flow<String?> = dataStore.data.map { preferences ->
        preferences[AuthPreferenceKeys.ACCESS_TOKEN]
    }

    /**
     * Get the current token type
     */
    val tokenType: Flow<String?> = dataStore.data.map { preferences ->
        preferences[AuthPreferenceKeys.TOKEN_TYPE]
    }

    /**
     * Get the full authorization header value (e.g., "Bearer token123")
     */
    val authHeader: Flow<String?> = dataStore.data.map { preferences ->
        val token = preferences[AuthPreferenceKeys.ACCESS_TOKEN]
        val type = preferences[AuthPreferenceKeys.TOKEN_TYPE]
        if (token != null && type != null) {
            "${type.replaceFirstChar { it.uppercase() }} $token"
        } else {
            null
        }
    }

    /**
     * Check if the user is authenticated (has a token)
     */
    val isAuthenticated: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[AuthPreferenceKeys.ACCESS_TOKEN] != null
    }

    /**
     * Save authentication tokens
     */
    suspend fun saveAuthTokens(accessToken: String, tokenType: String) {
        dataStore.edit { preferences ->
            preferences[AuthPreferenceKeys.ACCESS_TOKEN] = accessToken
            preferences[AuthPreferenceKeys.TOKEN_TYPE] = tokenType
        }
    }

    /**
     * Clear authentication tokens (logout)
     */
    suspend fun clearAuthTokens() {
        dataStore.edit { preferences ->
            preferences.remove(AuthPreferenceKeys.ACCESS_TOKEN)
            preferences.remove(AuthPreferenceKeys.TOKEN_TYPE)
        }
    }
}
