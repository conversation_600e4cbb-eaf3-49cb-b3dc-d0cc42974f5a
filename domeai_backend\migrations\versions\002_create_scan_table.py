"""create scan table

Revision ID: 002
Revises: 001
Create Date: 2025-05-08 06:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade():
    # Create scans table
    op.create_table(
        'scans',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('owner_id', sa.Integer(), nullable=False),
        sa.Column('status', sa.String(), nullable=False, server_default='pending'),
        sa.Column('input_text', sa.Text(), nullable=True),
        sa.Column('input_url', sa.String(), nullable=True),
        sa.Column('input_content_type', sa.String(), nullable=False),
        sa.Column('user_provided_context', sa.Text(), nullable=True),
        sa.Column('raw_input_payload', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('analysis_result', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index(op.f('ix_scans_id'), 'scans', ['id'], unique=False)
    op.create_index(op.f('ix_scans_owner_id'), 'scans', ['owner_id'], unique=False)
    op.create_index(op.f('ix_scans_status'), 'scans', ['status'], unique=False)


def downgrade():
    # Drop indexes
    op.drop_index(op.f('ix_scans_status'), table_name='scans')
    op.drop_index(op.f('ix_scans_owner_id'), table_name='scans')
    op.drop_index(op.f('ix_scans_id'), table_name='scans')
    
    # Drop scans table
    op.drop_table('scans')
