import base64
import json
import requests

# Create a test notification
developer_notification = {
    "version": "1.0",
    "packageName": "com.domeai.scamdetector",
    "eventTimeMillis": "1621234567890",
    "testNotification": {
        "version": "1.0"
    }
}

# Encode the notification as base64
encoded_data = base64.b64encode(json.dumps(developer_notification).encode("utf-8")).decode("utf-8")

# Create the Pub/Sub message
pubsub_message = {
    "message": {
        "data": encoded_data,
        "messageId": "test-message-id",
        "publishTime": "2023-05-20T10:00:00.000Z"
    },
    "subscription": "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
}

# API endpoint
url = "http://localhost:8000/api/v1/webhooks/googleplay/rtdn"

print("Sending test notification...")
try:
    response = requests.post(url, json=pubsub_message)
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")
except Exception as e:
    print(f"Error: {str(e)}")
