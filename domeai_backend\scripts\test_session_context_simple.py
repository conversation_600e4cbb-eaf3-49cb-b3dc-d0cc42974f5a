#!/usr/bin/env python
"""
Simple script to test session context accumulation.
"""

import logging
import sys
import os
import json
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.config import settings
from app.core.database import SessionLocal

def test_session_context():
    """Test session context accumulation."""
    logger.info("Testing session context accumulation")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Test query to check if the database is accessible
        result = db.execute(text("SELECT 1"))
        logger.info(f"Database connection test: {result.fetchone()}")
        
        # Check if the settings are loaded correctly
        logger.info(f"SESSION_MAX_INPUTS_PREMIUM: {settings.SESSION_MAX_INPUTS_PREMIUM}")
        logger.info(f"SESSION_MAX_INPUTS_EXPERT: {settings.SESSION_MAX_INPUTS_EXPERT}")
        logger.info(f"SESSION_ACTIVITY_WINDOW_HOURS: {settings.SESSION_ACTIVITY_WINDOW_HOURS}")
        
        # Check if the scan_sessions table exists
        result = db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'scan_sessions'
            )
        """))
        logger.info(f"scan_sessions table exists: {result.fetchone()[0]}")
        
        # Check if the scans table exists
        result = db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'scans'
            )
        """))
        logger.info(f"scans table exists: {result.fetchone()[0]}")
        
        # Get a sample scan session
        result = db.execute(text("SELECT id, owner_id, created_at FROM scan_sessions LIMIT 1"))
        session_row = result.fetchone()
        if session_row:
            session_id, owner_id, created_at = session_row
            logger.info(f"Sample session: ID={session_id}, Owner={owner_id}, Created={created_at}")
            
            # Get scans in this session
            result = db.execute(text("""
                SELECT id, owner_id, status, input_content_type, created_at 
                FROM scans 
                WHERE scan_session_id = :session_id
                ORDER BY created_at ASC
            """), {"session_id": session_id})
            
            scans = result.fetchall()
            logger.info(f"Found {len(scans)} scans in session {session_id}")
            
            for i, scan in enumerate(scans):
                scan_id, scan_owner_id, status, content_type, scan_created_at = scan
                logger.info(f"Scan {i+1}: ID={scan_id}, Status={status}, Type={content_type}, Created={scan_created_at}")
                
                # Get scan analysis result
                result = db.execute(text("""
                    SELECT analysis_result, input_text
                    FROM scans
                    WHERE id = :scan_id
                """), {"scan_id": scan_id})
                
                scan_data = result.fetchone()
                if scan_data:
                    analysis_result, input_text = scan_data
                    
                    # Extract text from analysis result or input text
                    extracted_text = None
                    if analysis_result and isinstance(analysis_result, dict) and "extracted_text" in analysis_result:
                        extracted_text = analysis_result["extracted_text"]
                    elif input_text:
                        extracted_text = input_text
                    
                    if extracted_text:
                        logger.info(f"Scan {i+1} text: {extracted_text[:100]}...")
                    
                    # Check for session assessment in analysis result
                    if analysis_result and isinstance(analysis_result, dict) and "overall_session_assessment" in analysis_result:
                        session_assessment = analysis_result["overall_session_assessment"]
                        if session_assessment:
                            logger.info(f"Scan {i+1} session assessment: {session_assessment[:100]}...")
        else:
            logger.warning("No scan sessions found in the database")
    
    except Exception as e:
        logger.error(f"Error in test_session_context: {str(e)}")
    
    finally:
        db.close()

if __name__ == "__main__":
    test_session_context()
