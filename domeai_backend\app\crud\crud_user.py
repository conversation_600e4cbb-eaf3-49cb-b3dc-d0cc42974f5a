from typing import Optional, List
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy import or_

from app.core.security import get_password_hash, verify_password
from app.models.user import User
from app.schemas.user import UserCreate
from app.core.config import settings


def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """
    Get a user by email.

    Args:
        db: Database session.
        email: Email of the user to get.

    Returns:
        Optional[User]: The user if found, None otherwise.
    """
    return db.query(User).filter(User.email == email).first()


def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
    """
    Get a user by ID.

    Args:
        db: Database session.
        user_id: ID of the user to get.

    Returns:
        Optional[User]: The user if found, None otherwise.
    """
    return db.query(User).filter(User.id == user_id).first()


def create_user(db: Session, user_in: UserCreate) -> User:
    """
    Create a new user.

    Args:
        db: Database session.
        user_in: User creation data.

    Returns:
        User: The created user.
    """
    hashed_password = get_password_hash(user_in.password)

    # HOSTINGER HORIZONS CONTEST: Default new users to premium tier
    if settings.HOSTINGER_HORIZONS_CONTEST_MODE:
        subscription_tier = "premium"
        monthly_scan_allowance = 100
        expert_scan_allowance = 0
    else:
        # Normal behavior: default to free tier
        subscription_tier = "free"
        monthly_scan_allowance = 5
        expert_scan_allowance = 0

    db_user = User(
        email=user_in.email,
        hashed_password=hashed_password,
        is_active=True,
        subscription_tier=subscription_tier,
        monthly_scan_allowance=monthly_scan_allowance,
        expert_scan_allowance=expert_scan_allowance,
        scans_this_month=0,
        expert_scans_this_month=0,
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def authenticate_user(db: Session, email: str, password: str) -> Optional[User]:
    """
    Authenticate a user.

    Args:
        db: Database session.
        email: Email of the user to authenticate.
        password: Password to verify.

    Returns:
        Optional[User]: The authenticated user if successful, None otherwise.
    """
    user = get_user_by_email(db, email=email)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user


def get_user_by_google_play_purchase_token(db: Session, purchase_token: str) -> Optional[User]:
    """
    Get a user by Google Play purchase token.

    Args:
        db: Database session.
        purchase_token: Google Play purchase token.

    Returns:
        Optional[User]: The user if found, None otherwise.
    """
    return db.query(User).filter(User.google_play_purchase_token == purchase_token).first()


def get_user_by_provider_subscription_id(db: Session, provider_subscription_id: str) -> Optional[User]:
    """
    Get a user by provider subscription ID.

    Args:
        db: Database session.
        provider_subscription_id: Provider subscription ID (e.g., Google Play order ID).

    Returns:
        Optional[User]: The user if found, None otherwise.
    """
    return db.query(User).filter(User.provider_subscription_id == provider_subscription_id).first()


def get_users_by_subscription_tier(db: Session, subscription_tier: str) -> List[User]:
    """
    Get all users with a specific subscription tier.

    Args:
        db: Database session.
        subscription_tier: Subscription tier to filter by.

    Returns:
        List[User]: List of users with the specified subscription tier.
    """
    return db.query(User).filter(User.subscription_tier == subscription_tier).all()


def get_users_with_expiring_subscriptions(db: Session, expiry_date: datetime) -> List[User]:
    """
    Get all users whose subscriptions are expiring on or before the specified date.

    Args:
        db: Database session.
        expiry_date: Date to check for expiring subscriptions.

    Returns:
        List[User]: List of users with expiring subscriptions.
    """
    return db.query(User).filter(User.subscription_expiry_date <= expiry_date).all()
