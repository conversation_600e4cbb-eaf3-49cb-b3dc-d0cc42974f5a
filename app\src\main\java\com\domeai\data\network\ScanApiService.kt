package com.domeai.data.network

import com.domeai.data.model.network.ScanDetailsRequestData
import com.domeai.data.model.network.ScanResponse
import com.domeai.data.model.network.ScanSubmissionPayload
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * API service for scan operations
 */
interface ScanApiService {
    /**
     * Submit a text or URL scan
     */
    @POST("api/v1/scans/")
    suspend fun submitTextOrUrlScan(
        @Header("Authorization") token: String,
        @Body payload: ScanSubmissionPayload,
        @Query("use_expert_scan") useExpertScan: Boolean = false
    ): Response<ScanResponse>

    /**
     * Submit an image scan
     */
    @Multipart
    @POST("api/v1/scans/upload-image/")
    suspend fun submitImageScan(
        @Header("Authorization") token: String,
        @Part file: MultipartBody.Part,
        @Part("user_provided_context") userContext: RequestBody?,
        @Part("scan_session_id") sessionId: RequestBody?,
        @Part("use_expert_scan") useExpertScan: RequestBody? = null
    ): Response<ScanResponse>

    /**
     * Get scan details by ID
     */
    @GET("api/v1/scans/{scan_id}")
    suspend fun getScanDetails(
        @Header("Authorization") token: String,
        @Path("scan_id") scanId: Int
    ): Response<ScanResponse>
}
