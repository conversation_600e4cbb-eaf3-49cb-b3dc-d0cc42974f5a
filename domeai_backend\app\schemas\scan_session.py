import uuid
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel

from app.schemas.scan import Scan


class ScanSessionBase(BaseModel):
    """Base schema for scan sessions."""
    title: Optional[str] = None


class ScanSessionCreate(ScanSessionBase):
    """Schema for creating a new scan session."""
    pass


class ScanSessionUpdate(ScanSessionBase):
    """Schema for updating a scan session."""
    pass


class ScanSession(ScanSessionBase):
    """Schema for returning scan session information."""
    id: uuid.UUID
    owner_id: int
    created_at: datetime
    last_activity_at: datetime
    scans: List[Scan] = []

    class Config:
        orm_mode = True
