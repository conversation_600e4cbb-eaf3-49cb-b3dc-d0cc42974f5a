"""convert_embedding_to_pgvector

Revision ID: convert_embedding_to_pgvector
Revises: 422bc7616ce1
Create Date: 2025-05-12 21:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'convert_embedding_to_pgvector'
down_revision = '422bc7616ce1'
branch_labels = None
depends_on = None


def upgrade():
    # Ensure the vector extension is enabled
    op.execute('CREATE EXTENSION IF NOT EXISTS vector;')
    
    # Drop the existing GIN index on the embedding column
    op.execute('DROP INDEX IF EXISTS knowledge_base_chunks_embedding_idx;')
    
    # Create a temporary table with the vector column
    op.execute('''
    CREATE TABLE knowledge_base_chunks_temp (
        id SERIAL PRIMARY KEY,
        content TEXT NOT NULL,
        embedding vector(1536) NOT NULL,
        source VARCHAR,
        created_at TIMESTAMP DEFAULT NOW()
    );
    ''')
    
    # Copy data from the original table to the temporary table, converting the embedding
    op.execute('''
    INSERT INTO knowledge_base_chunks_temp (id, content, embedding, source, created_at)
    SELECT id, content, embedding::vector(1536), source, created_at
    FROM knowledge_base_chunks;
    ''')
    
    # Drop the original table
    op.execute('DROP TABLE knowledge_base_chunks;')
    
    # Rename the temporary table to the original table name
    op.execute('ALTER TABLE knowledge_base_chunks_temp RENAME TO knowledge_base_chunks;')
    
    # Create a sequence for the id column
    op.execute('''
    CREATE SEQUENCE IF NOT EXISTS knowledge_base_chunks_id_seq
    OWNED BY knowledge_base_chunks.id;
    ''')
    
    # Set the default value for the id column
    op.execute('''
    ALTER TABLE knowledge_base_chunks
    ALTER COLUMN id SET DEFAULT nextval('knowledge_base_chunks_id_seq');
    ''')
    
    # Create an index on the id column
    op.execute('CREATE INDEX knowledge_base_chunks_id_idx ON knowledge_base_chunks (id);')
    
    # Create a pgvector index on the embedding column
    op.execute('''
    CREATE INDEX knowledge_base_chunks_embedding_idx
    ON knowledge_base_chunks
    USING ivfflat (embedding vector_cosine_ops)
    WITH (lists = 100);
    ''')


def downgrade():
    # Drop the pgvector index
    op.execute('DROP INDEX IF EXISTS knowledge_base_chunks_embedding_idx;')
    
    # Create a temporary table with the double precision[] column
    op.execute('''
    CREATE TABLE knowledge_base_chunks_temp (
        id SERIAL PRIMARY KEY,
        content TEXT NOT NULL,
        embedding DOUBLE PRECISION[] NOT NULL,
        source VARCHAR,
        created_at TIMESTAMP DEFAULT NOW()
    );
    ''')
    
    # Copy data from the original table to the temporary table, converting the embedding
    op.execute('''
    INSERT INTO knowledge_base_chunks_temp (id, content, embedding, source, created_at)
    SELECT id, content, embedding::float8[], source, created_at
    FROM knowledge_base_chunks;
    ''')
    
    # Drop the original table
    op.execute('DROP TABLE knowledge_base_chunks;')
    
    # Rename the temporary table to the original table name
    op.execute('ALTER TABLE knowledge_base_chunks_temp RENAME TO knowledge_base_chunks;')
    
    # Create a sequence for the id column
    op.execute('''
    CREATE SEQUENCE IF NOT EXISTS knowledge_base_chunks_id_seq
    OWNED BY knowledge_base_chunks.id;
    ''')
    
    # Set the default value for the id column
    op.execute('''
    ALTER TABLE knowledge_base_chunks
    ALTER COLUMN id SET DEFAULT nextval('knowledge_base_chunks_id_seq');
    ''')
    
    # Create an index on the id column
    op.execute('CREATE INDEX knowledge_base_chunks_id_idx ON knowledge_base_chunks (id);')
    
    # Create a GIN index on the embedding column
    op.execute('CREATE INDEX knowledge_base_chunks_embedding_idx ON knowledge_base_chunks USING gin (embedding);')
