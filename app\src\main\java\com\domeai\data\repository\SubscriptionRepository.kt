package com.domeai.data.repository

import com.domeai.data.model.Subscription
import com.domeai.data.model.PaymentRecord
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for subscription-related operations
 */
interface SubscriptionRepository {
    /**
     * Get the current subscription details
     *
     * @param forceRefresh If true, forces a refresh from the backend API
     */
    suspend fun getCurrentSubscription(forceRefresh: Boolean = false): Subscription

    /**
     * Update the subscription plan
     */
    suspend fun updateSubscriptionPlan(planName: String): Subscription

    /**
     * Toggle auto-renew setting
     */
    suspend fun setAutoRenew(enabled: Boolean): Subscription

    /**
     * Cancel the current subscription
     */
    suspend fun cancelSubscription(): Subscription

    /**
     * Get payment history
     */
    suspend fun getPaymentHistory(): List<PaymentRecord>

    /**
     * Get payment history as a Flow
     */
    fun getPaymentHistoryFlow(): Flow<List<PaymentRecord>>
}
