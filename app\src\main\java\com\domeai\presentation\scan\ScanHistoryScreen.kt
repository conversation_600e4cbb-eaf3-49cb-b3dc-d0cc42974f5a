package com.domeai.presentation.scan

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.AssistChip
import androidx.compose.material3.AssistChipDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SearchBar
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.domeai.data.model.RiskLevel
import com.domeai.data.model.ScanResult
import com.domeai.data.model.ScanSourceType
import com.domeai.data.model.ScanType
import com.domeai.data.model.content
import com.domeai.data.model.description
import com.domeai.data.model.getDate
import com.domeai.data.model.isFavorite
import com.domeai.data.model.title
import com.domeai.data.model.type
import com.domeai.ui.composables.EmptyScanHistory
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScanHistoryScreen(
    onNavigateBack: () -> Unit,
    onScanClick: (String) -> Unit,
    viewModel: ScanHistoryViewModel = hiltViewModel()
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Scan History") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        ScanHistoryContent(
            onScanClick = onScanClick,
            showBackButton = true,
            modifier = Modifier.padding(paddingValues)
        )
    }
}

/**
 * The main content of the scan history screen, extracted to be reusable
 * in both the dedicated screen and the tab view
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScanHistoryContent(
    onScanClick: (String) -> Unit,
    showBackButton: Boolean = true,
    modifier: Modifier = Modifier,
    viewModel: ScanHistoryViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Search state
    var searchActive by remember { mutableStateOf(false) }

    // Filter dropdown state
    var showFilterDropdown by remember { mutableStateOf(false) }

    // Show error in snackbar if present
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            snackbarHostState.showSnackbar(it)
        }
    }

    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // Top actions row
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.End
        ) {
            if (!showBackButton) {
                Text(
                    text = "Scan History",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
            }

            IconButton(onClick = { searchActive = !searchActive }) {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = "Search"
                )
            }

            IconButton(onClick = { showFilterDropdown = true }) {
                Icon(
                    imageVector = Icons.Default.FilterList,
                    contentDescription = "Filter"
                )

                DropdownMenu(
                    expanded = showFilterDropdown,
                    onDismissRequest = { showFilterDropdown = false }
                ) {
                    Text(
                        text = "Filter By:",
                        style = MaterialTheme.typography.titleSmall,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                    )

                    Divider()

                    // Risk Level filters
                    DropdownMenuItem(
                        text = { Text("All Risk Levels") },
                        onClick = {
                            viewModel.handleAction(ScanHistoryAction.FilterByRiskLevel(null))
                            showFilterDropdown = false
                        }
                    )

                    RiskLevel.values().forEach { riskLevel ->
                        DropdownMenuItem(
                            text = { Text(riskLevel.name.replace("_", " ")) },
                            onClick = {
                                viewModel.handleAction(ScanHistoryAction.FilterByRiskLevel(riskLevel))
                                showFilterDropdown = false
                            }
                        )
                    }

                    Divider()

                    // Scan Type filters
                    DropdownMenuItem(
                        text = { Text("All Scan Types") },
                        onClick = {
                            viewModel.handleAction(ScanHistoryAction.FilterByScanType(null))
                            showFilterDropdown = false
                        }
                    )

                    ScanType.values().forEach { scanType ->
                        DropdownMenuItem(
                            text = { Text(scanType.name) },
                            onClick = {
                                viewModel.handleAction(ScanHistoryAction.FilterByScanType(scanType))
                                showFilterDropdown = false
                            }
                        )
                    }

                    Divider()

                    // Favorites filter
                    DropdownMenuItem(
                        text = { Text("Favorites Only") },
                        onClick = {
                            viewModel.handleAction(ScanHistoryAction.ToggleFavoriteFilter(true))
                            showFilterDropdown = false
                        }
                    )

                    DropdownMenuItem(
                        text = { Text("Clear All Filters") },
                        onClick = {
                            viewModel.handleAction(ScanHistoryAction.ClearFilters)
                            showFilterDropdown = false
                        }
                    )
                }
            }
        }

        // Search bar
        AnimatedVisibility(
            visible = searchActive,
            enter = fadeIn() + expandVertically(),
            exit = fadeOut() + shrinkVertically()
        ) {
            SearchBar(
                query = uiState.filter.searchQuery,
                onQueryChange = { viewModel.handleAction(ScanHistoryAction.Search(it)) },
                onSearch = { searchActive = false },
                active = true,
                onActiveChange = { searchActive = it },
                placeholder = { Text("Search scans...") },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "Search Icon"
                    )
                },
                trailingIcon = {
                    if (uiState.filter.searchQuery.isNotEmpty()) {
                        IconButton(onClick = { viewModel.handleAction(ScanHistoryAction.Search("")) }) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "Clear Search"
                            )
                        }
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                // Search suggestions could go here
            }
        }

        // Active filters
        if (uiState.filter != ScanHistoryFilter()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                uiState.filter.riskLevel?.let { riskLevel ->
                    FilterChip(
                        selected = true,
                        onClick = { viewModel.handleAction(ScanHistoryAction.FilterByRiskLevel(null)) },
                        label = { Text("Risk: ${riskLevel.name.replace("_", " ")}") },
                        trailingIcon = {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "Clear Filter"
                            )
                        }
                    )
                }

                uiState.filter.scanType?.let { scanType ->
                    FilterChip(
                        selected = true,
                        onClick = { viewModel.handleAction(ScanHistoryAction.FilterByScanType(null)) },
                        label = { Text("Type: ${scanType.name}") },
                        trailingIcon = {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "Clear Filter"
                            )
                        }
                    )
                }

                if (uiState.filter.favoritesOnly) {
                    FilterChip(
                        selected = true,
                        onClick = { viewModel.handleAction(ScanHistoryAction.ToggleFavoriteFilter(false)) },
                        label = { Text("Favorites Only") },
                        trailingIcon = {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "Clear Filter"
                            )
                        }
                    )
                }

                if (uiState.filter.searchQuery.isNotBlank() && !searchActive) {
                    FilterChip(
                        selected = true,
                        onClick = { viewModel.handleAction(ScanHistoryAction.Search("")) },
                        label = { Text("Search: ${uiState.filter.searchQuery}") },
                        trailingIcon = {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "Clear Filter"
                            )
                        }
                    )
                }
            }
        }

        // Content
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
        ) {
            if (uiState.isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else if (uiState.scans.isEmpty()) {
                EmptyScanHistory(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    item {
                        Spacer(modifier = Modifier.height(8.dp))
                    }

                    items(
                        items = uiState.scans,
                        key = { it.id }
                    ) { scan ->
                        // Use the original styling but with enhanced functionality
                        ScanHistoryItem(
                            scan = scan,
                            onClick = { onScanClick(scan.id) },
                            onFavoriteClick = { viewModel.handleAction(ScanHistoryAction.ToggleFavorite(scan.id)) },
                            onDeleteClick = { viewModel.handleAction(ScanHistoryAction.DeleteScan(scan.id)) }
                        )
                    }

                    item {
                        Spacer(modifier = Modifier.height(16.dp))
                    }
                }
            }
        }

        // Snackbar host
        SnackbarHost(
            hostState = snackbarHostState,
            modifier = Modifier.align(Alignment.CenterHorizontally)
        )
    }
}

@Composable
fun ScanHistoryItem(
    scan: ScanResult,
    onClick: () -> Unit,
    onFavoriteClick: () -> Unit = {},
    onDeleteClick: () -> Unit = {}
) {
    val dateFormat = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
    val formattedDate = dateFormat.format(Date(scan.timestamp))

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Risk level indicator (using the original styling)
            com.domeai.ui.composables.RiskLevelIndicator(riskLevel = scan.riskLevel)

            Spacer(modifier = Modifier.width(16.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = when (scan.sourceType) {
                        ScanSourceType.OVERLAY_SCREENSHOT -> "Screenshot Scan"
                        ScanSourceType.MANUAL_TEXT -> "Text Scan"
                        ScanSourceType.MANUAL_IMAGE -> "Image Scan"
                    },
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = scan.sourceContent ?: "No content preview available",
                    style = MaterialTheme.typography.bodySmall,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = formattedDate,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
            }

            // Add favorite and delete buttons
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Row {
                    // Favorite button
                    IconButton(
                        onClick = onFavoriteClick,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = if (scan.isFavorite) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                            contentDescription = if (scan.isFavorite) "Remove from Favorites" else "Add to Favorites",
                            tint = if (scan.isFavorite) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }

                    // Delete button
                    IconButton(
                        onClick = onDeleteClick,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "Delete Scan",
                            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }

                Text(
                    text = "${scan.riskScore}%",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = com.domeai.ui.composables.getRiskColor(scan.riskLevel)
                )

                Text(
                    text = scan.riskLevel.name.replace("_", " "),
                    style = MaterialTheme.typography.bodySmall,
                    color = com.domeai.ui.composables.getRiskColor(scan.riskLevel)
                )
            }
        }
    }
}

@Composable
fun getRiskLevelColor(riskLevel: RiskLevel): Color {
    return when (riskLevel) {
        RiskLevel.SAFE -> Color(0xFF4CAF50)         // Green
        RiskLevel.LOW_RISK -> Color(0xFF8BC34A)     // Light Green
        RiskLevel.MEDIUM_RISK -> Color(0xFFFFC107)  // Amber
        RiskLevel.HIGH_RISK -> Color(0xFFFF9800)    // Orange
    }
}
