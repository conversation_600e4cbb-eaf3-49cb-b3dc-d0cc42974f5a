"""Add scan sessions

Revision ID: 005
Revises: 002
Create Date: 2025-05-13 06:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID
import uuid


# revision identifiers, used by Alembic.
revision = '005'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade():
    # Create scan_sessions table
    op.create_table(
        'scan_sessions',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True),
        sa.Column('owner_id', sa.Integer, sa.ForeignKey('users.id'), nullable=False, index=True),
        sa.Column('title', sa.String, nullable=True),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now(), nullable=False),
        sa.Column('last_activity_at', sa.DateTime, server_default=sa.func.now(), nullable=False),
    )

    # Add scan_session_id column to scans table
    op.add_column(
        'scans',
        sa.<PERSON>umn('scan_session_id', UUID(as_uuid=True), sa.<PERSON>('scan_sessions.id'), nullable=True, index=True)
    )


def downgrade():
    # Remove scan_session_id column from scans table
    op.drop_column('scans', 'scan_session_id')

    # Drop scan_sessions table
    op.drop_table('scan_sessions')
