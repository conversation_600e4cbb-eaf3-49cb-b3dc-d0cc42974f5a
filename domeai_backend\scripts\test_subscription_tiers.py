#!/usr/bin/env python
"""
Script to test the subscription tier functionality.
"""

import asyncio
import logging
import sys
from datetime import datetime, timezone

# Add the parent directory to the path so we can import from app
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.ai_service_factory import get_ai_service
from app.models.user import User

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

async def test_basic_tier():
    """Test the basic tier AI service."""
    logger.info("Testing Basic Tier AI Service")
    
    # Get the basic tier AI service
    ai_service = get_ai_service(user_tier="free")
    
    # Test multimodal analysis with text
    text_input = "Hi, I saw your listing and I'm very interested in buying it. I'm currently out of the country for work but I want to purchase it right away. I'll pay extra for shipping and handling. I'll send payment via PayPal and arrange a shipping company to pick it up. Please take down the listing and I'll pay an extra $50 for your trouble. Let me know your PayPal email so I can send the money right away."
    
    logger.info("Testing multimodal analysis with text input")
    result = await ai_service.get_multimodal_analysis(text_input=text_input)
    
    logger.info(f"Model used: {ai_service.model}")
    logger.info(f"Embedding model: {ai_service.embedding_model}")
    logger.info(f"Embedding dimensions: {ai_service.embedding_dimensions}")
    
    # Log the result
    logger.info(f"Result: {result}")
    
    return result

async def test_premium_tier():
    """Test the premium tier AI service."""
    logger.info("Testing Premium Tier AI Service")
    
    # Get the premium tier AI service
    ai_service = get_ai_service(user_tier="premium")
    
    # Test multimodal analysis with text
    text_input = "Hi, I saw your listing and I'm very interested in buying it. I'm currently out of the country for work but I want to purchase it right away. I'll pay extra for shipping and handling. I'll send payment via PayPal and arrange a shipping company to pick it up. Please take down the listing and I'll pay an extra $50 for your trouble. Let me know your PayPal email so I can send the money right away."
    
    logger.info("Testing multimodal analysis with text input")
    result = await ai_service.get_multimodal_analysis(text_input=text_input)
    
    logger.info(f"Model used: {ai_service.model}")
    logger.info(f"Embedding model: {ai_service.embedding_model}")
    logger.info(f"Embedding dimensions: {ai_service.embedding_dimensions}")
    
    # Log the result
    logger.info(f"Result: {result}")
    
    return result

def test_user_scan_limits():
    """Test the user scan limits."""
    logger.info("Testing User Scan Limits")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create a test user with free tier (5 scans per month)
        test_user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            subscription_tier="free",
            monthly_scan_allowance=5,
            scans_this_month=0,
            scan_counter_reset_at=datetime.now(timezone.utc)
        )
        
        # Add the user to the database
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        logger.info(f"Created test user with ID {test_user.id}")
        logger.info(f"Subscription tier: {test_user.subscription_tier}")
        logger.info(f"Monthly scan allowance: {test_user.monthly_scan_allowance}")
        logger.info(f"Scans this month: {test_user.scans_this_month}")
        
        # Simulate submitting scans
        for i in range(1, 7):  # Try to submit 6 scans (1 more than the limit)
            if test_user.scans_this_month >= test_user.monthly_scan_allowance:
                logger.info(f"Scan {i}: Monthly scan limit reached")
                break
            
            test_user.scans_this_month += 1
            db.commit()
            logger.info(f"Scan {i}: Submitted successfully")
            logger.info(f"Scans this month: {test_user.scans_this_month}")
        
        # Clean up
        db.delete(test_user)
        db.commit()
        logger.info("Test user deleted")
        
    finally:
        db.close()

async def main():
    """Run the tests."""
    # Test the basic tier AI service
    await test_basic_tier()
    
    # Test the premium tier AI service
    await test_premium_tier()
    
    # Test the user scan limits
    test_user_scan_limits()

if __name__ == "__main__":
    asyncio.run(main())
