#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test the knowledge base retrieval.
"""

import asyncio
import logging
import sys

# Add the parent directory to the path so we can import from app
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.database import SessionLocal
from app.services.ai_services import OpenAIModelService
from app.core.config import settings
from app.crud import crud_kb

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

async def test_cashier_check_scam():
    """Test the knowledge base retrieval with a cashier's check scam scenario."""
    db = SessionLocal()
    try:
        # Initialize the OpenAI service
        ai_service = OpenAIModelService(api_key=settings.OPENAI_API_KEY)
        
        # Cashier's check scam text
        text_content = """
        Hey there! I'm interested in your item on OfferUp. I'd like to buy it right away. I'll send you a cashier's check for $1,500. That's $500 more than your asking price. Once you deposit it, just send me back the extra $500 via Western Union. I'm out of town for work, so I can't pick it up in person. I'll arrange shipping once you confirm. Let me know if this works for you!
        """
        
        # Generate text embedding
        logger.info("Generating text embedding...")
        text_embedding = await ai_service.get_text_embedding(text=text_content)
        
        # Find similar KB chunks
        logger.info("Finding similar KB chunks...")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=text_embedding,
            top_k=3
        )
        
        # Log the similar chunks
        logger.info(f"Found {len(similar_chunks)} similar chunks:")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Chunk {i+1}: ID {chunk.id}, Source: {chunk.source}")
            logger.info(f"Similarity score: {getattr(chunk, 'similarity_score', 'N/A')}")
            logger.info(f"Content: {chunk.content[:150]}...")
        
        return {
            "similar_chunks": [
                {
                    "id": chunk.id,
                    "source": chunk.source,
                    "similarity_score": getattr(chunk, "similarity_score", None),
                    "content": chunk.content[:150] + "..."
                }
                for chunk in similar_chunks
            ]
        }
    
    finally:
        db.close()

async def test_remote_buyer_scam():
    """Test the knowledge base retrieval with a remote buyer scam scenario."""
    db = SessionLocal()
    try:
        # Initialize the OpenAI service
        ai_service = OpenAIModelService(api_key=settings.OPENAI_API_KEY)
        
        # Remote buyer scam text
        text_content = """
        Hi, I saw your listing and I'm very interested in buying it. I'm currently out of the country for work but I want to purchase it right away. I'll pay extra for shipping and handling. I'll send payment via PayPal and arrange a shipping company to pick it up. Please take down the listing and I'll pay an extra $50 for your trouble. Let me know your PayPal email so I can send the money right away.
        """
        
        # Generate text embedding
        logger.info("Generating text embedding...")
        text_embedding = await ai_service.get_text_embedding(text=text_content)
        
        # Find similar KB chunks
        logger.info("Finding similar KB chunks...")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=text_embedding,
            top_k=3
        )
        
        # Log the similar chunks
        logger.info(f"Found {len(similar_chunks)} similar chunks:")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Chunk {i+1}: ID {chunk.id}, Source: {chunk.source}")
            logger.info(f"Similarity score: {getattr(chunk, 'similarity_score', 'N/A')}")
            logger.info(f"Content: {chunk.content[:150]}...")
        
        return {
            "similar_chunks": [
                {
                    "id": chunk.id,
                    "source": chunk.source,
                    "similarity_score": getattr(chunk, "similarity_score", None),
                    "content": chunk.content[:150] + "..."
                }
                for chunk in similar_chunks
            ]
        }
    
    finally:
        db.close()

async def test_tech_support_scam():
    """Test the knowledge base retrieval with a tech support scam scenario."""
    db = SessionLocal()
    try:
        # Initialize the OpenAI service
        ai_service = OpenAIModelService(api_key=settings.OPENAI_API_KEY)
        
        # Tech support scam text
        text_content = """
        WARNING! Your computer is infected with 3 viruses! Call Microsoft Support Immediately at ************** to prevent data loss. Your personal information is at risk. If you close this window, your computer will be disabled. Our certified Microsoft technicians can remove the viruses remotely for a one-time fee of $299.99. Act now to protect your data!
        """
        
        # Generate text embedding
        logger.info("Generating text embedding...")
        text_embedding = await ai_service.get_text_embedding(text=text_content)
        
        # Find similar KB chunks
        logger.info("Finding similar KB chunks...")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=text_embedding,
            top_k=3
        )
        
        # Log the similar chunks
        logger.info(f"Found {len(similar_chunks)} similar chunks:")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Chunk {i+1}: ID {chunk.id}, Source: {chunk.source}")
            logger.info(f"Similarity score: {getattr(chunk, 'similarity_score', 'N/A')}")
            logger.info(f"Content: {chunk.content[:150]}...")
        
        return {
            "similar_chunks": [
                {
                    "id": chunk.id,
                    "source": chunk.source,
                    "similarity_score": getattr(chunk, "similarity_score", None),
                    "content": chunk.content[:150] + "..."
                }
                for chunk in similar_chunks
            ]
        }
    
    finally:
        db.close()

async def main():
    """Run all test scenarios."""
    logger.info("Testing cashier's check scam scenario...")
    cashier_check_results = await test_cashier_check_scam()
    
    logger.info("\nTesting remote buyer scam scenario...")
    remote_buyer_results = await test_remote_buyer_scam()
    
    logger.info("\nTesting tech support scam scenario...")
    tech_support_results = await test_tech_support_scam()
    
    logger.info("\nAll tests completed.")

if __name__ == "__main__":
    asyncio.run(main())
