package com.domeai.data.model

/**
 * Data class representing user credentials for login
 */
data class LoginCredentials(
    val email: String,
    val password: String
)

/**
 * Data class representing user information for registration
 */
data class SignUpCredentials(
    val email: String,
    val password: String,
    val confirmPassword: String,
    val acceptedTerms: Boolean = false
)

/**
 * Data class representing a request to reset password
 */
data class ForgotPasswordRequest(
    val email: String
)

/**
 * Data class representing the authenticated user
 */
data class User(
    val id: String,
    val email: String,
    val name: String? = null,
    val profilePictureUrl: String? = null,
    val isEmailVerified: Boolean = false,
    val isPremium: Boolean = false
)

/**
 * Sealed class representing the result of authentication operations
 */
sealed class AuthResult {
    data class Success(val user: User) : AuthResult()
    data class Error(val message: String) : AuthResult()
    data object Loading : AuthResult()
    data object Idle : AuthResult()
}

/**
 * Sealed class representing the validation result for input fields
 */
sealed class ValidationResult {
    data object Valid : ValidationResult()
    data class Invalid(val errorMessage: String) : ValidationResult()
}
