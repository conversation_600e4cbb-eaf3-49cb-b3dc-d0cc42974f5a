import asyncio
import psycopg2
import sys
import json
sys.path.insert(0, '/app')
from app.services.ai_services import OpenAIModelService
import os

async def load_full_kb():
    # Load the complete knowledge base
    with open('/app/scripts/cleaned_kb_chunks.json', 'r') as f:
        chunks = json.load(f)
    
    print(f'Loading {len(chunks)} knowledge base chunks...')
    
    ai_service = OpenAIModelService(api_key=os.getenv('OPENAI_API_KEY'))
    conn = psycopg2.connect(host='db', port='5432', database='dome_app_main_db', user='dome_api_user', password='Dome2025ApiSecure!')
    cur = conn.cursor()
    
    # Clear existing chunks first
    cur.execute('DELETE FROM knowledge_base_chunks')
    conn.commit()
    print('Cleared existing chunks')
    
    successful = 0
    failed = 0
    
    try:
        for i, chunk in enumerate(chunks):
            try:
                print(f'Processing chunk {i+1}/{len(chunks)}: {chunk["source"]}')
                
                # Generate embedding
                embedding = await ai_service.get_text_embedding(text=chunk['content'])
                embedding_str = '[' + ','.join(map(str, embedding)) + ']'
                
                # Insert into database
                cur.execute('INSERT INTO knowledge_base_chunks (content, embedding, source, created_at) VALUES (%s, %s::vector(1536), %s, now())', (chunk['content'], embedding_str, chunk['source']))
                successful += 1
                print(f'Inserted chunk {i+1}')
                
                # Commit every 10 chunks to avoid losing progress
                if (i + 1) % 10 == 0:
                    conn.commit()
                    print(f'Committed batch {i+1}')
                
            except Exception as e:
                print(f'Error processing chunk {i+1}: {e}')
                failed += 1
                conn.rollback()
                continue
        
        # Final commit
        conn.commit()
        
        # Verify total count
        cur.execute('SELECT COUNT(*) FROM knowledge_base_chunks')
        count = cur.fetchone()[0]
        print(f'Knowledge base loaded successfully!')
        print(f'Total chunks in database: {count}')
        print(f'Successful: {successful}, Failed: {failed}')
        
    except Exception as e:
        print(f'Error: {e}')
        conn.rollback()
    finally:
        cur.close()
        conn.close()

if __name__ == '__main__':
    asyncio.run(load_full_kb())
