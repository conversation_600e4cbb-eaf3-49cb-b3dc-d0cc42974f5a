package com.domeai.data.model

import java.util.Date

/**
 * Represents the type of content that was scanned
 */
enum class ScanType {
    URL,
    TEXT,
    EMAIL,
    PHONE,
    APP,
    UNKNOWN
}

/**
 * Extension properties for ScanResult to make it easier to work with
 */
val ScanResult.title: String
    get() = when (riskLevel) {
        RiskLevel.SAFE -> "Safe Content"
        RiskLevel.LOW_RISK -> "Low Risk Content"
        RiskLevel.MEDIUM_RISK -> "Medium Risk Content"
        RiskLevel.HIGH_RISK -> "High Risk Content"
    }

val ScanResult.description: String
    get() = explanation

val ScanResult.content: String
    get() = sourceContent ?: "No content available"

val ScanResult.type: ScanType
    get() = when (sourceType) {
        ScanSourceType.OVERLAY_SCREENSHOT -> ScanType.TEXT
        ScanSourceType.MANUAL_TEXT -> ScanType.TEXT
        ScanSourceType.MANUAL_IMAGE -> ScanType.TEXT
    }

val ScanResult.isFavorite: Boolean
    get() = false // We'll implement this later

/**
 * Extension function to convert timestamp to Date
 */
fun ScanResult.getDate(): Date = Date(timestamp)

/**
 * Represents a specific detection detail within a scan result
 */
data class DetectionDetail(
    val id: String,
    val title: String,
    val description: String,
    val riskLevel: RiskLevel,
    val evidence: String? = null
)
