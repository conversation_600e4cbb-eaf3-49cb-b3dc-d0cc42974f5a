"""
Minimal test script for verifying the Scan Session AI Context Utilization feature.
"""

import asyncio
import logging
import os
import sys
import time
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan import Scan
from app.services.ai_service_factory import get_ai_service
from app import crud

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Test data
TEST_TEXT_1 = "This is a test message from a dating app where someone is asking for inappropriate photos and refusing to video call."
TEST_TEXT_2 = "This is a follow-up message with more pressure to send intimate photos and excuses about why they can't video call."
TEST_TEXT_3 = "This is a third message with emotional manipulation and urgency tactics to get photos."
TEST_TEXT_4 = "This is a final message with aggressive demands and hostile reaction to continued refusal."

USER_CONTEXT = "They almost got me. My intuition is almost on point. I was so close too. I'm on a dating app and matched with someone who seemed too good to be true."

def create_premium_user(db):
    """Create a premium tier test user."""
    unique_email = f"test_premium_user_{int(time.time())}@example.com"
    test_user = User(
        email=unique_email,
        hashed_password="hashed_password",
        subscription_tier="premium",
        monthly_scan_allowance=100,
        scans_this_month=0,
        scan_counter_reset_at=datetime.now(timezone.utc)
    )
    db.add(test_user)
    db.commit()
    db.refresh(test_user)
    logger.info(f"Created premium test user with ID {test_user.id}")
    return test_user

async def create_text_scan_in_session(db, user, session_id, text, user_provided_context=None, is_session_followup=True):
    """Create a text scan in the specified session."""
    scan = Scan(
        owner_id=user.id,
        status="pending",
        input_content_type="text",
        input_text=text,
        user_provided_context=user_provided_context,
        raw_input_payload={"is_session_followup": is_session_followup},
        scan_session_id=session_id
    )
    
    db.add(scan)
    db.commit()
    db.refresh(scan)
    logger.info(f"Created scan {scan.id} in session {session_id}")
    
    return scan

async def process_scan_with_real_ai(db, scan, user, previous_scans=None):
    """Process a scan with the real AI service."""
    logger.info(f"Processing scan {scan.id} with real AI service")
    
    # Update scan status to "processing"
    scan.status = "processing"
    db.add(scan)
    db.commit()
    db.refresh(scan)
    
    # Get the AI service based on user's tier
    ai_service = get_ai_service(user_tier=user.subscription_tier)
    
    # Compile accumulated session context
    accumulated_session_context = None
    if previous_scans:
        context_parts = []
        
        for i, prev_scan in enumerate(previous_scans):
            # Extract text from previous scan
            prev_text = None
            if prev_scan.analysis_result and "extracted_text" in prev_scan.analysis_result:
                prev_text = prev_scan.analysis_result["extracted_text"]
            elif prev_scan.input_text:
                prev_text = prev_scan.input_text
            
            if prev_text:
                # Add scan number and text to context
                context_parts.append(f"[Scan {i+1}]: {prev_text}")
        
        # Join all parts into a single context string
        if context_parts:
            accumulated_session_context = "\n---\n".join(context_parts)
            logger.info(f"Compiled accumulated session context ({len(context_parts)} previous scans)")
            logger.info(f"Accumulated session context: {accumulated_session_context}")
    
    try:
        # For text scans, we can skip the multimodal analysis
        # Generate embedding
        logger.info(f"Getting text embedding for: {scan.input_text[:100]}...")
        embedding = await ai_service.get_text_embedding(text=scan.input_text)
        
        # Perform RAG analysis
        logger.info(f"Performing RAG analysis")
        rag_result = await ai_service.perform_scam_analysis_with_rag(
            query_text=scan.input_text,
            query_embedding=embedding,
            accumulated_session_context=accumulated_session_context,
            db=db
        )
        
        # Convert ScanResultData to dict
        rag_result_dict = rag_result.__dict__
        
        # Create analysis result
        analysis_result = {
            "extracted_text": scan.input_text,
            **rag_result_dict
        }
        
        # Update scan with result
        scan.status = "completed"
        scan.analysis_result = analysis_result
        db.add(scan)
        db.commit()
        db.refresh(scan)
        
        logger.info(f"Completed processing of scan {scan.id}")
        logger.info(f"Risk Score: {rag_result_dict.get('risk_score', 'N/A')}")
        logger.info(f"Detected Red Flags: {rag_result_dict.get('detected_red_flags', [])}")
        logger.info(f"Explanation: {rag_result_dict.get('explanation', '')[:200]}...")
        
        # Log overall session assessment if available
        if "overall_session_assessment" in rag_result_dict and rag_result_dict["overall_session_assessment"]:
            logger.info(f"Overall session assessment: {rag_result_dict['overall_session_assessment']}")
        else:
            logger.info("No overall session assessment provided")
        
        return analysis_result
        
    except Exception as e:
        logger.error(f"Error processing scan: {str(e)}", exc_info=True)
        
        # Update scan with error
        scan.status = "error"
        scan.error_message = str(e)
        db.add(scan)
        db.commit()
        db.refresh(scan)
        
        return {"error": str(e)}

async def execute_minimal_test():
    """Execute a minimal test of the scan session AI context utilization feature."""
    logger.info("Executing minimal test of scan session AI context utilization")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create a premium tier test user
        test_user = create_premium_user(db)
        
        # Create a scan session
        session = crud.crud_scan_session.create_scan_session(db=db, owner_id=test_user.id)
        session_id = session.id
        logger.info(f"Created scan session with ID {session_id}")
        
        # Track scan credits before
        scans_before = test_user.scans_this_month
        logger.info(f"Scans before: {scans_before}")
        
        # Process text messages sequentially
        all_scans = []
        
        # Process first message (not a follow-up)
        logger.info("\n=== Processing Message 1 ===\n")
        scan1 = await create_text_scan_in_session(
            db, 
            test_user, 
            session_id, 
            TEST_TEXT_1, 
            user_provided_context=USER_CONTEXT,
            is_session_followup=False
        )
        
        # Update session activity
        crud.crud_scan_session.update_scan_session_activity(db=db, db_session=session)
        
        # Update user scan count for first scan
        test_user.scans_this_month += 1
        db.commit()
        db.refresh(test_user)
        
        # Process the scan
        await process_scan_with_real_ai(db, scan1, test_user)
        all_scans.append(scan1)
        
        # Verify scan credit consumption for first scan
        db.refresh(test_user)
        logger.info(f"Scans after Message 1: {test_user.scans_this_month}")
        logger.info(f"Scan credit consumed: {test_user.scans_this_month - scans_before}")
        
        # Process Message 2
        logger.info("\n=== Processing Message 2 ===\n")
        scan2 = await create_text_scan_in_session(
            db, 
            test_user, 
            session_id, 
            TEST_TEXT_2,
            is_session_followup=True
        )
        
        # Update session activity
        crud.crud_scan_session.update_scan_session_activity(db=db, db_session=session)
        
        # Process the scan with previous scans context
        await process_scan_with_real_ai(db, scan2, test_user, all_scans)
        all_scans.append(scan2)
        
        # Verify scan credit consumption
        db.refresh(test_user)
        logger.info(f"Scans after Message 2: {test_user.scans_this_month}")
        logger.info(f"Additional scan credits consumed: {test_user.scans_this_month - (scans_before + 1)}")
        
        # Process Message 3
        logger.info("\n=== Processing Message 3 ===\n")
        scan3 = await create_text_scan_in_session(
            db, 
            test_user, 
            session_id, 
            TEST_TEXT_3,
            is_session_followup=True
        )
        
        # Update session activity
        crud.crud_scan_session.update_scan_session_activity(db=db, db_session=session)
        
        # Process the scan with previous scans context
        await process_scan_with_real_ai(db, scan3, test_user, all_scans)
        all_scans.append(scan3)
        
        # Verify scan credit consumption
        db.refresh(test_user)
        logger.info(f"Scans after Message 3: {test_user.scans_this_month}")
        logger.info(f"Additional scan credits consumed: {test_user.scans_this_month - (scans_before + 1)}")
        
        # Process Message 4
        logger.info("\n=== Processing Message 4 ===\n")
        scan4 = await create_text_scan_in_session(
            db, 
            test_user, 
            session_id, 
            TEST_TEXT_4,
            is_session_followup=True
        )
        
        # Update session activity
        crud.crud_scan_session.update_scan_session_activity(db=db, db_session=session)
        
        # Process the scan with previous scans context
        await process_scan_with_real_ai(db, scan4, test_user, all_scans)
        
        # Verify final scan credit consumption
        db.refresh(test_user)
        logger.info(f"Final scan count: {test_user.scans_this_month}")
        logger.info(f"Total scan credits consumed: {test_user.scans_this_month - scans_before}")
        
        logger.info("Minimal test completed successfully")
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(execute_minimal_test())
