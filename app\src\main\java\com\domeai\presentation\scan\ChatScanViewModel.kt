package com.domeai.presentation.scan

import android.content.Context
import android.net.Uri
import android.util.Log
import android.webkit.URLUtil
import androidx.compose.foundation.lazy.LazyListState
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.delay
import com.domeai.data.model.RiskLevel
import com.domeai.data.model.ScanResult
import com.domeai.data.model.ScanSourceType
import com.domeai.data.model.network.ScanResponse
import com.domeai.data.model.network.ScanResultData
import com.domeai.data.repository.ScanRepository
import com.domeai.data.repository.SubscriptionRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job
import java.util.UUID
import javax.inject.Inject

private const val TAG = "ChatScanViewModel"

/**
 * Data class to track polling scans
 */
data class PollingScan(
    val scanId: String,
    val inputType: ScanInputType,
    val content: String,
    val pollingJob: Job
)

/**
 * UI state for the chat scan screen
 */
data class ChatScanUiState(
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val scanId: String? = null,
    val scanStatus: String? = null,
    val activeSubscriptionTier: String = "basic", // Values: "basic", "premium", "expert"
    val isExpertModeEnabled: Boolean = false,
    val expertScansRemaining: Int = 0,
    val regularScansRemaining: Int = 0,
    val showScanLimitMessage: Boolean = false,
    val pollingScans: Map<String, PollingScan> = emptyMap(), // Track scans being polled
    val latestScanResults: List<ScanResult> = emptyList(), // Latest scan results for chat display
    val chatMessages: List<ChatMessage> = emptyList(), // Persistent chat messages
    val chatScanIds: Set<String> = emptySet(), // Track scan IDs that belong to this chat session
    val activeScanId: String? = null // Track which scan the session messages belong to
)

/**
 * ViewModel for the chat scan screen
 */
@HiltViewModel
class ChatScanViewModel @Inject constructor(
    private val scanRepository: ScanRepository,
    private val subscriptionRepository: SubscriptionRepository,
    @ApplicationContext private val context: Context
) : ViewModel() {

    private val _uiState = MutableStateFlow(ChatScanUiState())
    val uiState: StateFlow<ChatScanUiState> = _uiState.asStateFlow()

    // LazyListState for persistent scroll position
    val lazyListState = LazyListState()

    // Store the last refresh trigger value
    private var lastRefreshTrigger = 0L

    init {
        // Initialize chat with welcome message
        initializeChat()

        // Initialize the user's subscription plan with force refresh
        refreshUserPlan(forceRefresh = true)

        // Start observing for plan refresh triggers
        observePlanRefreshTriggers()

        // Start observing scan results
        observeScanResults()

        // Log initialization
        Log.d(TAG, "ChatScanViewModel initialized")
    }

    /**
     * Initialize chat with welcome message
     */
    private fun initializeChat() {
        val initialGreeting = com.domeai.presentation.scan.ChatMessage(
            content = "Hello! I'm DomeAI, your personal scam detector. I can help you analyze text, URLs, or images to identify potential scams. How can I assist you today?",
            isUser = false,
            timestamp = System.currentTimeMillis()
        )

        _uiState.update {
            it.copy(chatMessages = listOf(initialGreeting))
        }
    }

    /**
     * Observe shared preferences for plan refresh triggers
     */
    private fun observePlanRefreshTriggers() {
        viewModelScope.launch {
            while (true) {
                try {
                    // Check for new refresh trigger value every second
                    val sharedPrefs = context.getSharedPreferences("dome_ai_prefs", Context.MODE_PRIVATE)
                    val currentTrigger = sharedPrefs.getLong("plan_refresh_trigger", 0L)

                    // If we have a new trigger value, refresh the plan
                    if (currentTrigger > 0 && currentTrigger != lastRefreshTrigger) {
                        Log.d(TAG, "Detected plan refresh trigger: $currentTrigger")
                        lastRefreshTrigger = currentTrigger
                        refreshUserPlan(forceRefresh = true)
                    }

                    // Wait before checking again
                    delay(1000)
                } catch (e: Exception) {
                    Log.e(TAG, "Error checking for plan refresh triggers", e)
                    delay(5000) // Wait longer on error
                }
            }
        }
    }

    /**
     * Observe scan results from the repository
     */
    private fun observeScanResults() {
        viewModelScope.launch {
            scanRepository.getAllScans().collect { scanResults ->
                Log.d(TAG, "Received ${scanResults.size} scan results from repository")

                // Update UI state with latest scan results
                _uiState.update { currentState ->
                    currentState.copy(latestScanResults = scanResults)
                }

                // Log the latest scan results for debugging
                scanResults.take(3).forEach { scan ->
                    Log.d(TAG, "Scan ${scan.id}: ${scan.riskLevel}, explanation: ${scan.explanation.take(50)}...")
                }
            }
        }
    }

    /**
     * Send text to be scanned
     */
    fun onSendText(text: String, userContext: String? = null, sessionId: String? = null) {
        // Debug logging
        Log.d(TAG, "onSendText called with text: '$text', length: ${text.length}, blank: ${text.isBlank()}")

        // Check if the text is a URL
        if (URLUtil.isValidUrl(text)) {
            Log.d(TAG, "Text is a URL, redirecting to onSendUrl")
            onSendUrl(text, userContext, sessionId)
            return
        }

        _uiState.update { it.copy(isLoading = true, errorMessage = null) }

        viewModelScope.launch {
            try {
                // Check if expert mode is enabled
                val useExpertMode = _uiState.value.isExpertModeEnabled

                Log.d(TAG, "Submitting text scan: $text, expertMode: $useExpertMode")
                val result = scanRepository.submitTextScan(text, userContext, sessionId)
                handleScanResult(result, ScanInputType.TEXT, text)

                // Refresh user plan to update scan counts
                refreshUserPlan()
            } catch (e: Exception) {
                Log.e(TAG, "Error sending text scan", e)
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        errorMessage = e.message
                    )
                }

                // Log the error for debugging
                Log.e(TAG, "Error sending text scan: ${e.message}", e)

                // Don't create fallback responses to better debug API issues
                // Instead, show the error message to the user
            }
        }
    }

    /**
     * Send URL to be scanned
     */
    fun onSendUrl(url: String, userContext: String? = null, sessionId: String? = null) {
        // Debug logging
        Log.d(TAG, "onSendUrl called with URL: '$url', length: ${url.length}, blank: ${url.isBlank()}")

        _uiState.update { it.copy(isLoading = true, errorMessage = null) }

        viewModelScope.launch {
            try {
                // Check if expert mode is enabled
                val useExpertMode = _uiState.value.isExpertModeEnabled

                Log.d(TAG, "Submitting URL scan: $url, expertMode: $useExpertMode")
                val result = scanRepository.submitUrlScan(url, userContext, sessionId)
                handleScanResult(result, ScanInputType.URL, url)

                // Refresh user plan to update scan counts
                refreshUserPlan()
            } catch (e: Exception) {
                Log.e(TAG, "Error sending URL scan", e)
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        errorMessage = e.message
                    )
                }

                // Log the error for debugging
                Log.e(TAG, "Error sending URL scan: ${e.message}", e)

                // Don't create fallback responses to better debug API issues
                // Instead, show the error message to the user
            }
        }
    }

    /**
     * Send image to be scanned
     */
    fun onSendImage(
        imageUri: Uri,
        userContext: String? = null,
        sessionId: String? = null,
        context: Context
    ) {
        // Debug logging
        Log.d(TAG, "onSendImage called with URI: '$imageUri', null: ${imageUri == null}")
        Log.d("DomeUIImageURI", "ViewModel.onSendImage received URI: $imageUri")

        _uiState.update { it.copy(isLoading = true, errorMessage = null) }

        viewModelScope.launch {
            try {
                // Check if expert mode is enabled
                val useExpertMode = _uiState.value.isExpertModeEnabled

                Log.d(TAG, "Submitting image scan: $imageUri, expertMode: $useExpertMode")

                try {
                    Log.d("DomeUIImageURI", "ViewModel validation: imageUri=$imageUri")
                    // Validate the URI
                    if (imageUri.toString().isBlank()) {
                        Log.d("DomeUIImageURI", "URI validation failed: blank or null")
                        throw Exception("Image URI is blank or null")
                    }

                    // Check if the URI is accessible
                    context.contentResolver.openInputStream(imageUri)?.use {
                        Log.d(TAG, "Successfully opened input stream for URI: $imageUri")
                        Log.d("DomeUIImageURI", "URI accessibility check passed")
                    } ?: throw Exception("Failed to open input stream for URI: $imageUri")

                    Log.d("DomeUIImageURI", "About to call scanRepository.submitImageScan with URI: $imageUri")
                    // Submit the image scan with the expert mode flag
                    val result = scanRepository.submitImageScan(
                        imageUri,
                        userContext,
                        sessionId,
                        context,
                        useExpertScan = useExpertMode
                    )

                    handleScanResult(result, ScanInputType.IMAGE, imageUri.toString())
                } catch (e: Exception) {
                    Log.e(TAG, "Error validating image URI", e)
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            errorMessage = "Error processing image: ${e.message}"
                        )
                    }
                }

                // Refresh user plan to update scan counts
                refreshUserPlan()
            } catch (e: Exception) {
                Log.e(TAG, "Error sending image scan", e)
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        errorMessage = e.message
                    )
                }

                // Log the error for debugging
                Log.e(TAG, "Error sending image scan: ${e.message}", e)

                // Don't create fallback responses to better debug API issues
                // Instead, show the error message to the user
            }
        }
    }

    /**
     * Create a fallback scan result for UI when API call fails
     */
    private fun createFallbackScanResult(inputType: ScanInputType, content: String) {
        // Create a temporary scan result for the UI
        val scanResult = ScanResult(
            id = UUID.randomUUID().toString(),
            timestamp = System.currentTimeMillis(),
            riskScore = 50, // Medium risk as default
            riskLevel = RiskLevel.MEDIUM_RISK,
            redFlags = listOf("API connection issue"),
            explanation = "We're experiencing some technical difficulties connecting to our AI analysis service. " +
                    "Please try again later or contact support if the issue persists.",
            sourceType = when (inputType) {
                ScanInputType.TEXT -> ScanSourceType.MANUAL_TEXT
                ScanInputType.URL -> ScanSourceType.MANUAL_TEXT
                ScanInputType.IMAGE -> ScanSourceType.MANUAL_IMAGE
            },
            sourceContent = content,
            isGeneralQuestion = false
        )

        // Add the scan result to the repository
        viewModelScope.launch {
            scanRepository.addScan(scanResult)
        }
    }

    /**
     * Handle the result of a scan submission
     */
    private fun handleScanResult(
        result: Result<ScanResponse>,
        inputType: ScanInputType,
        content: String
    ) {
        result.fold(
            onSuccess = { scanResponse ->
                Log.d(TAG, "Scan submitted successfully: ${scanResponse.id}")
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        scanId = scanResponse.id.toString(),
                        scanStatus = scanResponse.status,
                        errorMessage = null
                    )
                }

                // Create a minimal scan result for tracking purposes (will be updated when completed)
                val scanResult = ScanResult(
                    id = scanResponse.id.toString(),
                    timestamp = System.currentTimeMillis(),
                    riskScore = 0, // Will be updated when completed
                    riskLevel = RiskLevel.MEDIUM_RISK, // Will be updated when completed
                    redFlags = emptyList(), // Will be updated when completed
                    explanation = "Processing...", // Will be updated when completed
                    sourceType = when (inputType) {
                        ScanInputType.TEXT -> ScanSourceType.MANUAL_TEXT
                        ScanInputType.URL -> ScanSourceType.MANUAL_TEXT
                        ScanInputType.IMAGE -> ScanSourceType.MANUAL_IMAGE
                    },
                    sourceContent = content
                )

                // Add the scan to the repository so it can be updated later
                viewModelScope.launch {
                    scanRepository.addScan(scanResult)
                }

                // Start polling for the scan result
                startPollingForScan(scanResponse.id.toString(), inputType, content)
            },
            onFailure = { error ->
                Log.e(TAG, "Error submitting scan", error)

                // Show detailed error message to user for debugging
                val errorMsg = "API Error: ${error.message}"

                _uiState.update {
                    it.copy(
                        isLoading = false,
                        errorMessage = errorMsg
                    )
                }
            }
        )
    }

    /**
     * Start polling for scan results
     */
    private fun startPollingForScan(scanId: String, inputType: ScanInputType, content: String) {
        Log.d(TAG, "Starting polling for scan ID: $scanId")

        // Cancel any existing polling job for this scan
        stopPollingForScan(scanId)

        val pollingJob = viewModelScope.launch {
            var retryCount = 0
            val maxRetries = 18 // 18 * 5 seconds = 90 seconds timeout

            while (retryCount < maxRetries) {
                try {
                    Log.d(TAG, "Polling attempt ${retryCount + 1} for scan $scanId")

                    val result = scanRepository.getScanDetails(scanId)

                    result.fold(
                        onSuccess = { scanResponse ->
                            Log.d(TAG, "Polling response for scan $scanId: status=${scanResponse.status}")
                            Log.d(TAG, "Scan response details: id=${scanResponse.id}, analysisResult=${scanResponse.analysisResult != null}")

                            when (scanResponse.status) {
                                "completed" -> {
                                    Log.d(TAG, "Scan $scanId completed, updating UI")
                                    Log.d(TAG, "Analysis result available: ${scanResponse.analysisResult != null}")
                                    if (scanResponse.analysisResult != null) {
                                        Log.d(TAG, "Risk score: ${scanResponse.analysisResult.riskScore}")
                                        Log.d(TAG, "Red flags: ${scanResponse.analysisResult.detectedRedFlags}")
                                    }
                                    handleCompletedScan(scanResponse, inputType, content)
                                    stopPollingForScan(scanId)
                                    return@launch
                                }
                                "failed" -> {
                                    Log.e(TAG, "Scan $scanId failed: ${scanResponse.errorMessage}")
                                    handleFailedScan(scanId, scanResponse.errorMessage ?: "Scan failed")
                                    stopPollingForScan(scanId)
                                    return@launch
                                }
                                "pending", "processing" -> {
                                    Log.d(TAG, "Scan $scanId still processing (status: ${scanResponse.status}), continuing to poll")
                                    // Continue polling
                                }
                                else -> {
                                    Log.w(TAG, "Unknown scan status: ${scanResponse.status}, continuing to poll")
                                    // Continue polling for unknown status
                                }
                            }
                        },
                        onFailure = { error ->
                            Log.e(TAG, "Error polling scan $scanId: ${error.message}")
                            // Continue polling on error (might be temporary network issue)
                        }
                    )

                    retryCount++
                    if (retryCount < maxRetries) {
                        delay(5000) // Wait 5 seconds before next poll
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Exception during polling for scan $scanId", e)
                    retryCount++
                    if (retryCount < maxRetries) {
                        delay(5000)
                    }
                }
            }

            // Timeout reached
            Log.w(TAG, "Polling timeout for scan $scanId")
            handlePollingTimeout(scanId)
            stopPollingForScan(scanId)
        }

        // Store the polling job
        val pollingScan = PollingScan(scanId, inputType, content, pollingJob)
        _uiState.update {
            it.copy(pollingScans = it.pollingScans + (scanId to pollingScan))
        }
    }

    /**
     * Stop polling for a specific scan
     */
    private fun stopPollingForScan(scanId: String) {
        val currentPollingScans = _uiState.value.pollingScans
        currentPollingScans[scanId]?.pollingJob?.cancel()

        _uiState.update {
            it.copy(pollingScans = it.pollingScans - scanId)
        }

        Log.d(TAG, "Stopped polling for scan $scanId")
    }

    /**
     * Clear the error message
     */
    fun clearError() {
        _uiState.update { it.copy(errorMessage = null) }
    }

    /**
     * Add a user message to the chat
     */
    fun addUserMessage(content: String, imageUri: Uri? = null, inputType: ScanInputType = ScanInputType.TEXT) {
        val message = com.domeai.presentation.scan.ChatMessage(
            content = content,
            isUser = true,
            timestamp = System.currentTimeMillis(),
            imageUri = imageUri,
            inputType = inputType
        )

        _uiState.update { currentState ->
            currentState.copy(chatMessages = currentState.chatMessages + message)
        }
    }

    /**
     * Add an analyzing message to the chat
     */
    fun addAnalyzingMessage() {
        val message = com.domeai.presentation.scan.ChatMessage(
            content = "🔍 Analyzing your submission... This may take 30-90 seconds.",
            isUser = false,
            timestamp = System.currentTimeMillis()
        )

        _uiState.update { currentState ->
            currentState.copy(chatMessages = currentState.chatMessages + message)
        }
    }

    /**
     * Add a scan result message to the chat
     */
    fun addScanResultMessage(scanResult: ScanResult) {
        // Remove any existing "Analyzing" message
        _uiState.update { currentState ->
            val filteredMessages = currentState.chatMessages.filter { message ->
                !message.content.contains("Analyzing your submission")
            }
            currentState.copy(chatMessages = filteredMessages)
        }

        // Use the explanation directly from the ScanResult (it's already formatted by buildExplanationText)
        val explanationText = scanResult.explanation

        val aiMessage = if (scanResult.isGeneralQuestion == true) {
            // For session messages, use only the conversational response
            com.domeai.presentation.scan.ChatMessage(
                content = explanationText,
                isUser = false,
                timestamp = System.currentTimeMillis(),
                scanResult = scanResult
            )
        } else {
            // For regular scans, use the full structured format
            val (riskLevelText, riskColorCircle) = when (scanResult.riskLevel) {
                RiskLevel.SAFE -> "Safe" to "🟢"
                RiskLevel.LOW_RISK -> "Low Risk" to "🟡"
                RiskLevel.MEDIUM_RISK -> "Medium Risk" to "🟠"
                RiskLevel.HIGH_RISK -> "High Risk" to "🔴"
            }

            com.domeai.presentation.scan.ChatMessage(
                content = "🛡️ Scan Completed\n\n**Risk Level:** $riskColorCircle $riskLevelText (${scanResult.riskScore}%)\n\n$explanationText",
                isUser = false,
                timestamp = System.currentTimeMillis(),
                scanResult = scanResult
            )
        }

        _uiState.update { currentState ->
            currentState.copy(chatMessages = currentState.chatMessages + aiMessage)
        }

        // Add post-scan messages for actual scans, session counter for session messages
        if (scanResult.isGeneralQuestion == true) {
            addSessionMessageCounter()
        } else {
            // Set this scan as the active scan for session messages
            _uiState.update { currentState ->
                currentState.copy(activeScanId = scanResult.id)
            }
            addPostScanMessages()
        }
    }

    /**
     * Add session message counter after a session message
     */
    private fun addSessionMessageCounter() {
        val currentState = _uiState.value

        // Calculate session messages remaining based on tier and usage
        val maxSessionMessages = when (currentState.activeSubscriptionTier) {
            "basic" -> 0 // Basic gets no session messages
            "premium" -> 10 // Premium gets 10 messages per session
            "expert" -> 15 // Expert gets 15 messages per session
            else -> 0
        }

        // Count session messages used for the CURRENT ACTIVE SCAN only
        val sessionMessagesUsed = currentState.chatMessages.count { message ->
            message.scanResult?.isGeneralQuestion == true &&
            message.scanResult?.id == currentState.activeScanId
        }

        val sessionMessagesRemaining = maxOf(0, maxSessionMessages - sessionMessagesUsed)

        // Only show session message info for paid tiers
        if (maxSessionMessages > 0) {
            val sessionCounterMessage = com.domeai.presentation.scan.ChatMessage(
                content = "📊 You have $sessionMessagesRemaining session messages remaining",
                isUser = false,
                timestamp = System.currentTimeMillis() + 1
            )

            _uiState.update { currentState ->
                currentState.copy(
                    chatMessages = currentState.chatMessages + sessionCounterMessage
                )
            }
        }
    }

    /**
     * Add post-scan messages (session remaining + follow-up + new scan button)
     */
    private fun addPostScanMessages() {
        val currentState = _uiState.value

        // Calculate session messages remaining based on tier and usage
        val maxSessionMessages = when (currentState.activeSubscriptionTier) {
            "basic" -> 0 // Basic gets no session messages
            "premium" -> 10 // Premium gets 10 messages per session
            "expert" -> 15 // Expert gets 15 messages per session
            else -> 0
        }

        // Count session messages used for the CURRENT ACTIVE SCAN only
        val sessionMessagesUsed = currentState.chatMessages.count { message ->
            message.scanResult?.isGeneralQuestion == true &&
            message.scanResult?.id == currentState.activeScanId
        }

        val sessionMessagesRemaining = maxOf(0, maxSessionMessages - sessionMessagesUsed)

        // Only show session message info for paid tiers
        val sessionMessageText = if (maxSessionMessages > 0) {
            "📊 You have $sessionMessagesRemaining session messages remaining\n\n"
        } else {
            ""
        }

        // Combined post-scan message with all information
        val combinedMessage = com.domeai.presentation.scan.ChatMessage(
            content = "${sessionMessageText}Is there anything else I can help you with regarding this scan?",
            isUser = false,
            timestamp = System.currentTimeMillis() + 1,
            isNewScanButton = true // This will show the "Start New Scan" button
        )

        _uiState.update { currentState ->
            currentState.copy(
                chatMessages = currentState.chatMessages + combinedMessage
            )
        }
    }

    /**
     * Track a scan ID as part of this chat session
     */
    fun trackScanId(scanId: String) {
        _uiState.update { currentState ->
            currentState.copy(chatScanIds = currentState.chatScanIds + scanId)
        }
    }

    /**
     * Start a new chat (clear all messages except welcome)
     */
    fun startNewChat() {
        // Clear all state and reinitialize
        _uiState.update {
            it.copy(
                chatMessages = emptyList(),
                chatScanIds = emptySet(),
                scanId = null,
                scanStatus = null,
                isLoading = false,
                errorMessage = null,
                pollingScans = emptyMap(),
                latestScanResults = emptyList(),
                activeScanId = null // Reset active scan ID
            )
        }

        // Reinitialize with welcome message
        initializeChat()
    }

    /**
     * Toggle expert mode (only available for Expert tier users)
     */
    fun toggleExpertMode() {
        val currentState = _uiState.value

        // Only allow toggling if user is on Expert tier
        if (currentState.activeSubscriptionTier == "expert") {
            // Check if user has expert scans remaining
            if (!currentState.isExpertModeEnabled && currentState.expertScansRemaining <= 0) {
                _uiState.update { it.copy(
                    errorMessage = "You have used all your expert scans for this month."
                ) }
                return
            }

            // Toggle expert mode
            _uiState.update { it.copy(
                isExpertModeEnabled = !it.isExpertModeEnabled
            ) }

            Log.d(TAG, "Expert mode toggled: ${!currentState.isExpertModeEnabled}")
        } else {
            _uiState.update { it.copy(
                errorMessage = "Expert mode is only available for Expert tier users."
            ) }
        }
    }

    /**
     * Handle completed scan results
     */
    private fun handleCompletedScan(scanResponse: ScanResponse, inputType: ScanInputType, content: String) {
        Log.d(TAG, "Handling completed scan: ${scanResponse.id}")
        Log.d(TAG, "Analysis result: ${scanResponse.analysisResult}")

        val analysisResult = scanResponse.analysisResult
        if (analysisResult != null) {
            Log.d(TAG, "Processing analysis result with risk score: ${analysisResult.riskScore}")
            Log.d(TAG, "isGeneralQuestion flag: ${analysisResult.isGeneralQuestion}")
            Log.d(TAG, "Model used: ${analysisResult.modelUsed}")
            // Safely handle nullable riskScore
            val riskScore = analysisResult.riskScore ?: 0.5f
            val riskScoreInt = (riskScore * 100).toInt()

            // Create a scan result with the actual AI analysis
            val scanResult = ScanResult(
                id = scanResponse.id.toString(),
                timestamp = System.currentTimeMillis(),
                riskScore = riskScoreInt, // Convert to percentage
                riskLevel = when {
                    riskScore >= 0.8f -> RiskLevel.HIGH_RISK
                    riskScore >= 0.5f -> RiskLevel.MEDIUM_RISK
                    riskScore >= 0.2f -> RiskLevel.LOW_RISK
                    else -> RiskLevel.SAFE
                },
                redFlags = analysisResult.detectedRedFlags ?: emptyList(),
                explanation = buildExplanationText(analysisResult),
                sourceType = when (inputType) {
                    ScanInputType.TEXT -> ScanSourceType.MANUAL_TEXT
                    ScanInputType.URL -> ScanSourceType.MANUAL_TEXT
                    ScanInputType.IMAGE -> ScanSourceType.MANUAL_IMAGE
                },
                sourceContent = content,
                isGeneralQuestion = analysisResult.isGeneralQuestion ?: false
            )

            // Update the scan result in the repository
            viewModelScope.launch {
                Log.d(TAG, "Updating scan result in repository: ${scanResult.id}")
                Log.d(TAG, "Updated scan details: riskScore=${scanResult.riskScore}, explanation length=${scanResult.explanation.length}")
                scanRepository.updateScan(scanResult)
                Log.d(TAG, "Scan result updated successfully")
            }
        } else {
            Log.w(TAG, "Completed scan has no analysis result")
            handleFailedScan(scanResponse.id.toString(), "Analysis result not available")
        }
    }

    /**
     * Build a comprehensive explanation text from the analysis result
     */
    private fun buildExplanationText(analysisResult: ScanResultData): String {
        val parts = mutableListOf<String>()

        // Check if this is a session message (general question)
        if (analysisResult.isGeneralQuestion == true) {
            // For session messages, return only the conversational response with improved formatting
            return analysisResult.explanation?.let { formatSessionMessage(it) } ?: ""
        }

        // For regular scans, use the structured format
        // Add main analysis section - preserve the original explanation with better formatting
        analysisResult.explanation?.let { explanation ->
            val cleanedExplanation = cleanText(explanation)
                .replace("- ", "\n• ")
                .replace("–", "\n•")
                .trim()
            if (cleanedExplanation.isNotBlank()) {
                parts.add("**📋 Analysis:**\n\n$cleanedExplanation")
            }
        }

        // Add flags section with risk-based adaptation
        analysisResult.detectedRedFlags?.let { flags ->
            if (flags.isNotEmpty()) {
                // Determine flag type based on risk score
                val riskScore = analysisResult.riskScore ?: 0.5f
                val isLowRisk = riskScore <= 0.3f
                val isMediumRisk = riskScore > 0.3f && riskScore <= 0.6f

                val flagTitle = when {
                    isLowRisk -> "✅ Green Flags"
                    isMediumRisk -> "⚠️ Mixed Assessment"
                    else -> "🚩 Red Flags"
                }

                val flagIcon = when {
                    isLowRisk -> "✅"
                    isMediumRisk -> "⚠️"
                    else -> "🚩"
                }

                val flagsText = flags.joinToString("\n\n") { flag ->
                    "$flagIcon ${cleanText(flag)}"
                }
                parts.add("**$flagTitle:**\n\n$flagsText")
            }
        }

        // Add key findings section (NEW)
        analysisResult.keyFindings?.let { keyFindings ->
            val cleanedKeyFindings = cleanText(keyFindings)
                .replace("- ", "\n🔍 ")
                .replace("–", "\n🔍")
                .trim()
            if (cleanedKeyFindings.isNotBlank()) {
                parts.add("**🔍 Key Findings:**\n\n$cleanedKeyFindings")
            }
        }

        // Add knowledge base references section (NEW)
        analysisResult.knowledgeBaseReferences?.let { knowledgeBase ->
            val cleanedKnowledgeBase = cleanText(knowledgeBase)
                .replace("- ", "\n📚 ")
                .replace("–", "\n📚")
                .trim()
            if (cleanedKnowledgeBase.isNotBlank()) {
                parts.add("**📚 Knowledge Base References:**\n\n$cleanedKnowledgeBase")
            }
        }

        // Add recommendations section
        analysisResult.recommendations?.let { recommendations ->
            val cleanedRecommendations = cleanText(recommendations)
                .replace("- ", "\n💡 ")
                .replace("–", "\n💡")
                .trim()
            if (cleanedRecommendations.isNotBlank()) {
                parts.add("**💡 Recommendations:**\n\n$cleanedRecommendations")
            }
        }

        // Add confidence level with emoji
        analysisResult.confidenceLevel?.let { confidence ->
            if (confidence.isNotBlank()) {
                val confidenceEmoji = when {
                    confidence.contains("High", ignoreCase = true) -> "🎯"
                    confidence.contains("Medium", ignoreCase = true) -> "📊"
                    else -> "📈"
                }
                val cleanConfidence = cleanText(confidence)
                parts.add("**$confidenceEmoji Confidence:** $cleanConfidence")
            }
        }

        // Add model information with generic name
        analysisResult.modelUsed?.let { model ->
            val genericModelName = when {
                model.contains("gpt-4", ignoreCase = true) -> "Premium AI Engine"
                model.contains("expert", ignoreCase = true) -> "Expert AI Engine"
                else -> "Advanced AI Engine"
            }
            parts.add("**🤖 Powered by:** $genericModelName")
        }

        return if (parts.isNotEmpty()) {
            parts.joinToString("\n\n")
        } else {
            "✅ Analysis completed successfully."
        }
    }

    /**
     * Format session message with proper spacing and structure
     */
    private fun formatSessionMessage(text: String): String {
        var formatted = text

        // Remove AI internal reasoning if present
        if (formatted.contains("This is a general question")) {
            val lines = formatted.split("\n")
            val contentStart = lines.indexOfFirst {
                it.contains("Here's a helpful") ||
                it.contains("When analyzing") ||
                it.contains("To ") ||
                !it.contains("This is a general question") && !it.contains("not a specific content")
            }
            if (contentStart > 0) {
                formatted = lines.drop(contentStart).joinToString("\n").trim()
            }
        }

        return formatted
            .replace("**", "") // Remove markdown bold
            .replace("*", "") // Remove markdown italic
            .replace("\n\n\n", "\n\n") // Fix excessive line breaks
            .replace("- ", "\n• ") // Convert dashes to bullet points
            .replace("1. ", "\n1. ") // Ensure numbered lists have proper spacing
            .replace("2. ", "\n2. ")
            .replace("3. ", "\n3. ")
            .replace("4. ", "\n4. ")
            .replace("5. ", "\n5. ")
            .replace("6. ", "\n6. ")
            .replace("7. ", "\n7. ")
            .replace("8. ", "\n8. ")
            .replace("9. ", "\n9. ")
            .trim()
    }

    /**
     * Clean text by removing excessive markdown but preserving structure
     */
    private fun cleanText(text: String): String {
        return text
            .replace("---", "\n\n")
            .replace("**", "") // Remove bold markdown for now, we'll add it back selectively
            .replace("*", "")
            .replace("\n\n\n", "\n\n")
            .trim()
    }

    /**
     * Handle failed scan
     */
    private fun handleFailedScan(scanId: String, errorMessage: String) {
        Log.e(TAG, "Handling failed scan: $scanId, error: $errorMessage")

        val scanResult = ScanResult(
            id = scanId,
            timestamp = System.currentTimeMillis(),
            riskScore = 0,
            riskLevel = RiskLevel.MEDIUM_RISK,
            redFlags = listOf("Scan failed"),
            explanation = "Scan failed: $errorMessage. Please try again or contact support if the issue persists.",
            sourceType = ScanSourceType.MANUAL_TEXT,
            sourceContent = "Failed scan",
            isGeneralQuestion = false
        )

        // Update the scan result in the repository
        viewModelScope.launch {
            scanRepository.updateScan(scanResult)
        }
    }

    /**
     * Handle polling timeout
     */
    private fun handlePollingTimeout(scanId: String) {
        Log.w(TAG, "Handling polling timeout for scan: $scanId")

        val scanResult = ScanResult(
            id = scanId,
            timestamp = System.currentTimeMillis(),
            riskScore = 0,
            riskLevel = RiskLevel.MEDIUM_RISK,
            redFlags = listOf("Processing timeout"),
            explanation = "The scan is taking longer than expected to process. " +
                    "Please check your scan history later or contact support if the issue persists.",
            sourceType = ScanSourceType.MANUAL_TEXT,
            sourceContent = "Timeout",
            isGeneralQuestion = false
        )

        // Update the scan result in the repository
        viewModelScope.launch {
            scanRepository.updateScan(scanResult)
        }
    }

    /**
     * Refresh the user's subscription plan
     * This should be called when the user changes their subscription plan
     * or after submitting a scan
     *
     * @param forceRefresh If true, forces a refresh from the backend API
     */
    fun refreshUserPlan(forceRefresh: Boolean = false) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "Refreshing user plan (forceRefresh=$forceRefresh)")
                val subscription = subscriptionRepository.getCurrentSubscription(forceRefresh = forceRefresh)

                // Determine the active subscription tier
                val activeSubscriptionTier = when {
                    subscription.planName.contains("Expert", ignoreCase = true) -> "expert"
                    subscription.planName.contains("Premium", ignoreCase = true) -> "premium"
                    else -> "basic"
                }

                // Calculate remaining scans
                val regularScansRemaining = when (activeSubscriptionTier) {
                    "basic" -> com.domeai.data.model.network.SubscriptionTiers.BASIC_SCAN_LIMIT - subscription.scansThisMonth
                    "premium" -> 100 - subscription.scansThisMonth // Premium has 100 scans limit
                    "expert" -> com.domeai.data.model.network.SubscriptionTiers.EXPERT_REGULAR_SCAN_LIMIT - subscription.scansThisMonth
                    else -> 0
                }

                // Calculate remaining expert scans (only for expert tier)
                val expertScansRemaining = if (activeSubscriptionTier == "expert") {
                    com.domeai.data.model.network.SubscriptionTiers.EXPERT_ADVANCED_SCAN_LIMIT - subscription.expertScansThisMonth
                } else {
                    0
                }

                // Show scan limit message if user is close to limit
                val showScanLimitMessage = when (activeSubscriptionTier) {
                    "basic" -> regularScansRemaining <= 1
                    "premium" -> regularScansRemaining <= 5
                    "expert" -> regularScansRemaining <= 5 || expertScansRemaining <= 2
                    else -> false
                }

                // Update the UI state with the active subscription tier and scan limits
                _uiState.update { it.copy(
                    activeSubscriptionTier = activeSubscriptionTier,
                    expertScansRemaining = expertScansRemaining,
                    regularScansRemaining = regularScansRemaining,
                    showScanLimitMessage = showScanLimitMessage,
                    // Reset expert mode if not on expert tier
                    isExpertModeEnabled = if (activeSubscriptionTier != "expert") false else it.isExpertModeEnabled
                ) }

                Log.d(TAG, "Refreshed user plan: $activeSubscriptionTier, " +
                        "Regular scans remaining: $regularScansRemaining, " +
                        "Expert scans remaining: $expertScansRemaining, " +
                        "scansThisMonth: ${subscription.scansThisMonth}, " +
                        "expertScansThisMonth: ${subscription.expertScansThisMonth}")
            } catch (e: Exception) {
                Log.e(TAG, "Error refreshing user plan", e)
            }
        }
    }
}
