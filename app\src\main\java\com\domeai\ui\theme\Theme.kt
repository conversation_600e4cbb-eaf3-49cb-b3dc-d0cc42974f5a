package com.domeai.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

private val LightColorScheme = lightColorScheme(
    primary = PrimaryBlue,
    onPrimary = NeutralWhite,
    primaryContainer = PrimaryLight,
    onPrimaryContainer = PrimaryDark,

    secondary = SecondaryGreen,
    onSecondary = NeutralWhite,
    secondaryContainer = SecondaryLight,
    onSecondaryContainer = SecondaryDark,

    tertiary = PrimaryLight,
    onTertiary = NeutralWhite,
    tertiaryContainer = PrimaryLight.copy(alpha = 0.3f),
    onTertiaryContainer = PrimaryDark,

    error = AlertRed,
    onError = NeutralWhite,
    errorContainer = AlertRed.copy(alpha = 0.1f),
    onErrorContainer = AlertRed,

    background = NeutralWhite,
    onBackground = NeutralDark,

    surface = NeutralWhite,
    onSurface = NeutralDark,
    surfaceVariant = NeutralLight,
    onSurfaceVariant = NeutralMedium,

    outline = NeutralMedium
)

private val DarkColorScheme = darkColorScheme(
    primary = PrimaryLight,
    onPrimary = NeutralDark,
    primaryContainer = PrimaryDark,
    onPrimaryContainer = PrimaryLight,

    secondary = SecondaryLight,
    onSecondary = NeutralDark,
    secondaryContainer = SecondaryDark,
    onSecondaryContainer = SecondaryLight,

    tertiary = PrimaryLight,
    onTertiary = NeutralDark,
    tertiaryContainer = PrimaryDark,
    onTertiaryContainer = PrimaryLight,

    error = DarkError,
    onError = NeutralDark,
    errorContainer = DarkError.copy(alpha = 0.1f),
    onErrorContainer = DarkError,

    background = DarkBackground,
    onBackground = NeutralLight,

    surface = DarkSurface,
    onSurface = NeutralLight,
    surfaceVariant = DarkSurface.copy(alpha = 0.7f),
    onSurfaceVariant = NeutralLight.copy(alpha = 0.7f),

    outline = NeutralMedium
)

@Composable
fun DomeAITheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is disabled to ensure consistent brand colors
    content: @Composable () -> Unit
) {
    // Always use our fixed brand color scheme
    val colorScheme = if (darkTheme) DarkColorScheme else LightColorScheme

    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.primary.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}
