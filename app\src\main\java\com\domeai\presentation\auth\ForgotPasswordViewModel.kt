package com.domeai.presentation.auth

import androidx.lifecycle.viewModelScope
import com.domeai.data.model.AuthResult
import com.domeai.data.model.ForgotPasswordRequest
import com.domeai.data.model.ValidationResult
import com.domeai.data.repository.AuthRepository
import com.domeai.data.util.Validator
import com.domeai.presentation.common.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * UI State for Forgot Password Screen
 */
data class ForgotPasswordUiState(
    val email: String = "",
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val isResetEmailSent: Boolean = false,
    val emailError: String? = null
) : com.domeai.presentation.common.UiState

/**
 * UI Events for Forgot Password Screen
 */
sealed class ForgotPasswordUiEvent : com.domeai.presentation.common.UiEvent {
    data object NavigateBack : ForgotPasswordUiEvent()
}

/**
 * UI Actions for Forgot Password Screen
 */
sealed class ForgotPasswordUiAction : com.domeai.presentation.common.UiAction {
    data class UpdateEmail(val email: String) : ForgotPasswordUiAction()
    data object ResetPassword : ForgotPasswordUiAction()
    data object NavigateBack : ForgotPasswordUiAction()
}

/**
 * ViewModel for Forgot Password Screen
 */
@HiltViewModel
class ForgotPasswordViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val validator: Validator
) : BaseViewModel<ForgotPasswordUiState, ForgotPasswordUiEvent, ForgotPasswordUiAction>() {
    
    override fun createInitialState(): ForgotPasswordUiState = ForgotPasswordUiState()
    
    override fun handleAction(action: ForgotPasswordUiAction) {
        when (action) {
            is ForgotPasswordUiAction.UpdateEmail -> updateEmail(action.email)
            is ForgotPasswordUiAction.ResetPassword -> resetPassword()
            is ForgotPasswordUiAction.NavigateBack -> navigateBack()
        }
    }
    
    private fun updateEmail(email: String) {
        updateState { it.copy(email = email, emailError = null) }
    }
    
    private fun resetPassword() {
        val currentState = uiState.value
        val emailValidation = validator.validateEmail(currentState.email)
        
        if (emailValidation is ValidationResult.Invalid) {
            updateState { it.copy(emailError = emailValidation.errorMessage) }
            return
        }
        
        viewModelScope.launch {
            val request = ForgotPasswordRequest(email = currentState.email)
            
            authRepository.forgotPassword(request).collectLatest { result ->
                when (result) {
                    is AuthResult.Loading -> {
                        updateState { it.copy(isLoading = true, errorMessage = null) }
                    }
                    is AuthResult.Success -> {
                        updateState { 
                            it.copy(
                                isLoading = false, 
                                isResetEmailSent = true,
                                errorMessage = null
                            ) 
                        }
                    }
                    is AuthResult.Error -> {
                        updateState { 
                            it.copy(
                                isLoading = false, 
                                errorMessage = result.message
                            ) 
                        }
                    }
                    else -> { /* Ignore other states */ }
                }
            }
        }
    }
    
    private fun navigateBack() {
        sendEvent(ForgotPasswordUiEvent.NavigateBack)
    }
}
