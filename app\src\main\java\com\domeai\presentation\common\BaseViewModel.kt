package com.domeai.presentation.common

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

abstract class BaseViewModel<S : UiState, E : UiEvent, A : UiAction> : ViewModel() {

    private val _uiState = MutableStateFlow(createInitialState())
    val uiState: StateFlow<S> = _uiState.asStateFlow()

    private val _uiEvent = Channel<E>()
    val uiEvent = _uiEvent.receiveAsFlow()

    abstract fun createInitialState(): S

    abstract fun handleAction(action: A)

    protected fun updateState(update: (S) -> S) {
        _uiState.update(update)
    }

    fun sendAction(action: A) {
        viewModelScope.launch {
            handleAction(action)
        }
    }

    protected fun sendEvent(event: E) {
        viewModelScope.launch {
            _uiEvent.send(event)
        }
    }
}

interface UiState

interface UiEvent

interface UiAction
