# Test payload
$payload = @{
    message = @{
        data = "test-data"
        messageId = "test-message-id"
        publishTime = "2023-05-20T10:00:00.000Z"
    }
    subscription = "test-subscription"
} | ConvertTo-Json

Write-Host "Sending test webhook..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8000/webhook" -Method POST -Body $payload -ContentType "application/json"
    Write-Host "Response: $($response | ConvertTo-Json)"
}
catch {
    Write-Host "Error: $_"
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)"
    Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
}

Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
