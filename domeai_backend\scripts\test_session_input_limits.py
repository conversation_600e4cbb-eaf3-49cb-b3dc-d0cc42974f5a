#!/usr/bin/env python
"""
<PERSON><PERSON>t to test session input limits for Premium tier.

This script submits 11 simple text inputs sequentially into a new session
and verifies that:
1. The first 10 scans get added to the session, consuming only 1 initial Premium scan credit
2. When the 11th text input is submitted with the same session ID, a new session is automatically created
   and a second Premium scan credit is consumed
"""

import asyncio
import logging
import sys
import os
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan import Scan
from app.models.scan_session import ScanSession
from app.crud import crud_user, crud_scan, crud_scan_session
from app.core.config import settings
from app.api.v1.endpoints.scans import create_scan

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

async def create_test_user(db: Session, email: str, tier: str = "premium") -> User:
    """Create a test user with the specified subscription tier."""
    # Check if user already exists
    user = crud_user.get_user_by_email(db, email=email)
    if user:
        logger.info(f"User {email} already exists with ID {user.id}")
        # Update subscription tier
        user.subscription_tier = tier
        user.scans_this_month = 0  # Reset scan count
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    # Create new user
    from app.schemas.user import UserCreate

    user_in = UserCreate(
        email=email,
        password="testpassword",
        full_name="Test User"
    )

    user = crud_user.create_user(db=db, obj_in=user_in)

    # Update user properties
    user.subscription_tier = tier
    user.scans_this_month = 0
    user.scan_allowance = 50 if tier == "premium" else 100
    user.expert_scans_this_month = 0
    user.expert_scan_allowance = 0 if tier == "free" else (10 if tier == "premium" else 20)

    db.add(user)
    db.commit()
    db.refresh(user)
    logger.info(f"Created user {email} with ID {user.id} and tier {tier}")
    return user

async def create_scan_in_session(
    db: Session,
    user: User,
    session_id: Optional[uuid.UUID],
    message: str,
    scan_number: int
) -> Scan:
    """Create a scan in the specified session."""
    # Create scan
    from app.schemas.scan import ScanCreate

    scan_in = ScanCreate(
        input_content_type="text",
        input_text=f"Test scan {scan_number}: {message}",
        scan_session_id=session_id
    )

    # Create raw input payload
    raw_input_payload = scan_in.dict()
    raw_input_payload["is_expert_scan"] = False

    # Determine if this is a session followup
    is_session_followup = False
    if session_id:
        # Verify the session exists and belongs to the user
        session = crud_scan_session.get_scan_session(
            db=db, session_id=session_id, owner_id=user.id
        )
        if session:
            # Check if the session has reached its input limit
            if user.subscription_tier in ["premium", "expert"]:
                # Count existing scans in the session
                existing_scans = db.query(Scan).filter(
                    Scan.scan_session_id == session_id
                ).count()

                # Check if session has reached the input limit
                session_max_inputs = settings.SESSION_MAX_INPUTS_PREMIUM
                if user.subscription_tier == "expert":
                    session_max_inputs = settings.SESSION_MAX_INPUTS_EXPERT

                if existing_scans >= session_max_inputs:
                    logger.info(f"Session {session_id} has reached its input limit of {session_max_inputs}")

                    # Create a new session instead of using the existing one
                    session = crud_scan_session.create_scan_session(db=db, owner_id=user.id)
                    session_id = session.id

                    # This is a new session, not a followup, so it will consume a scan credit
                    is_session_followup = False

                    logger.info(f"Created new session {session_id} after reaching input limit")
                else:
                    # Update session activity
                    crud_scan_session.update_scan_session_activity(db=db, db_session=session)

                    # Mark as a session followup (for paid tiers, this won't consume a scan credit)
                    is_session_followup = True
            else:
                # For free tier users, always update session activity but still consume credits
                crud_scan_session.update_scan_session_activity(db=db, db_session=session)
                is_session_followup = True
    else:
        # Create a new session for this scan
        session = crud_scan_session.create_scan_session(db=db, owner_id=user.id)
        session_id = session.id

    # Check if we should consume a scan credit
    # For paid tiers (premium/expert), session followups don't consume credits
    should_consume_credit = True
    if is_session_followup and user.subscription_tier in ["premium", "expert"]:
        should_consume_credit = False
        logger.info(f"Session followup scan for paid tier user - not consuming scan credit")

    # Update scan counters if needed
    if should_consume_credit:
        user.scans_this_month += 1
        logger.info(f"Consuming scan credit. New count: {user.scans_this_month}")

    # Save the updated counters
    db.add(user)
    db.commit()
    db.refresh(user)

    # Create scan entry in database
    db_scan = crud_scan.create_scan_entry(
        db=db,
        scan_in=scan_in,
        owner_id=user.id,
        raw_input_payload=raw_input_payload,
        scan_session_id=session_id
    )

    # Set status to completed (we're not actually processing the scan)
    db_scan.status = "completed"
    db.add(db_scan)
    db.commit()
    db.refresh(db_scan)

    logger.info(f"Created scan {db_scan.id} in session {session_id}")
    logger.info(f"User scan count: {user.scans_this_month}")

    return db_scan

async def test_session_input_limits():
    """Test session input limits for Premium tier."""
    logger.info("Testing session input limits for Premium tier")

    # Create a database session
    db = SessionLocal()
    try:
        # Create a test user with premium tier
        user = await create_test_user(db, email="<EMAIL>", tier="premium")

        # Log initial scan count
        initial_scan_count = user.scans_this_month
        logger.info(f"Initial scan count: {initial_scan_count}")

        # Create a new session
        session = crud_scan_session.create_scan_session(db=db, owner_id=user.id)
        session_id = session.id
        logger.info(f"Created session {session_id}")

        # Submit 11 simple text inputs sequentially
        scans = []
        current_session_id = session_id

        for i in range(1, 12):
            logger.info(f"\n--- Submitting scan {i} ---\n")

            # Create scan
            scan = await create_scan_in_session(
                db,
                user,
                current_session_id,
                f"This is a test message for scan {i}",
                i
            )

            # Update current session ID if it changed
            if scan.scan_session_id != current_session_id:
                logger.info(f"Session ID changed from {current_session_id} to {scan.scan_session_id}")
                current_session_id = scan.scan_session_id

            scans.append(scan)

            # Log scan details
            logger.info(f"Scan {i} details:")
            logger.info(f"  Scan ID: {scan.id}")
            logger.info(f"  Session ID: {scan.scan_session_id}")
            logger.info(f"  User scan count: {user.scans_this_month}")

        # Verify results
        logger.info("\n--- Verification ---\n")

        # Refresh user to get latest scan count
        db.refresh(user)
        final_scan_count = user.scans_this_month
        logger.info(f"Final scan count: {final_scan_count}")
        logger.info(f"Scan count difference: {final_scan_count - initial_scan_count}")

        # Count scans in each session
        session_counts = {}
        for scan in scans:
            session_id = scan.scan_session_id
            if session_id not in session_counts:
                session_counts[session_id] = 0
            session_counts[session_id] += 1

        logger.info(f"Session counts: {session_counts}")

        # Verify that we have at least 2 sessions
        logger.info(f"Number of sessions: {len(session_counts)}")

        # Verify that the first session has exactly SESSION_MAX_INPUTS_PREMIUM scans
        first_session_count = session_counts.get(session_id, 0)
        logger.info(f"First session count: {first_session_count}")
        logger.info(f"SESSION_MAX_INPUTS_PREMIUM: {settings.SESSION_MAX_INPUTS_PREMIUM}")

        if first_session_count == settings.SESSION_MAX_INPUTS_PREMIUM:
            logger.info("PASS: First session has exactly SESSION_MAX_INPUTS_PREMIUM scans")
        else:
            logger.warning(f"FAIL: First session has {first_session_count} scans, expected {settings.SESSION_MAX_INPUTS_PREMIUM}")

        # Verify that we consumed exactly 2 scan credits
        if final_scan_count - initial_scan_count == 2:
            logger.info("PASS: Consumed exactly 2 scan credits")
        else:
            logger.warning(f"FAIL: Consumed {final_scan_count - initial_scan_count} scan credits, expected 2")

    finally:
        db.close()

async def main():
    """Run the script."""
    await test_session_input_limits()

if __name__ == "__main__":
    asyncio.run(main())
