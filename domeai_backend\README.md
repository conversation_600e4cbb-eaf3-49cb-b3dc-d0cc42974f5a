# DomeAI Scam Detector Backend

Backend API for the DomeAI Scam Detector application, built with FastAPI, PostgreSQL, and Celery.

## Features

- FastAPI framework for high-performance API development
- PostgreSQL with pgvector for vector similarity search
- Celery for asynchronous task processing
- <PERSON><PERSON> and <PERSON>er Compose for containerization
- Alembic for database migrations
- Poetry for dependency management

## Prerequisites

- Python 3.9+
- Docker and Docker Compose
- Poetry (for local development)

## Getting Started

### Local Development

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd domeai_backend
   ```

2. Install dependencies with Poetry:
   ```bash
   poetry install
   ```

3. Create a `.env` file based on `.env.example`:
   ```bash
   cp .env.example .env
   ```
   Update the environment variables as needed. For the `SECRET_KEY`, generate a strong random key:
   ```bash
   openssl rand -hex 32
   ```

4. Start the development server:
   ```bash
   poetry run uvicorn app.main:app --reload
   ```

5. Access the API documentation at http://localhost:8000/docs

### Using Docker Compose

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd domeai_backend
   ```

2. Create a `.env` file based on `.env.example`:
   ```bash
   cp .env.example .env
   ```
   Update the environment variables as needed. For the `SECRET_KEY`, generate a strong random key:
   ```bash
   openssl rand -hex 32
   ```

3. Build and start the containers:
   ```bash
   docker-compose up -d --build
   ```

4. Access the API documentation at http://localhost:8000/docs

## Database Migrations

To create a new migration:

```bash
alembic revision --autogenerate -m "description"
```

To apply migrations:

```bash
alembic upgrade head
```

## Project Structure

```
domeai_backend/
├── app/
│   ├── __init__.py
│   ├── main.py             # Main FastAPI app instance and root/health endpoints
│   ├── api/                # API routers/endpoints
│   │   ├── __init__.py
│   │   └── v1/
│   │       ├── __init__.py
│   │       └── endpoints/
│   │           └── __init__.py
│   ├── core/               # Core logic, config, security
│   │   ├── __init__.py
│   │   └── config.py       # Application settings (env vars)
│   ├── crud/               # CRUD operations (database interactions)
│   │   └── __init__.py
│   ├── models/             # SQLAlchemy models
│   │   └── __init__.py
│   ├── schemas/            # Pydantic schemas
│   │   └── __init__.py
│   └── services/           # Business logic services
│       └── __init__.py
├── tests/                  # Unit and integration tests
│   └── __init__.py
├── alembic/                # Database migrations
├── .env.example            # Example environment variables
├── .gitignore
├── Dockerfile              # Dockerfile for the FastAPI app
├── docker-compose.yml      # Docker Compose configuration
├── pyproject.toml          # Poetry configuration
└── README.md               # Project documentation
```

## API Endpoints

- `/`: Root endpoint with welcome message
- `/health`: Health check endpoint that tests database connection and pgvector installation
- `/api/v1/docs`: API documentation (Swagger UI)
- `/api/v1/redoc`: API documentation (ReDoc)

### Authentication Endpoints

- `POST /api/v1/auth/register`: Register a new user
  ```bash
  curl -X POST "http://localhost:8000/api/v1/auth/register" \
       -H "Content-Type: application/json" \
       -d '{"email": "<EMAIL>", "password": "strongpassword"}'
  ```

- `POST /api/v1/auth/login`: Login and get access token
  ```bash
  curl -X POST "http://localhost:8000/api/v1/auth/login" \
       -H "Content-Type: application/x-www-form-urlencoded" \
       -d "username=<EMAIL>&password=strongpassword"
  ```

- `GET /api/v1/auth/me`: Get current user information
  ```bash
  curl -X GET "http://localhost:8000/api/v1/auth/me" \
       -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
  ```

## License

[MIT License](LICENSE)
