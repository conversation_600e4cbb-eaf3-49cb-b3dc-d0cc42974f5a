#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test a multi-image scan session with real AI analysis.
"""

import asyncio
import logging
import sys
import uuid
import os
import time
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan_session import ScanSession
from app.models.scan import Scan
from app.crud import crud_scan_session
from app.services.ai_service_factory import get_ai_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Test image paths
TEST_IMAGE_PATHS = [
    "temp_test_image_1.jpg",
    "temp_test_image_2.jpg",
    "temp_test_image_3.jpg",
    "temp_test_image_4.jpg"
]

# User context for the first scan
USER_CONTEXT = """They almost got me. My intuition is almost on point. I was so close too. Holy Shit. My intuition is "almost" on point. I finally got one. So I'm on a "dating" app. I matched with a girl in my area. She seemed a little too good to be true. So I of course proceeded with caution. When she asked me personal details, I didn't tell her much. My big mistake was to add her on my personal instagram. Onto the good part. She started getting sexual immediately. I told her I don't send pics until I meet the person. This upset her because she had already sent pics over dm. So I sent her a couple fully clothed pics of me in the mirror. This upset her, she wanted a "limp dick" picture. Red flag, what girl wants to see it limp. 😂😂 After she asked for my number and I turned that down, she turned down my offer to video call. Here's a few of the chats. I warned the people on my Instagram about a potential fake dick pic incoming. Will update if this gets more interesting."""

async def process_scan_with_real_ai(
    db: Session,
    scan: Scan,
    user_tier: str,
    is_expert_scan: bool,
    previous_scans: List[Scan] = None
) -> Dict[str, Any]:
    """
    Process a scan with the real AI service.
    
    Args:
        db: Database session
        scan: The scan to process
        user_tier: User's subscription tier
        is_expert_scan: Whether this is an expert scan
        previous_scans: List of previous scans in the session
        
    Returns:
        The analysis result
    """
    logger.info(f"Processing scan {scan.id} with real AI service")
    
    # Update scan status to "processing"
    scan.status = "processing"
    db.add(scan)
    db.commit()
    db.refresh(scan)
    
    # Log information about previous scans
    if previous_scans:
        logger.info(f"Found {len(previous_scans)} previous scans in this session")
        for prev_scan in previous_scans:
            source = prev_scan.input_content_type
            
            # Extract text snippet if available
            text_snippet = None
            if prev_scan.analysis_result and "extracted_text" in prev_scan.analysis_result:
                text_snippet = prev_scan.analysis_result["extracted_text"][:100] + "..."
            elif prev_scan.input_text:
                text_snippet = prev_scan.input_text[:100] + "..."
            else:
                text_snippet = "No text available"
            
            logger.info(f"Previous scan {prev_scan.id} - Source: {source}, Text: {text_snippet}")
    
    try:
        # Get the appropriate AI service based on user's subscription tier
        ai_service = get_ai_service(user_tier=user_tier)
        
        # Process the scan with the real AI service
        if scan.input_content_type == "image_path":
            # Get the file path from the raw_input_payload
            file_path = scan.raw_input_payload.get("file_path")
            if not file_path:
                raise ValueError("File path not found in raw_input_payload")
            
            # Get multimodal analysis
            logger.info(f"Getting multimodal analysis for image at {file_path}")
            multimodal_result = await ai_service.get_multimodal_analysis(
                image_path=file_path
            )
            
            # Extract text and get embedding
            extracted_text = multimodal_result.get("extracted_text", "")
            logger.info(f"Getting text embedding for extracted text: {extracted_text[:100]}...")
            embedding = await ai_service.get_text_embedding(text=extracted_text)
            
            # Perform RAG analysis
            logger.info(f"Performing RAG analysis")
            rag_result = await ai_service.perform_scam_analysis_with_rag(
                query_text=extracted_text,
                query_embedding=embedding,
                original_image_description=multimodal_result.get("image_description", ""),
                original_platform_identified=multimodal_result.get("platform_identified", ""),
                db=db
            )
            
            # Convert ScanResultData to dict
            rag_result_dict = {
                "risk_score": rag_result.risk_score,
                "explanation": rag_result.explanation,
                "detected_red_flags": rag_result.detected_red_flags,
                "recommendations": rag_result.recommendations,
                "confidence_level": rag_result.confidence_level,
                "model_used": rag_result.model_used
            }
            
            # Combine results
            analysis_result = {
                **multimodal_result,
                **rag_result_dict
            }
            
            # Update scan with result
            scan.status = "completed"
            scan.analysis_result = analysis_result
            db.add(scan)
            db.commit()
            db.refresh(scan)
            
            logger.info(f"Completed processing of scan {scan.id}")
            logger.info(f"Analysis result: Risk Score: {analysis_result.get('risk_score', 'N/A')}, Red Flags: {analysis_result.get('detected_red_flags', [])}")
            
            return analysis_result
            
        else:
            logger.error(f"Unsupported input content type: {scan.input_content_type}")
            scan.status = "failed"
            scan.error_message = f"Unsupported input content type: {scan.input_content_type}"
            db.add(scan)
            db.commit()
            return None
    
    except Exception as e:
        logger.error(f"Error processing scan {scan.id}: {str(e)}")
        scan.status = "failed"
        scan.error_message = str(e)
        db.add(scan)
        db.commit()
        return None

async def test_multi_scan_session_with_real_ai():
    """Test a multi-image scan session with real AI analysis."""
    logger.info("Testing Multi-Image Scan Session with Real AI")
    
    # Create test images
    for i, path in enumerate(TEST_IMAGE_PATHS):
        with open(path, "w") as f:
            f.write(f"This is test image {i+1}")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create a premium tier test user
        unique_email = f"test_premium_user_{int(time.time())}@example.com"
        test_user = User(
            email=unique_email,
            hashed_password="hashed_password",
            subscription_tier="premium",
            monthly_scan_allowance=100,
            scans_this_month=0,
            scan_counter_reset_at=datetime.now(timezone.utc)
        )
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        logger.info(f"Created premium test user with ID {test_user.id}")
        
        # Create a scan session
        session = crud_scan_session.create_scan_session(
            db=db, owner_id=test_user.id, title="Multi-Image Scan Session Test"
        )
        session_id = session.id
        logger.info(f"Created scan session with ID {session_id}")
        
        # Track scan credits before
        scans_before = test_user.scans_this_month
        logger.info(f"Scans before: {scans_before}")
        
        # Submit and process scans sequentially
        all_scans = []
        
        # Submit Image 1
        logger.info("Submitting Image 1")
        scan1 = Scan(
            owner_id=test_user.id,
            status="pending",
            input_content_type="image_path",
            user_provided_context=USER_CONTEXT,
            raw_input_payload={"file_path": TEST_IMAGE_PATHS[0], "is_session_followup": False},
            scan_session_id=session_id
        )
        db.add(scan1)
        db.commit()
        db.refresh(scan1)
        logger.info(f"Created scan 1 with ID {scan1.id}")
        
        # Update session activity
        crud_scan_session.update_scan_session_activity(db=db, db_session=session)
        
        # Update user scan count for first scan
        test_user.scans_this_month += 1
        db.commit()
        db.refresh(test_user)
        
        # Process Image 1
        await process_scan_with_real_ai(db, scan1, "premium", False)
        all_scans.append(scan1)
        
        # Verify scan credit consumption
        logger.info(f"Scans after Image 1: {test_user.scans_this_month}")
        logger.info(f"Scan credit consumed: {test_user.scans_this_month - scans_before}")
        
        # Submit and process Image 2
        logger.info("Submitting Image 2")
        scan2 = Scan(
            owner_id=test_user.id,
            status="pending",
            input_content_type="image_path",
            raw_input_payload={"file_path": TEST_IMAGE_PATHS[1], "is_session_followup": True},
            scan_session_id=session_id
        )
        db.add(scan2)
        db.commit()
        db.refresh(scan2)
        logger.info(f"Created scan 2 with ID {scan2.id}")
        
        # Update session activity
        crud_scan_session.update_scan_session_activity(db=db, db_session=session)
        
        # Process Image 2
        previous_scans = all_scans.copy()
        await process_scan_with_real_ai(db, scan2, "premium", False, previous_scans)
        all_scans.append(scan2)
        
        # Verify scan credit consumption (should not increase for premium user)
        db.refresh(test_user)
        logger.info(f"Scans after Image 2: {test_user.scans_this_month}")
        logger.info(f"Scan credit consumed for second scan: {test_user.scans_this_month - (scans_before + 1)}")
        
        # Submit and process Image 3
        logger.info("Submitting Image 3")
        scan3 = Scan(
            owner_id=test_user.id,
            status="pending",
            input_content_type="image_path",
            raw_input_payload={"file_path": TEST_IMAGE_PATHS[2], "is_session_followup": True},
            scan_session_id=session_id
        )
        db.add(scan3)
        db.commit()
        db.refresh(scan3)
        logger.info(f"Created scan 3 with ID {scan3.id}")
        
        # Update session activity
        crud_scan_session.update_scan_session_activity(db=db, db_session=session)
        
        # Process Image 3
        previous_scans = all_scans.copy()
        await process_scan_with_real_ai(db, scan3, "premium", False, previous_scans)
        all_scans.append(scan3)
        
        # Verify scan credit consumption (should not increase for premium user)
        db.refresh(test_user)
        logger.info(f"Scans after Image 3: {test_user.scans_this_month}")
        logger.info(f"Scan credit consumed for third scan: {test_user.scans_this_month - (scans_before + 1)}")
        
        # Submit and process Image 4
        logger.info("Submitting Image 4")
        scan4 = Scan(
            owner_id=test_user.id,
            status="pending",
            input_content_type="image_path",
            raw_input_payload={"file_path": TEST_IMAGE_PATHS[3], "is_session_followup": True},
            scan_session_id=session_id
        )
        db.add(scan4)
        db.commit()
        db.refresh(scan4)
        logger.info(f"Created scan 4 with ID {scan4.id}")
        
        # Update session activity
        crud_scan_session.update_scan_session_activity(db=db, db_session=session)
        
        # Process Image 4
        previous_scans = all_scans.copy()
        await process_scan_with_real_ai(db, scan4, "premium", False, previous_scans)
        all_scans.append(scan4)
        
        # Verify scan credit consumption (should not increase for premium user)
        db.refresh(test_user)
        logger.info(f"Scans after Image 4: {test_user.scans_this_month}")
        logger.info(f"Total scan credits consumed: {test_user.scans_this_month - scans_before}")
        
        # Retrieve the final session details
        final_session = crud_scan_session.get_scan_session(
            db=db, session_id=session_id, owner_id=test_user.id
        )
        
        logger.info(f"Final session details:")
        logger.info(f"Session ID: {final_session.id}")
        logger.info(f"Session title: {final_session.title}")
        logger.info(f"Session created at: {final_session.created_at}")
        logger.info(f"Session last activity at: {final_session.last_activity_at}")
        
        # Verify all scans are associated with this session
        session_scans = db.query(Scan).filter(Scan.scan_session_id == session_id).all()
        logger.info(f"Number of scans in session: {len(session_scans)}")
        logger.info(f"Scan IDs in session: {[scan.id for scan in session_scans]}")
        
        # Print detailed analysis results for each scan
        for i, scan in enumerate(session_scans):
            logger.info(f"\nDetailed Analysis for Scan {i+1} (ID: {scan.id}):")
            if scan.analysis_result:
                logger.info(f"Risk Score: {scan.analysis_result.get('risk_score', 'N/A')}")
                logger.info(f"Explanation: {scan.analysis_result.get('explanation', 'N/A')}")
                logger.info(f"Red Flags: {scan.analysis_result.get('detected_red_flags', [])}")
                logger.info(f"Recommendations: {scan.analysis_result.get('recommendations', 'N/A')}")
                logger.info(f"Confidence Level: {scan.analysis_result.get('confidence_level', 'N/A')}")
                logger.info(f"Model Used: {scan.analysis_result.get('model_used', 'N/A')}")
            else:
                logger.info(f"No analysis result available")
        
        # Clean up
        for scan in session_scans:
            db.delete(scan)
        db.delete(final_session)
        db.delete(test_user)
        db.commit()
        logger.info("Test data deleted")
        
    finally:
        db.close()
        
        # Delete the test images
        for path in TEST_IMAGE_PATHS:
            if os.path.exists(path):
                os.remove(path)
                logger.info(f"Deleted test image: {path}")

def main():
    """Run the test."""
    asyncio.run(test_multi_scan_session_with_real_ai())

if __name__ == "__main__":
    main()
