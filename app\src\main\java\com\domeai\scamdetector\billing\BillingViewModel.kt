package com.domeai.scamdetector.billing

import android.app.Activity
import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.android.billingclient.api.ProductDetails
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

/**
 * ViewModel for billing operations.
 */
@HiltViewModel
class BillingViewModel @Inject constructor(
    private val billingRepository: BillingRepository
) : ViewModel() {
    
    /**
     * Get the subscription products.
     */
    val subscriptionProducts: LiveData<List<ProductDetails>> = billingRepository.getSubscriptionProducts()
    
    /**
     * Get the user's subscription status.
     */
    val userSubscriptionStatus: StateFlow<SubscriptionStatus> = billingRepository.getUserSubscriptionStatus()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = SubscriptionStatus.Free
        )
    
    /**
     * Launch the purchase flow for a subscription.
     */
    fun launchSubscriptionPurchaseFlow(activity: Activity, productId: String) {
        billingRepository.launchSubscriptionPurchaseFlow(activity, productId)
    }
    
    /**
     * Get the subscription product details by ID.
     */
    fun getSubscriptionProductDetails(productId: String): ProductDetails? {
        return billingRepository.getSubscriptionProductDetails(productId)
    }
    
    /**
     * Check if the user has a premium subscription.
     */
    fun hasPremiumSubscription(): Boolean {
        return userSubscriptionStatus.value == SubscriptionStatus.Premium || 
               userSubscriptionStatus.value == SubscriptionStatus.Expert
    }
    
    /**
     * Check if the user has an expert subscription.
     */
    fun hasExpertSubscription(): Boolean {
        return userSubscriptionStatus.value == SubscriptionStatus.Expert
    }
}
