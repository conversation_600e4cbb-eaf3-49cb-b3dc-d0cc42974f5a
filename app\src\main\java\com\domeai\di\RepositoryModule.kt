package com.domeai.di

import com.domeai.data.repository.AuthRepository
import com.domeai.data.repository.AuthRepositoryImpl
import com.domeai.data.repository.FakePrivacyRepository
import com.domeai.data.repository.FakeSubscriptionRepository
import com.domeai.data.repository.OverlayRepository
import com.domeai.data.repository.OverlayRepositoryImpl
import com.domeai.data.repository.PrivacyRepository
import com.domeai.data.repository.ScanRepository
import com.domeai.data.repository.ScanRepositoryImpl
import com.domeai.data.repository.SubscriptionRepository
import com.domeai.data.repository.SubscriptionRepositoryImpl
import com.domeai.data.repository.UserRepository
import com.domeai.data.repository.UserRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    @Binds
    @Singleton
    abstract fun bindAuthRepository(
        authRepositoryImpl: AuthRepositoryImpl
    ): AuthRepository

    @Binds
    @Singleton
    abstract fun bindOverlayRepository(
        overlayRepositoryImpl: OverlayRepositoryImpl
    ): OverlayRepository

    @Binds
    @Singleton
    abstract fun bindUserRepository(
        userRepositoryImpl: UserRepositoryImpl
    ): UserRepository

    @Binds
    @Singleton
    abstract fun bindPrivacyRepository(
        fakePrivacyRepository: FakePrivacyRepository
    ): PrivacyRepository

    @Binds
    @Singleton
    abstract fun bindSubscriptionRepository(
        subscriptionRepositoryImpl: SubscriptionRepositoryImpl
    ): SubscriptionRepository

    @Binds
    @Singleton
    abstract fun bindScanRepository(
        scanRepositoryImpl: ScanRepositoryImpl
    ): ScanRepository
}
