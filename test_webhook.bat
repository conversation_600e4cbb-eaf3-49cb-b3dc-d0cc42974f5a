@echo off
echo Testing Google Play RTDN endpoint...

curl -X POST http://localhost:8000/api/v1/webhooks/googleplay/rtdn ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":{\"data\":\"eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJjb20uZG9tZWFpLnNjYW1kZXRlY3RvciIsImV2ZW50VGltZU1pbGxpcyI6IjE2MjEyMzQ1Njc4OTAiLCJ0ZXN0Tm90aWZpY2F0aW9uIjp7InZlcnNpb24iOiIxLjAifX0=\",\"messageId\":\"test-message-id\",\"publishTime\":\"2023-05-20T10:00:00.000Z\"},\"subscription\":\"projects/domeai-project/subscriptions/google-play-rtdn-subscription\"}"

echo.
echo Press any key to exit...
pause > nul
