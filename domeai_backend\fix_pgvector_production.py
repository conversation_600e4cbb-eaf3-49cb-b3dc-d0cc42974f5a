#!/usr/bin/env python3
"""
Fix pgvector extension and knowledge base table in production
"""
import os
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def fix_pgvector():
    """Fix pgvector extension and table structure"""
    
    # Get database URL from environment
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("❌ DATABASE_URL environment variable not found")
        return
    
    print("🔧 Fixing pgvector extension and table structure...")
    
    try:
        # Connect to database
        conn = psycopg2.connect(database_url)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        print("✅ Connected to database")
        
        # 1. Enable pgvector extension
        print("1. Enabling pgvector extension...")
        cursor.execute("CREATE EXTENSION IF NOT EXISTS vector;")
        print("✅ pgvector extension enabled")
        
        # 2. Check current table structure
        print("2. Checking current table structure...")
        cursor.execute("""
            SELECT column_name, data_type, udt_name 
            FROM information_schema.columns 
            WHERE table_name = 'knowledge_base_chunks' 
            ORDER BY ordinal_position
        """)
        columns = cursor.fetchall()
        
        embedding_column_type = None
        for col in columns:
            print(f"   Column: {col[0]} - Type: {col[1]} ({col[2]})")
            if col[0] == 'embedding':
                embedding_column_type = col[2]
        
        # 3. Fix embedding column if needed
        if embedding_column_type == '_float8':  # This is FLOAT[] type
            print("3. Converting embedding column from FLOAT[] to vector(1536)...")
            
            # Check if we have data
            cursor.execute("SELECT COUNT(*) FROM knowledge_base_chunks")
            count = cursor.fetchone()[0]
            print(f"   Found {count} existing rows")
            
            if count > 0:
                # Create backup and convert
                cursor.execute("""
                    -- Add new vector column
                    ALTER TABLE knowledge_base_chunks ADD COLUMN embedding_vector vector(1536);
                    
                    -- Convert existing data (if any)
                    UPDATE knowledge_base_chunks 
                    SET embedding_vector = embedding::vector(1536) 
                    WHERE embedding IS NOT NULL;
                    
                    -- Drop old column
                    ALTER TABLE knowledge_base_chunks DROP COLUMN embedding;
                    
                    -- Rename new column
                    ALTER TABLE knowledge_base_chunks RENAME COLUMN embedding_vector TO embedding;
                """)
                print("✅ Converted embedding column to vector(1536)")
            else:
                # No data, just recreate column
                cursor.execute("""
                    ALTER TABLE knowledge_base_chunks DROP COLUMN embedding;
                    ALTER TABLE knowledge_base_chunks ADD COLUMN embedding vector(1536);
                """)
                print("✅ Recreated embedding column as vector(1536)")
                
        elif embedding_column_type == 'vector':
            print("✅ Embedding column is already vector type")
        else:
            print(f"⚠️ Unknown embedding column type: {embedding_column_type}")
        
        # 4. Create proper index
        print("4. Creating pgvector index...")
        cursor.execute("DROP INDEX IF EXISTS knowledge_base_chunks_embedding_idx;")
        cursor.execute("""
            CREATE INDEX knowledge_base_chunks_embedding_idx
            ON knowledge_base_chunks
            USING ivfflat (embedding vector_cosine_ops)
            WITH (lists = 100);
        """)
        print("✅ Created pgvector index")
        
        # 5. Test vector operations
        print("5. Testing vector operations...")
        test_vector = "[" + ",".join(["0.1"] * 1536) + "]"
        
        # Test cosine distance
        cursor.execute("""
            SELECT 1 as test_id, 1 - (%s::vector(1536) <-> %s::vector(1536)) as similarity
        """, (test_vector, test_vector))
        result = cursor.fetchone()
        print(f"✅ Cosine distance test: similarity = {result[1]}")
        
        # Test inner product
        cursor.execute("""
            SELECT 1 as test_id, -1 * (%s::vector(1536) <#> %s::vector(1536)) as similarity
        """, (test_vector, test_vector))
        result = cursor.fetchone()
        print(f"✅ Inner product test: similarity = {result[1]}")
        
        print("🎉 pgvector fix completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    fix_pgvector()
