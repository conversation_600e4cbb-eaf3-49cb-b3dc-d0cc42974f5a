package com.domeai.di

import com.domeai.data.network.AuthApiService
import com.domeai.data.network.AuthInterceptor
import com.domeai.data.network.ScanApiService
import com.domeai.data.network.SubscriptionApiService
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    // Base URL for the API
    // Production backend deployed on Render.com
    private const val BASE_URL = "https://domeai-backend.onrender.com/"

    // Local development URLs (commented out)
    // private const val BASE_URL = "http://********:8000/"  // For emulator
    // private const val BASE_URL = "http://************:8000/"  // For physical device

    @Provides
    @Singleton
    fun provideGson(): Gson {
        return GsonBuilder().create()
    }

    @Provides
    @Singleton
    fun provideHttpLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
    }

    @Provides
    @Singleton
    fun provideOkHttpClient(
        loggingInterceptor: HttpLoggingInterceptor,
        authInterceptor: AuthInterceptor
    ): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .addInterceptor(authInterceptor)
            .connectTimeout(60, TimeUnit.SECONDS)  // Increased for production
            .readTimeout(120, TimeUnit.SECONDS)    // Increased for AI processing
            .writeTimeout(60, TimeUnit.SECONDS)    // Increased for image uploads
            .build()
    }

    @Provides
    @Singleton
    fun provideRetrofit(gson: Gson, okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BASE_URL)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .client(okHttpClient)
            .build()
    }

    @Provides
    @Singleton
    fun provideAuthApiService(retrofit: Retrofit): AuthApiService {
        return retrofit.create(AuthApiService::class.java)
    }

    @Provides
    @Singleton
    fun provideScanApiService(retrofit: Retrofit): ScanApiService {
        return retrofit.create(ScanApiService::class.java)
    }

    @Provides
    @Singleton
    fun provideSubscriptionApiService(retrofit: Retrofit): SubscriptionApiService {
        return retrofit.create(SubscriptionApiService::class.java)
    }
}
