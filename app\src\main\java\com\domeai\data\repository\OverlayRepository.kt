package com.domeai.data.repository

import com.domeai.data.model.OverlayServiceState
import com.domeai.data.model.ScanResult
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for overlay service operations
 */
interface OverlayRepository {
    /**
     * Get the current state of the overlay service
     */
    fun getOverlayServiceState(): Flow<OverlayServiceState>
    
    /**
     * Enable or disable the overlay service
     */
    suspend fun setOverlayServiceEnabled(enabled: Boolean)
    
    /**
     * Check if the overlay permission is granted
     */
    suspend fun checkOverlayPermission(): Boolean
    
    /**
     * Get recent scan results
     */
    fun getRecentScans(limit: Int = 10): Flow<List<ScanResult>>
    
    /**
     * Get a specific scan result by ID
     */
    suspend fun getScanById(id: String): ScanResult?
}
