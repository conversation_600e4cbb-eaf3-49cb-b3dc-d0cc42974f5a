#!/usr/bin/env python3
"""
Quick script to check scan 97 results
"""
import requests
import json

API_BASE_URL = "https://domeai-backend.onrender.com"

def check_scan_97():
    # Login first
    login_url = f"{API_BASE_URL}/api/v1/auth/login"
    login_data = {
        "username": "<EMAIL>",
        "password": "@#48CrVGt3"
    }
    
    print("Logging in...")
    response = requests.post(login_url, data=login_data)
    if response.status_code != 200:
        print(f"Login failed: {response.status_code}")
        return
    
    token = response.json()["access_token"]
    print("✅ Login successful")
    
    # Check scan 97
    scan_url = f"{API_BASE_URL}/api/v1/scans/97"
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\nChecking scan 97...")
    response = requests.get(scan_url, headers=headers)
    
    if response.status_code == 200:
        scan_data = response.json()
        print(f"Status: {scan_data.get('status')}")
        print(f"User Context: {scan_data.get('user_provided_context')}")
        
        analysis = scan_data.get('analysis_result')
        if analysis:
            print(f"\nAnalysis Result Keys: {list(analysis.keys())}")
            print(f"Risk Score: {analysis.get('risk_score')}")
            print(f"Explanation: {analysis.get('explanation', '')[:200]}...")
            print(f"Red Flags: {analysis.get('detected_red_flags', [])}")
            print(f"Recommendations: {analysis.get('recommendations', [])}")
            print(f"Knowledge Base References: {analysis.get('knowledge_base_references', [])}")
        else:
            print("No analysis result found")
    else:
        print(f"Failed to get scan: {response.status_code}")
        print(response.text)

if __name__ == "__main__":
    check_scan_97()
