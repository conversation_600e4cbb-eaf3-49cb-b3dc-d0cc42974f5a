"""
Create a test user with Google Play subscription data.

This script creates a test user in the database with Google Play subscription data.
"""
import sys
import os
import logging
from datetime import datetime, timezone, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.user import User
from app.core.config import settings
from app.core.security import get_password_hash

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Test data
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "password123"
TEST_PURCHASE_TOKEN = "test_purchase_token_123456789"
TEST_SUBSCRIPTION_ID = "domeai_placeholder_premium_monthly"
TEST_ORDER_ID = "GPA.1234-5678-9012-3456"


def create_test_user():
    """
    Create a test user with Google Play subscription data.
    """
    # Create database connection
    engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Check if the user already exists
        existing_user = db.query(User).filter(User.email == TEST_EMAIL).first()
        if existing_user:
            logger.info(f"User {TEST_EMAIL} already exists. Updating subscription data.")
            user = existing_user
        else:
            logger.info(f"Creating new user {TEST_EMAIL}")
            user = User(
                email=TEST_EMAIL,
                hashed_password=get_password_hash(TEST_PASSWORD),
                is_active=True
            )
            db.add(user)
            db.commit()
            db.refresh(user)
        
        # Update user's subscription data
        user.subscription_provider = "google_play"
        user.provider_subscription_id = TEST_ORDER_ID
        user.google_play_purchase_token = TEST_PURCHASE_TOKEN
        user.subscription_product_id = TEST_SUBSCRIPTION_ID
        user.subscription_tier = "premium"
        user.subscription_expiry_date = datetime.now(timezone.utc) + timedelta(days=30)
        user.auto_renew_status = True
        user.is_trial_period = False
        user.monthly_scan_allowance = 100
        user.expert_scan_allowance = 0
        user.scans_this_month = 0
        user.expert_scans_this_month = 0
        user.scan_counter_reset_at = datetime.now(timezone.utc)
        user.last_rtdn_received_at = datetime.now(timezone.utc)
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        logger.info(f"User {TEST_EMAIL} created/updated with Google Play subscription data.")
        logger.info(f"User ID: {user.id}")
        logger.info(f"Subscription Tier: {user.subscription_tier}")
        logger.info(f"Subscription Expiry: {user.subscription_expiry_date}")
        logger.info(f"Purchase Token: {user.google_play_purchase_token}")
        logger.info(f"Order ID: {user.provider_subscription_id}")
        
    except Exception as e:
        logger.error(f"Error creating test user: {str(e)}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    create_test_user()
