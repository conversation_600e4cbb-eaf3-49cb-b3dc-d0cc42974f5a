package com.domeai.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import androidx.core.app.NotificationCompat
import com.domeai.R
import com.domeai.data.repository.OverlayRepository
import com.domeai.presentation.main.MainActivity
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Service for displaying an overlay button on top of other apps
 */
@AndroidEntryPoint
class OverlayService : Service() {

    companion object {
        private const val NOTIFICATION_CHANNEL_ID = "overlay_service_channel"
        private const val NOTIFICATION_ID = 1

        // Intent actions
        const val ACTION_START = "com.domeai.service.ACTION_START"
        const val ACTION_STOP = "com.domeai.service.ACTION_STOP"

        // Intent to start the service
        fun getStartIntent(context: Context): Intent {
            return Intent(context, OverlayService::class.java).apply {
                action = ACTION_START
            }
        }

        // Intent to stop the service
        fun getStopIntent(context: Context): Intent {
            return Intent(context, OverlayService::class.java).apply {
                action = ACTION_STOP
            }
        }
    }

    @Inject
    lateinit var overlayRepository: OverlayRepository

    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private var params: WindowManager.LayoutParams? = null

    private var initialX: Int = 0
    private var initialY: Int = 0
    private var initialTouchX: Float = 0f
    private var initialTouchY: Float = 0f

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START -> startOverlay()
            ACTION_STOP -> stopOverlay()
            else -> {
                // If no action is specified, start the overlay anyway
                android.util.Log.d("OverlayService", "No action specified, starting overlay by default")
                startOverlay()
            }
        }

        // Return START_STICKY to ensure the service restarts if it's killed
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        super.onDestroy()
        stopOverlay()
        serviceScope.cancel()
    }

    private fun startOverlay() {
        try {
            android.util.Log.d("OverlayService", "Starting overlay service")

            // Start as foreground service first to avoid ANR
            startForeground(NOTIFICATION_ID, createNotification())

            android.util.Log.d("OverlayService", "Started as foreground service")

            // Skip the repository check and directly add the overlay view
            // This ensures the button appears regardless of state issues
            android.util.Log.d("OverlayService", "Directly adding overlay view")
            addOverlayView()

            // Also check the state for logging purposes
            serviceScope.launch {
                try {
                    val state = overlayRepository.getOverlayServiceState().first()
                    android.util.Log.d("OverlayService", "Got overlay state: enabled=${state.isEnabled}, hasPermission=${state.hasPermission}")

                    if (!state.isEnabled || !state.hasPermission) {
                        android.util.Log.d("OverlayService", "Warning: Overlay not enabled or no permission, but showing anyway")
                    }
                } catch (e: Exception) {
                    android.util.Log.e("OverlayService", "Error checking overlay state: ${e.message}")
                    e.printStackTrace()
                }
            }
        } catch (e: Exception) {
            // Log the error and stop the service
            android.util.Log.e("OverlayService", "Error in startOverlay: ${e.message}")
            e.printStackTrace()
            stopSelf()
        }
    }

    private fun stopOverlay() {
        try {
            removeOverlayView()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                stopForeground(STOP_FOREGROUND_REMOVE)
            } else {
                stopForeground(true)
            }
            stopSelf()
        } catch (e: Exception) {
            android.util.Log.e("OverlayService", "Error stopping overlay: ${e.message}")
            stopSelf()
        }
    }

    private fun addOverlayView() {
        try {
            if (overlayView != null) return

            // Check if we have the overlay permission
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
                android.util.Log.e("OverlayService", "No overlay permission")
                stopSelf()
                return
            }

            // Create a custom overlay button with a shield icon
            val inflater = LayoutInflater.from(this)
            overlayView = inflater.inflate(R.layout.overlay_button, null)

            // Get screen dimensions to position the button properly
            val displayMetrics = resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels

            // Set up window parameters
            params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT, // Width
                WindowManager.LayoutParams.WRAP_CONTENT, // Height
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                },
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT
            ).apply {
                gravity = Gravity.TOP or Gravity.START
                // Position the button at the right edge of the screen initially
                x = screenWidth - 80 // Right edge minus button width with some padding
                y = 200
            }

            // Set up touch listener for dragging
            overlayView?.setOnTouchListener { _, event ->
                try {
                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            initialX = params?.x ?: 0
                            initialY = params?.y ?: 0
                            initialTouchX = event.rawX
                            initialTouchY = event.rawY
                            true
                        }
                        MotionEvent.ACTION_MOVE -> {
                            // Calculate new position
                            params?.x = initialX + (event.rawX - initialTouchX).toInt()
                            params?.y = initialY + (event.rawY - initialTouchY).toInt()

                            // Update the view position
                            windowManager?.updateViewLayout(overlayView, params)
                            true
                        }
                        MotionEvent.ACTION_UP -> {
                            val moved = Math.abs(event.rawX - initialTouchX) > 10 ||
                                    Math.abs(event.rawY - initialTouchY) > 10

                            if (moved) {
                                // Snap to edge after dragging
                                snapToEdge()
                            } else {
                                try {
                                    // Handle click - take screenshot or show manual scan UI
                                    // For MVP, we'll just open the app
                                    val intent = Intent(this@OverlayService, MainActivity::class.java).apply {
                                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                    }
                                    startActivity(intent)
                                } catch (e: Exception) {
                                    android.util.Log.e("OverlayService", "Error starting activity: ${e.message}")
                                }
                            }
                            true
                        }
                        else -> false
                    }
                } catch (e: Exception) {
                    android.util.Log.e("OverlayService", "Error in touch listener: ${e.message}")
                    false
                }
            }

            // Add the view to window manager
            windowManager?.addView(overlayView, params)
            android.util.Log.d("OverlayService", "Overlay view added successfully")
        } catch (e: Exception) {
            android.util.Log.e("OverlayService", "Error adding overlay view: ${e.message}")
            stopSelf()
        }
    }

    /**
     * Snap the overlay button to the nearest edge of the screen
     */
    private fun snapToEdge() {
        try {
            // Get screen dimensions
            val displayMetrics = resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val screenHeight = displayMetrics.heightPixels

            // Get button dimensions
            val buttonWidth = overlayView?.width ?: 100
            val buttonHeight = overlayView?.height ?: 100

            // Calculate distances to edges
            val distanceToLeft = params?.x ?: 0
            val distanceToRight = screenWidth - (distanceToLeft + buttonWidth)
            val distanceToTop = params?.y ?: 0
            val distanceToBottom = screenHeight - (distanceToTop + buttonHeight)

            // Find the closest edge
            val minHorizontalDistance = Math.min(distanceToLeft, distanceToRight)
            val minVerticalDistance = Math.min(distanceToTop, distanceToBottom)

            // Snap to the closest edge
            if (minHorizontalDistance < minVerticalDistance) {
                // Snap horizontally
                if (distanceToLeft < distanceToRight) {
                    // Snap to left
                    params?.x = 0
                } else {
                    // Snap to right
                    params?.x = screenWidth - buttonWidth
                }
            } else {
                // Snap vertically
                if (distanceToTop < distanceToBottom) {
                    // Snap to top
                    params?.y = 0
                } else {
                    // Snap to bottom
                    params?.y = screenHeight - buttonHeight
                }
            }

            // Update the view position
            windowManager?.updateViewLayout(overlayView, params)

            // Log the new position
            android.util.Log.d("OverlayService", "Snapped to edge: x=${params?.x}, y=${params?.y}")
        } catch (e: Exception) {
            android.util.Log.e("OverlayService", "Error snapping to edge: ${e.message}")
        }
    }

    private fun removeOverlayView() {
        try {
            overlayView?.let {
                try {
                    windowManager?.removeView(it)
                } catch (e: Exception) {
                    android.util.Log.e("OverlayService", "Error removing view: ${e.message}")
                }
                overlayView = null
            }
        } catch (e: Exception) {
            android.util.Log.e("OverlayService", "Error in removeOverlayView: ${e.message}")
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Overlay Service"
            val descriptionText = "Shows an overlay button for scanning"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(NOTIFICATION_CHANNEL_ID, name, importance).apply {
                description = descriptionText
                setShowBadge(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): android.app.Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setContentTitle("DomeAI Scam Detector")
            .setContentText("Overlay protection is active")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .build()
    }
}
