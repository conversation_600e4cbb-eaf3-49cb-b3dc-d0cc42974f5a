import uuid
from datetime import datetime
from typing import Dict, List, Optional, Union, Any

from pydantic import BaseModel, Field, field_validator, model_validator


class ScanResultData(BaseModel):
    """Schema for scan analysis results."""
    risk_score: Optional[float] = None
    detected_red_flags: Optional[List[str]] = None
    explanation: Optional[str] = None
    recommendations: Optional[str] = None
    confidence_level: Optional[str] = None
    model_used: Optional[str] = None
    overall_session_assessment: Optional[str] = None  # Assessment of the entire session context
    key_findings: Optional[str] = None  # Key insights and findings from the analysis
    knowledge_base_references: Optional[str] = None  # How KB information influenced the analysis
    is_general_question: Optional[bool] = False  # True if this was a session message, not a full scan


class ScanBase(BaseModel):
    """Base schema for scan data."""
    input_text: Optional[str] = None
    input_url: Optional[str] = None
    input_content_type: str  # e.g., "text", "image_path", "url"
    user_provided_context: Optional[str] = None


class ScanCreate(ScanBase):
    """Schema for creating a new scan request."""
    scan_session_id: Optional[uuid.UUID] = None

    @model_validator(mode='after')
    def validate_input_type(self) -> 'ScanCreate':
        """Validate that the appropriate input field is provided based on input_content_type."""
        if self.input_content_type == "text" and not self.input_text:
            raise ValueError("input_text is required when input_content_type is 'text'")
        elif self.input_content_type == "url" and not self.input_url:
            raise ValueError("input_url is required when input_content_type is 'url'")
        # Note: For image_path, the file_path is added to raw_input_payload after upload
        elif self.input_content_type == "image_path" and self.input_text:
            raise ValueError("input_text should not be provided when input_content_type is 'image_path'")

        return self


class Scan(ScanBase):
    """Schema for returning scan information from the DB."""
    id: int
    owner_id: int
    status: str  # e.g., "pending", "processing", "completed", "failed"
    created_at: datetime
    updated_at: datetime
    raw_input_payload: Optional[Dict[str, Any]] = None  # To store the original received data
    analysis_result: Optional[ScanResultData] = None  # Will be populated by the AI worker
    error_message: Optional[str] = None  # If processing failed
    scan_session_id: Optional[uuid.UUID] = None  # ID of the scan session this scan belongs to

    class Config:
        orm_mode = True


class ScanInDB(Scan):
    """Schema for internal representation of a scan in the database."""
    pass
