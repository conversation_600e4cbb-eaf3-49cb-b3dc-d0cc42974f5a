#!/usr/bin/env python
"""
Script to test pgvector search functionality.
"""

import asyncio
import logging
import sys
import os
import time
from typing import List

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.knowledge_base import KnowledgeBaseChunk
from app.crud import crud_kb
from app.services.ai_service_factory import get_ai_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

async def test_pgvector_search():
    """Test pgvector search functionality."""
    logger.info("Testing pgvector search functionality")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Get all knowledge base chunks
        all_chunks = db.query(KnowledgeBaseChunk).all()
        logger.info(f"Found {len(all_chunks)} knowledge base chunks")
        
        # Print the first few chunks
        for i, chunk in enumerate(all_chunks[:5]):
            logger.info(f"Chunk {i+1}: ID={chunk.id}, Source={chunk.source}")
            logger.info(f"Content: {chunk.content[:100]}...")
            if hasattr(chunk, 'embedding') and chunk.embedding:
                logger.info(f"Embedding dimensions: {len(chunk.embedding)}")
            else:
                logger.info("No embedding found")
        
        # Get a sample chunk to use as a query
        sample_chunk = None
        for chunk in all_chunks:
            if chunk.source and "cashier_check" in chunk.source:
                sample_chunk = chunk
                break
        
        if not sample_chunk:
            logger.warning("No sample chunk found with 'cashier_check' in source")
            # Use the first chunk as a sample
            if all_chunks:
                sample_chunk = all_chunks[0]
            else:
                logger.error("No chunks found in the database")
                return
        
        logger.info(f"Using sample chunk with ID={sample_chunk.id}, Source={sample_chunk.source}")
        logger.info(f"Sample chunk content: {sample_chunk.content[:100]}...")
        
        # Get the embedding for the sample chunk
        sample_embedding = sample_chunk.embedding
        
        # Test the find_similar_kb_chunks function
        logger.info("Testing find_similar_kb_chunks with sample chunk embedding")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=sample_embedding,
            top_k=3
        )
        
        logger.info(f"Found {len(similar_chunks)} similar chunks")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Similar chunk {i+1}: ID={chunk.id}, Source={chunk.source}")
            if hasattr(chunk, 'similarity_score'):
                logger.info(f"Similarity score: {chunk.similarity_score}")
            logger.info(f"Content: {chunk.content[:100]}...")
        
        # Test with a new embedding from the AI service
        logger.info("Testing with a new embedding from the AI service")
        ai_service = get_ai_service(user_tier="premium")
        
        # Generate a new embedding for a test query
        test_query = "I received a cashier's check for more than the amount I was selling my item for. The buyer asked me to wire back the difference. Is this legitimate?"
        logger.info(f"Generating embedding for test query: {test_query}")
        
        embedding = await ai_service.get_text_embedding(text=test_query)
        logger.info(f"Generated embedding with {len(embedding)} dimensions")
        
        # Test the find_similar_kb_chunks function with the new embedding
        logger.info("Testing find_similar_kb_chunks with new embedding")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=embedding,
            top_k=3
        )
        
        logger.info(f"Found {len(similar_chunks)} similar chunks")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Similar chunk {i+1}: ID={chunk.id}, Source={chunk.source}")
            if hasattr(chunk, 'similarity_score'):
                logger.info(f"Similarity score: {chunk.similarity_score}")
            logger.info(f"Content: {chunk.content[:100]}...")
        
        # Test EXPLAIN ANALYZE to check if the index is being used
        logger.info("Testing EXPLAIN ANALYZE to check if the index is being used")
        from sqlalchemy import text
        
        # Convert the embedding list to a string representation for SQL
        embedding_str = str(embedding).replace('[', '{').replace(']', '}')
        
        # Use raw SQL with EXPLAIN ANALYZE
        query = text(f"""
            EXPLAIN ANALYZE
            SELECT id, content, source, created_at,
                   1 - (embedding <-> :embedding) AS similarity_score
            FROM knowledge_base_chunks
            ORDER BY embedding <-> :embedding
            LIMIT :limit
        """)
        
        # Execute the query with parameters
        result = db.execute(
            query,
            {"embedding": embedding_str, "limit": 3}
        )
        
        # Print the query plan
        logger.info("Query plan:")
        for row in result:
            logger.info(row[0])
        
    finally:
        db.close()

def main():
    """Run the test."""
    asyncio.run(test_pgvector_search())

if __name__ == "__main__":
    main()
