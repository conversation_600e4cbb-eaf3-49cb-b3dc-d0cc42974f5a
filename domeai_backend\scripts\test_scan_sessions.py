#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test the scan session functionality.
"""

import asyncio
import logging
import sys
import uuid
from datetime import datetime, timezone

# Add the parent directory to the path so we can import from app
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan_session import ScanSession
from app.models.scan import Scan
from app.crud import crud_scan_session

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def test_create_scan_session():
    """Test creating a scan session."""
    logger.info("Testing Create Scan Session")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create a test user
        test_user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            subscription_tier="premium",
            monthly_scan_allowance=100,
            scans_this_month=0,
            scan_counter_reset_at=datetime.now(timezone.utc)
        )
        
        # Add the user to the database
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        logger.info(f"Created test user with ID {test_user.id}")
        
        # Create a scan session
        session = crud_scan_session.create_scan_session(
            db=db, owner_id=test_user.id, title="Test Session"
        )
        
        logger.info(f"Created scan session with ID {session.id}")
        logger.info(f"Session title: {session.title}")
        logger.info(f"Session owner ID: {session.owner_id}")
        logger.info(f"Session created at: {session.created_at}")
        logger.info(f"Session last activity at: {session.last_activity_at}")
        
        # Create a scan in the session
        scan1 = Scan(
            owner_id=test_user.id,
            status="completed",
            input_content_type="text",
            input_text="This is a test scan 1",
            scan_session_id=session.id
        )
        
        db.add(scan1)
        db.commit()
        db.refresh(scan1)
        
        logger.info(f"Created scan 1 with ID {scan1.id} in session {scan1.scan_session_id}")
        
        # Create another scan in the session
        scan2 = Scan(
            owner_id=test_user.id,
            status="completed",
            input_content_type="text",
            input_text="This is a test scan 2",
            scan_session_id=session.id
        )
        
        db.add(scan2)
        db.commit()
        db.refresh(scan2)
        
        logger.info(f"Created scan 2 with ID {scan2.id} in session {scan2.scan_session_id}")
        
        # Get previous scans in the session
        previous_scans = crud_scan_session.get_previous_scans_in_session(
            db=db, session_id=session.id, current_scan_id=scan2.id
        )
        
        logger.info(f"Found {len(previous_scans)} previous scans in session {session.id}")
        for prev_scan in previous_scans:
            logger.info(f"Previous scan ID: {prev_scan.id}, Text: {prev_scan.input_text}")
        
        # Update session activity
        updated_session = crud_scan_session.update_scan_session_activity(
            db=db, db_session=session
        )
        
        logger.info(f"Updated session last activity at: {updated_session.last_activity_at}")
        
        # Get all sessions for the user
        user_sessions = crud_scan_session.get_scan_sessions_by_owner(
            db=db, owner_id=test_user.id
        )
        
        logger.info(f"Found {len(user_sessions)} sessions for user {test_user.id}")
        for user_session in user_sessions:
            logger.info(f"Session ID: {user_session.id}, Title: {user_session.title}")
        
        # Clean up
        db.delete(scan1)
        db.delete(scan2)
        db.delete(session)
        db.delete(test_user)
        db.commit()
        logger.info("Test data deleted")
        
    finally:
        db.close()

def main():
    """Run the tests."""
    test_create_scan_session()

if __name__ == "__main__":
    main()
