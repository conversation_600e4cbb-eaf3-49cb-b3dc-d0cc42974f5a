#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test the full RAG pipeline with specific scenarios.
"""

import asyncio
import json
import logging
import sys
from typing import List, Dict, Any, Optional

# Add the parent directory to the path so we can import from app
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.database import SessionLocal
from app.services.ai_services import OpenAIModelService
from app.core.config import settings
from app.crud import crud_kb
from app.schemas.scan import ScanResultData

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

async def perform_scam_analysis_with_rag(
    content_type,
    text_content,
    image_content,
    source_app,
    source_url,
    text_embedding,
    similar_chunks,
    ai_service
):
    """
    Perform scam analysis with RAG.

    This is a simplified version of the function for testing purposes.
    """
    # Extract content from similar chunks
    knowledge_context = "\n\n".join([
        f"Source: {chunk.source}\n{chunk.content}"
        for chunk in similar_chunks
    ])

    # Construct a prompt for the AI service
    prompt = f"""
    You are an expert scam detector AI. Analyze the following content for potential scam indicators.

    CONTENT TO ANALYZE:
    {text_content}

    SOURCE APP: {source_app}
    SOURCE URL: {source_url}

    RELEVANT KNOWLEDGE BASE INFORMATION:
    {knowledge_context}

    Based on the content and the knowledge base information, provide a comprehensive scam analysis in JSON format with the following fields:

    1. risk_score: A number between 0.0 and 1.0, where 0.0 is definitely safe and 1.0 is definitely a scam.
    2. explanation: A detailed explanation of why you think this is or isn't a scam.
    3. red_flags: A list of specific red flags or suspicious elements in the content.
    4. recommendations: Specific recommendations for the user.
    5. confidence: Your confidence level in this analysis (Low, Medium, High).

    Format your response as a valid JSON object with these fields.
    """

    # Log the prompt
    logger.info("Prompt sent to GPT-4.1:")
    logger.info(prompt)

    # Call the AI service to analyze the content
    response = await ai_service.client.chat.completions.create(
        model="gpt-4.1",
        messages=[
            {"role": "system", "content": "You are an expert scam detector AI."},
            {"role": "user", "content": prompt}
        ],
        temperature=0.3,
        response_format={"type": "json_object"}
    )

    # Extract the response text
    response_text = response.choices[0].message.content

    # Log the raw response
    logger.info("Raw response from GPT-4.1:")
    logger.info(response_text)

    # Parse the JSON response
    try:
        response_json = json.loads(response_text)

        # Create a ScanResultData object
        result = ScanResultData(
            risk_score=response_json.get("risk_score", 0.0),
            explanation=response_json.get("explanation", ""),
            detected_red_flags=response_json.get("red_flags", []),
            recommendations=response_json.get("recommendations", "")
        )

        # Add confidence as an attribute
        setattr(result, 'confidence', response_json.get("confidence", "Medium"))

        return result

    except json.JSONDecodeError:
        logger.error("Failed to parse JSON response")
        # Return a default result
        result = ScanResultData(
            risk_score=0.5,
            explanation="Failed to parse AI response",
            detected_red_flags=["Error in AI analysis"],
            recommendations="Please try again later"
        )
        setattr(result, 'confidence', "Low")
        return result

async def test_scenario(scenario_name, text_content, source_app, source_url):
    """Test the full RAG pipeline with a specific scenario."""
    db = SessionLocal()
    try:
        # Initialize the OpenAI service
        ai_service = OpenAIModelService(api_key=settings.OPENAI_API_KEY)

        # Generate text embedding
        logger.info(f"Generating text embedding for {scenario_name}...")
        text_embedding = await ai_service.get_text_embedding(text=text_content)

        # Find similar KB chunks
        logger.info(f"Finding similar KB chunks for {scenario_name}...")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=text_embedding,
            top_k=3
        )

        # Log the similar chunks
        logger.info(f"Found {len(similar_chunks)} similar chunks for {scenario_name}:")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Chunk {i+1}: {chunk.source}")
            logger.info(f"Similarity score: {getattr(chunk, 'similarity_score', 'N/A')}")
            logger.info(f"Content: {chunk.content[:150]}...")

        # Extract content from similar chunks for the prompt
        knowledge_context = "\n\n".join([
            f"Source: {chunk.source}\n{chunk.content}"
            for chunk in similar_chunks
        ])

        # Construct the prompt
        prompt = f"""
        You are an expert scam detector AI. Analyze the following content for potential scam indicators.

        CONTENT TO ANALYZE:
        {text_content}

        SOURCE APP: {source_app}
        SOURCE URL: {source_url}

        RELEVANT KNOWLEDGE BASE INFORMATION:
        {knowledge_context}

        Based on the content and the knowledge base information, provide a comprehensive scam analysis in JSON format with the following fields:

        1. risk_score: A number between 0.0 and 1.0, where 0.0 is definitely safe and 1.0 is definitely a scam.
        2. explanation: A detailed explanation of why you think this is or isn't a scam.
        3. red_flags: A list of specific red flags or suspicious elements in the content.
        4. recommendations: Specific recommendations for the user.
        5. confidence: Your confidence level in this analysis (Low, Medium, High).

        Format your response as a valid JSON object with these fields.
        """

        # Log the prompt
        logger.info(f"Prompt sent to GPT-4.1 for {scenario_name}:")
        logger.info(prompt)

        # Perform scam analysis with RAG
        logger.info(f"Performing scam analysis with RAG for {scenario_name}...")
        result = await perform_scam_analysis_with_rag(
            content_type="text",
            text_content=text_content,
            image_content=None,
            source_app=source_app,
            source_url=source_url,
            text_embedding=text_embedding,
            similar_chunks=similar_chunks,
            ai_service=ai_service
        )

        # Get the raw response from the perform_scam_analysis_with_rag function
        # This is a simplification; in a real implementation, you would capture this from the function
        response = await ai_service.client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": "You are an expert scam detector AI."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            response_format={"type": "json_object"}
        )
        response_text = response.choices[0].message.content

        # Log the result
        logger.info(f"Scam analysis result for {scenario_name}:")
        logger.info(f"Risk score: {result.risk_score}")
        logger.info(f"Explanation: {result.explanation}")
        logger.info(f"Red flags: {result.detected_red_flags}")
        logger.info(f"Recommendations: {result.recommendations}")
        logger.info(f"Confidence: {getattr(result, 'confidence', 'N/A')}")

        return {
            "similar_chunks": [
                {
                    "source": chunk.source,
                    "similarity_score": getattr(chunk, "similarity_score", None),
                    "content": chunk.content[:150] + "..."
                }
                for chunk in similar_chunks
            ],
            "prompt": prompt,
            "raw_response": response_text,
            "result": {
                "risk_score": result.risk_score,
                "explanation": result.explanation,
                "red_flags": result.detected_red_flags,
                "recommendations": result.recommendations,
                "confidence": getattr(result, 'confidence', 'N/A')
            }
        }

    finally:
        db.close()

async def main():
    """Run all test scenarios."""
    # Scenario 1: Cashier's Check Scam
    logger.info("Testing Scenario 1: Cashier's Check Scam")
    cashier_check_text = """
    Hey there! I'm interested in your item on OfferUp. I'd like to buy it right away. I'll send you a cashier's check for $1,500. That's $500 more than your asking price. Once you deposit it, just send me back the extra $500 via Western Union. I'm out of town for work, so I can't pick it up in person. I'll arrange shipping once you confirm. Let me know if this works for you!
    """
    cashier_check_results = await test_scenario(
        scenario_name="Cashier's Check Scam",
        text_content=cashier_check_text,
        source_app="OfferUp",
        source_url="https://offerup.com/item/detail/123456"
    )

    # Scenario 2: Remote Buyer Text Scam
    logger.info("\nTesting Scenario 2: Remote Buyer Text Scam")
    remote_buyer_text = """
    Hi, I saw your listing and I'm very interested in buying it. I'm currently out of the country for work but I want to purchase it right away. I'll pay extra for shipping and handling. I'll send payment via PayPal and arrange a shipping company to pick it up. Please take down the listing and I'll pay an extra $50 for your trouble. Let me know your PayPal email so I can send the money right away.
    """
    remote_buyer_results = await test_scenario(
        scenario_name="Remote Buyer Text Scam",
        text_content=remote_buyer_text,
        source_app="Facebook Marketplace",
        source_url="https://www.facebook.com/marketplace/item/123456"
    )

    # Scenario 3: Tech Support Scam
    logger.info("\nTesting Scenario 3: Tech Support Scam")
    tech_support_text = """
    WARNING! Your computer is infected with 3 viruses! Call Microsoft Support Immediately at ************** to prevent data loss. Your personal information is at risk. If you close this window, your computer will be disabled. Our certified Microsoft technicians can remove the viruses remotely for a one-time fee of $299.99. Act now to protect your data!
    """
    tech_support_results = await test_scenario(
        scenario_name="Tech Support Scam",
        text_content=tech_support_text,
        source_app="Web Browser",
        source_url="https://fake-security-alert.com/warning"
    )

    # Save the results to a file
    with open("full_rag_test_results.json", "w") as f:
        json.dump({
            "cashier_check_scam": cashier_check_results,
            "remote_buyer_scam": remote_buyer_results,
            "tech_support_scam": tech_support_results
        }, f, indent=2)

    logger.info("\nAll tests completed. Results saved to full_rag_test_results.json")

if __name__ == "__main__":
    asyncio.run(main())
