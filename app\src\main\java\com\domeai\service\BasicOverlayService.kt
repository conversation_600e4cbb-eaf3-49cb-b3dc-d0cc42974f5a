package com.domeai.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import androidx.core.app.NotificationCompat
import com.domeai.R
import com.domeai.presentation.main.MainActivity

/**
 * A very basic overlay service with minimal functionality to ensure reliability
 */
class BasicOverlayService : Service() {
    
    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    
    // For tracking touch events
    private var initialX = 0
    private var initialY = 0
    private var initialTouchX = 0f
    private var initialTouchY = 0f
    
    // Screen dimensions
    private var screenWidth = 0
    private var screenHeight = 0
    
    companion object {
        private const val TAG = "BasicOverlayService"
        private const val NOTIFICATION_ID = 1002
        private const val CHANNEL_ID = "basic_overlay_channel"
        
        // Intent actions
        const val ACTION_START = "com.domeai.service.ACTION_START_BASIC"
        const val ACTION_STOP = "com.domeai.service.ACTION_STOP_BASIC"
        
        // Intent to start the service
        fun getStartIntent(context: Context): Intent {
            return Intent(context, BasicOverlayService::class.java).apply {
                action = ACTION_START
            }
        }
        
        // Intent to stop the service
        fun getStopIntent(context: Context): Intent {
            return Intent(context, BasicOverlayService::class.java).apply {
                action = ACTION_STOP
            }
        }
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Service created")
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "onStartCommand: ${intent?.action}")
        
        when (intent?.action) {
            ACTION_START -> startOverlay()
            ACTION_STOP -> stopOverlay()
            else -> startOverlay()
        }
        
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        stopOverlay()
        Log.d(TAG, "Service destroyed")
    }
    
    private fun startOverlay() {
        try {
            // Start as foreground service
            startForeground(NOTIFICATION_ID, createNotification())
            
            // Check for overlay permission
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
                Log.e(TAG, "No overlay permission")
                stopSelf()
                return
            }
            
            // Remove any existing view
            removeOverlayView()
            
            // Get screen dimensions
            val displayMetrics = resources.displayMetrics
            screenWidth = displayMetrics.widthPixels
            screenHeight = displayMetrics.heightPixels
            
            // Create and add the floating view
            createAndAddOverlayView()
            
            Log.d(TAG, "Overlay started successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting overlay: ${e.message}")
            e.printStackTrace()
            stopSelf()
        }
    }
    
    private fun createAndAddOverlayView() {
        try {
            // Inflate the floating view layout
            val inflater = LayoutInflater.from(this)
            floatingView = inflater.inflate(R.layout.floating_view, null)
            
            // Create layout parameters
            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                },
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT
            ).apply {
                gravity = Gravity.TOP or Gravity.END
                x = 0  // With END gravity, x=0 means right edge
                y = 100  // Position near the top
            }
            
            // Set up touch listener for dragging
            floatingView?.setOnTouchListener { _, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        initialX = params.x
                        initialY = params.y
                        initialTouchX = event.rawX
                        initialTouchY = event.rawY
                        true
                    }
                    MotionEvent.ACTION_MOVE -> {
                        params.x = initialX + (event.rawX - initialTouchX).toInt()
                        params.y = initialY + (event.rawY - initialTouchY).toInt()
                        
                        try {
                            windowManager?.updateViewLayout(floatingView, params)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error updating view position: ${e.message}")
                        }
                        true
                    }
                    MotionEvent.ACTION_UP -> {
                        val moved = Math.abs(event.rawX - initialTouchX) > 10 ||
                                Math.abs(event.rawY - initialTouchY) > 10
                        
                        if (moved) {
                            // Check if we're at the bottom of the screen
                            if (event.rawY > screenHeight - 200) {
                                // User dragged to bottom, remove the overlay
                                stopOverlay()
                                return@setOnTouchListener true
                            }
                            
                            // Snap to nearest edge
                            snapToEdge(params)
                        } else {
                            // Handle click - open the app
                            try {
                                val intent = Intent(this, MainActivity::class.java).apply {
                                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                }
                                startActivity(intent)
                            } catch (e: Exception) {
                                Log.e(TAG, "Error starting activity: ${e.message}")
                            }
                        }
                        true
                    }
                    else -> false
                }
            }
            
            // Add the view to the window manager
            windowManager?.addView(floatingView, params)
            Log.d(TAG, "Overlay view added successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error creating overlay view: ${e.message}")
        }
    }
    
    private fun snapToEdge(params: WindowManager.LayoutParams) {
        try {
            // Get button dimensions
            val buttonWidth = floatingView?.width ?: 100
            val buttonHeight = floatingView?.height ?: 100
            
            // Calculate distances to edges
            val distanceToLeft = params.x
            val distanceToRight = screenWidth - (params.x + buttonWidth)
            val distanceToTop = params.y
            val distanceToBottom = screenHeight - (params.y + buttonHeight)
            
            // Find the closest edge
            val minHorizontalDistance = Math.min(distanceToLeft, distanceToRight)
            val minVerticalDistance = Math.min(distanceToTop, distanceToBottom)
            
            // Snap to the closest edge
            if (minHorizontalDistance < minVerticalDistance) {
                // Snap horizontally
                if (distanceToLeft < distanceToRight) {
                    // Snap to left
                    params.x = 0
                } else {
                    // Snap to right
                    params.x = screenWidth - buttonWidth
                }
            } else {
                // Snap vertically
                if (distanceToTop < distanceToBottom) {
                    // Snap to top
                    params.y = 0
                } else {
                    // Snap to bottom
                    params.y = screenHeight - buttonHeight
                }
            }
            
            // Update the view position
            windowManager?.updateViewLayout(floatingView, params)
        } catch (e: Exception) {
            Log.e(TAG, "Error snapping to edge: ${e.message}")
        }
    }
    
    private fun removeOverlayView() {
        try {
            if (floatingView != null) {
                windowManager?.removeView(floatingView)
                floatingView = null
                Log.d(TAG, "Overlay view removed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error removing overlay view: ${e.message}")
        }
    }
    
    private fun stopOverlay() {
        removeOverlayView()
        
        // Stop the foreground service
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            if (Build.VERSION.SDK_INT >= 31) { // Android 12+
                stopForeground(Service.STOP_FOREGROUND_REMOVE)
            } else {
                stopForeground(true)
            }
        } else {
            stopForeground(true)
        }
        
        stopSelf()
        Log.d(TAG, "Overlay stopped")
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Basic Overlay Service"
            val descriptionText = "Shows a simple floating button"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): android.app.Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("DomeAI Scam Detector")
            .setContentText("Overlay protection is active")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .build()
    }
}
