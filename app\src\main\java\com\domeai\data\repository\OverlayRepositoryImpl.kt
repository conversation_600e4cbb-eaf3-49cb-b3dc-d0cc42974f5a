package com.domeai.data.repository

import android.content.Context
import android.provider.Settings
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import com.domeai.data.model.OverlayServiceState
import com.domeai.data.model.RiskLevel
import com.domeai.data.model.ScanResult
import com.domeai.data.model.ScanSourceType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Implementation of OverlayRepository
 * For the MVP, this uses mock data and local preferences
 * In a real app, this would connect to an actual backend API
 */
@Singleton
class OverlayRepositoryImpl @Inject constructor(
    private val context: Context,
    private val dataStore: DataStore<Preferences>
) : OverlayRepository {

    companion object {
        private val OVERLAY_ENABLED_KEY = booleanPreferencesKey("overlay_enabled")

        // Mock data for scan results
        private val mockScans = listOf(
            ScanResult(
                id = UUID.randomUUID().toString(),
                timestamp = System.currentTimeMillis() - 86400000, // 1 day ago
                riskScore = 85,
                riskLevel = RiskLevel.HIGH_RISK,
                redFlags = listOf(
                    "Suspicious URL detected",
                    "Request for personal information",
                    "Urgent action required language"
                ),
                explanation = "This appears to be a phishing attempt trying to steal your personal information.",
                sourceType = ScanSourceType.OVERLAY_SCREENSHOT,
                sourceContent = "Your account has been compromised. Please verify your identity by clicking on this link: http://suspicious-url.com"
            ),
            ScanResult(
                id = UUID.randomUUID().toString(),
                timestamp = System.currentTimeMillis() - *********, // 2 days ago
                riskScore = 45,
                riskLevel = RiskLevel.MEDIUM_RISK,
                redFlags = listOf(
                    "Unusual request",
                    "Grammar errors"
                ),
                explanation = "This message contains some suspicious elements but may be legitimate. Proceed with caution.",
                sourceType = ScanSourceType.MANUAL_TEXT,
                sourceContent = "Hello, we need to verify you're account details. Please respond with your information."
            ),
            ScanResult(
                id = UUID.randomUUID().toString(),
                timestamp = System.currentTimeMillis() - *********, // 3 days ago
                riskScore = 10,
                riskLevel = RiskLevel.LOW_RISK,
                redFlags = listOf(
                    "Contains promotional content"
                ),
                explanation = "This appears to be a legitimate marketing message.",
                sourceType = ScanSourceType.MANUAL_IMAGE,
                sourceContent = null
            ),
            ScanResult(
                id = UUID.randomUUID().toString(),
                timestamp = System.currentTimeMillis() - *********, // 4 days ago
                riskScore = 0,
                riskLevel = RiskLevel.SAFE,
                redFlags = emptyList(),
                explanation = "No suspicious elements detected in this content.",
                sourceType = ScanSourceType.OVERLAY_SCREENSHOT,
                sourceContent = "Your order #12345 has been shipped and will arrive on Monday. Track your package here: https://legitimate-store.com/track"
            )
        )
    }

    override fun getOverlayServiceState(): Flow<OverlayServiceState> {
        return dataStore.data.map { preferences ->
            try {
                val isEnabled = preferences[OVERLAY_ENABLED_KEY] ?: false
                val hasPermission = checkOverlayPermission()
                OverlayServiceState(isEnabled = isEnabled, hasPermission = hasPermission)
            } catch (e: Exception) {
                android.util.Log.e("OverlayRepository", "Error getting overlay state: ${e.message}")
                OverlayServiceState(isEnabled = false, hasPermission = false)
            }
        }
    }

    override suspend fun setOverlayServiceEnabled(enabled: Boolean) {
        try {
            dataStore.edit { preferences ->
                preferences[OVERLAY_ENABLED_KEY] = enabled
            }
        } catch (e: Exception) {
            android.util.Log.e("OverlayRepository", "Error setting overlay enabled: ${e.message}")
        }
    }

    override suspend fun checkOverlayPermission(): Boolean {
        return try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                Settings.canDrawOverlays(context)
            } else {
                true // On older versions, permission is granted at install time
            }
        } catch (e: Exception) {
            android.util.Log.e("OverlayRepository", "Error checking overlay permission: ${e.message}")
            false
        }
    }

    override fun getRecentScans(limit: Int): Flow<List<ScanResult>> {
        // In a real app, this would fetch from a database or API
        return kotlinx.coroutines.flow.flow {
            emit(mockScans.take(limit))
        }
    }

    override suspend fun getScanById(id: String): ScanResult? {
        // In a real app, this would fetch from a database or API
        return mockScans.find { it.id == id }
    }
}
