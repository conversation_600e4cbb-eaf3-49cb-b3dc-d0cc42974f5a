package com.domeai.scamdetector.ui.subscription

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.android.billingclient.api.ProductDetails
import com.domeai.scamdetector.billing.BillingManager
import com.domeai.scamdetector.billing.BillingViewModel
import com.domeai.scamdetector.billing.SubscriptionStatus

@Composable
fun SubscriptionScreen(
    viewModel: BillingViewModel = hiltViewModel(),
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val subscriptionProducts by remember { mutableStateOf(emptyList<ProductDetails>()) }
    val userSubscriptionStatus = viewModel.userSubscriptionStatus.collectAsStateWithLifecycle().value

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Text(
            text = "Subscription Plans",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Current subscription status
        SubscriptionStatusCard(userSubscriptionStatus)

        Spacer(modifier = Modifier.height(16.dp))

        // Subscription plans
        if (subscriptionProducts.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(subscriptionProducts) { productDetails ->
                    SubscriptionPlanCard(
                        productDetails = productDetails,
                        isCurrentPlan = isCurrentPlan("premium", userSubscriptionStatus),
                        onSubscribe = {
                            viewModel.launchSubscriptionPurchaseFlow(
                                activity = context as androidx.activity.ComponentActivity,
                                productId = "premium"
                            )
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun SubscriptionStatusCard(status: SubscriptionStatus) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = when (status) {
                SubscriptionStatus.Free -> Color(0xFFE0E0E0)
                SubscriptionStatus.Premium -> Color(0xFFBBDEFB)
                SubscriptionStatus.Expert -> Color(0xFFFFD54F)
            }
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Current Plan",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = when (status) {
                    SubscriptionStatus.Free -> "Free"
                    SubscriptionStatus.Premium -> "Premium"
                    SubscriptionStatus.Expert -> "Expert"
                },
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = when (status) {
                    SubscriptionStatus.Free -> "5 scans per month"
                    SubscriptionStatus.Premium -> "100 scans per month"
                    SubscriptionStatus.Expert -> "100 scans per month + 20 expert scans"
                },
                fontSize = 14.sp,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun SubscriptionPlanCard(
    productDetails: ProductDetails,
    isCurrentPlan: Boolean,
    onSubscribe: () -> Unit
) {
    // Mock data for now
    val formattedPrice = "$9.99"
    val planName = "Premium"
    val planPeriod = "Monthly"

    val planFeatures = listOf(
        "100 scans per month",
        "Premium AI model (GPT-4.1)",
        "10 inputs per session"
    )

    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isCurrentPlan) {
                Color(0xFFBBDEFB)
            } else {
                MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "$planName ($planPeriod)",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )

                Text(
                    text = formattedPrice,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            planFeatures.forEach { feature ->
                Text(
                    text = "• $feature",
                    fontSize = 14.sp,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Button(
                onClick = onSubscribe,
                modifier = Modifier.fillMaxWidth(),
                enabled = !isCurrentPlan
            ) {
                Text(
                    text = if (isCurrentPlan) "Current Plan" else "Subscribe",
                    fontSize = 16.sp
                )
            }
        }
    }
}

private fun isCurrentPlan(productId: String, status: SubscriptionStatus): Boolean {
    return when {
        productId == "premium" && status == SubscriptionStatus.Premium -> true
        productId == "expert" && status == SubscriptionStatus.Expert -> true
        else -> false
    }
}
