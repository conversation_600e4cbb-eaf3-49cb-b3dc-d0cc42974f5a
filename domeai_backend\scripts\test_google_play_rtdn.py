#!/usr/bin/env python
"""
Script to test the Google Play RTDN endpoint.

This script simulates a Google Cloud Pub/Sub message containing a Google Play
Real-Time Developer Notification (RTDN) and sends it to the webhook endpoint.
"""
import base64
import json
import logging
import sys
import requests
from typing import Dict, Any

# Add the parent directory to the path so we can import from app
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)


def create_test_notification() -> Dict[str, Any]:
    """
    Create a test notification payload.
    
    Returns:
        A dictionary containing a test notification
    """
    # Create a test notification
    developer_notification = {
        "version": "1.0",
        "packageName": "com.domeai.scamdetector",
        "eventTimeMillis": "1621234567890",
        "testNotification": {
            "version": "1.0"
        }
    }
    
    # Encode the notification as base64
    encoded_data = base64.b64encode(json.dumps(developer_notification).encode("utf-8")).decode("utf-8")
    
    # Create the Pub/Sub message
    pubsub_message = {
        "message": {
            "data": encoded_data,
            "messageId": "test-message-id",
            "publishTime": "2023-05-20T10:00:00.000Z"
        },
        "subscription": "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
    }
    
    return pubsub_message


def create_subscription_purchased_notification() -> Dict[str, Any]:
    """
    Create a subscription purchased notification payload.
    
    Returns:
        A dictionary containing a subscription purchased notification
    """
    # Create a subscription purchased notification
    developer_notification = {
        "version": "1.0",
        "packageName": "com.domeai.scamdetector",
        "eventTimeMillis": "1621234567890",
        "subscriptionNotification": {
            "version": "1.0",
            "notificationType": 4,  # SUBSCRIPTION_PURCHASED
            "purchaseToken": "test-purchase-token",
            "subscriptionId": "domeai_premium_monthly"
        }
    }
    
    # Encode the notification as base64
    encoded_data = base64.b64encode(json.dumps(developer_notification).encode("utf-8")).decode("utf-8")
    
    # Create the Pub/Sub message
    pubsub_message = {
        "message": {
            "data": encoded_data,
            "messageId": "test-message-id",
            "publishTime": "2023-05-20T10:00:00.000Z"
        },
        "subscription": "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
    }
    
    return pubsub_message


def send_test_notification(notification: Dict[str, Any], api_url: str = "http://localhost:8000") -> None:
    """
    Send a test notification to the webhook endpoint.
    
    Args:
        notification: The notification payload to send
        api_url: The base URL of the API
    """
    # Send the notification to the webhook endpoint
    response = requests.post(
        f"{api_url}/api/v1/webhooks/googleplay/rtdn",
        json=notification
    )
    
    # Check the response
    if response.status_code == 200:
        logger.info(f"Successfully sent test notification. Response: {response.json()}")
    else:
        logger.error(f"Failed to send test notification. Status code: {response.status_code}, Response: {response.text}")


def main() -> None:
    """
    Main function to run the test.
    """
    # Create and send a test notification
    logger.info("Sending test notification...")
    test_notification = create_test_notification()
    send_test_notification(test_notification)
    
    # Create and send a subscription purchased notification
    logger.info("Sending subscription purchased notification...")
    subscription_notification = create_subscription_purchased_notification()
    send_test_notification(subscription_notification)


if __name__ == "__main__":
    main()
