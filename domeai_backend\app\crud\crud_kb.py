from typing import List, Optional

from sqlalchemy.orm import Session

from app.models.knowledge_base import KnowledgeBaseChunk


def create_kb_chunk(
    db: Session, *, content: str, embedding: List[float], source: Optional[str] = None
) -> KnowledgeBaseChunk:
    """
    Create a new knowledge base chunk.

    Args:
        db: Database session
        content: The text content of the chunk
        embedding: The embedding vector for the chunk
        source: Optional source identifier for the chunk

    Returns:
        The created knowledge base chunk
    """
    db_chunk = KnowledgeBaseChunk(
        content=content,
        embedding=embedding,
        source=source
    )
    db.add(db_chunk)
    db.commit()
    db.refresh(db_chunk)
    return db_chunk


def find_similar_kb_chunks(
    db: Session, *, query_embedding: List[float], top_k: int = 3
) -> List[KnowledgeBaseChunk]:
    """
    Find the most similar knowledge base chunks to the query embedding.

    This uses pgvector's native vector similarity search with inner product.
    For normalized vectors (like OpenAI embeddings), inner product is equivalent to cosine similarity.

    Args:
        db: Database session
        query_embedding: The query embedding vector
        top_k: Number of similar chunks to return

    Returns:
        List of similar knowledge base chunks
    """
    import logging
    logger = logging.getLogger(__name__)

    try:
        # Use pgvector's native inner product for cosine similarity
        # The <#> operator is the negative inner product operator in pgvector
        # For normalized vectors, -1 * (negative inner product) = cosine similarity
        from sqlalchemy import text

        # For pgvector, we need to keep the embedding as a list with square brackets
        embedding_str = str(query_embedding)

        # Use raw SQL with the inner product operator
        # Note: We use -1 * (embedding <#> :embedding) to convert negative inner product to cosine similarity
        # For normalized vectors, this gives a value between -1 and 1, where 1 is identical
        query = text("""
            SELECT id, content, source, created_at,
                   -1 * (embedding <#> :embedding) AS similarity_score
            FROM knowledge_base_chunks
            ORDER BY embedding <#> :embedding ASC
            LIMIT :limit
        """)

        # Execute the query with parameters
        result = db.execute(
            query,
            {"embedding": embedding_str, "limit": top_k}
        )

        # Process the results
        chunks = []
        for row in result:
            # Get the chunk by ID
            chunk = db.query(KnowledgeBaseChunk).get(row.id)
            if chunk:
                # Add the similarity score as an attribute
                similarity = float(row.similarity_score)
                setattr(chunk, 'similarity_score', similarity)
                chunks.append(chunk)
                logger.info(f"Found similar chunk: ID={chunk.id}, Source={chunk.source}, Similarity={similarity:.4f}")

        return chunks

    except Exception as e:
        # Log the error and return an empty list
        logger.error(f"Error in find_similar_kb_chunks: {str(e)}")

        # Try with cosine distance as fallback
        try:
            logger.info("Attempting fallback with cosine distance")
            from sqlalchemy import text

            # Use cosine distance with proper scaling
            # Cosine distance in pgvector ranges from 0 (identical) to 2 (opposite)
            # To convert to similarity: 1 - (distance/2)
            query = text("""
                SELECT id, content, source, created_at,
                       1 - (embedding <-> :embedding)/2 AS similarity_score
                FROM knowledge_base_chunks
                ORDER BY embedding <-> :embedding
                LIMIT :limit
            """)

            # Execute the query with parameters
            result = db.execute(
                query,
                {"embedding": embedding_str, "limit": top_k}
            )

            # Process the results
            chunks = []
            for row in result:
                # Get the chunk by ID
                chunk = db.query(KnowledgeBaseChunk).get(row.id)
                if chunk:
                    # Add the similarity score as an attribute
                    similarity = float(row.similarity_score)
                    setattr(chunk, 'similarity_score', similarity)
                    chunks.append(chunk)
                    logger.info(f"Found similar chunk (fallback): ID={chunk.id}, Source={chunk.source}, Similarity={similarity:.4f}")

            return chunks

        except Exception as e2:
            logger.error(f"Fallback with cosine distance failed: {str(e2)}")

            # Last resort fallback
            try:
                logger.info("Attempting last resort fallback without vector operations")
                # Just get some chunks without similarity calculation
                chunks = db.query(KnowledgeBaseChunk).limit(top_k).all()
                for chunk in chunks:
                    setattr(chunk, 'similarity_score', 0.0)
                return chunks
            except Exception as e3:
                logger.error(f"Last resort fallback also failed: {str(e3)}")
                return []


def get_kb_chunk(db: Session, chunk_id: int) -> Optional[KnowledgeBaseChunk]:
    """
    Get a knowledge base chunk by ID.

    Args:
        db: Database session
        chunk_id: ID of the chunk to get

    Returns:
        The knowledge base chunk, or None if not found
    """
    return db.query(KnowledgeBaseChunk).filter(KnowledgeBaseChunk.id == chunk_id).first()


def get_all_kb_chunks(db: Session, skip: int = 0, limit: int = 100) -> List[KnowledgeBaseChunk]:
    """
    Get all knowledge base chunks.

    Args:
        db: Database session
        skip: Number of chunks to skip
        limit: Maximum number of chunks to return

    Returns:
        List of knowledge base chunks
    """
    return db.query(KnowledgeBaseChunk).offset(skip).limit(limit).all()
