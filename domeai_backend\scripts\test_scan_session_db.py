#!/usr/bin/env python
"""
<PERSON>ript to test the scan session database models.
"""

import logging
import sys
import uuid
import os
import time
from datetime import datetime, timezone

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan_session import ScanSession
from app.models.scan import Scan

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def test_scan_session_db():
    """Test the scan session database models."""
    logger.info("Testing Scan Session Database Models")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create a premium tier test user
        unique_email = f"test_premium_user_{int(time.time())}@example.com"
        test_user = User(
            email=unique_email,
            hashed_password="hashed_password",
            subscription_tier="premium",
            monthly_scan_allowance=100,
            scans_this_month=0,
            scan_counter_reset_at=datetime.now(timezone.utc)
        )
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        logger.info(f"Created premium test user with ID {test_user.id}")
        
        # Create a scan session
        session = ScanSession(
            owner_id=test_user.id,
            title="Test Session"
        )
        db.add(session)
        db.commit()
        db.refresh(session)
        logger.info(f"Created scan session with ID {session.id}")
        
        # Create first scan in the session
        scan1 = Scan(
            owner_id=test_user.id,
            status="completed",
            input_content_type="text",
            input_text="This is test scan 1",
            scan_session_id=session.id,
            raw_input_payload={"text": "This is test scan 1"}
        )
        db.add(scan1)
        db.commit()
        db.refresh(scan1)
        logger.info(f"Created scan 1 with ID {scan1.id}")
        
        # Create second scan in the session
        scan2 = Scan(
            owner_id=test_user.id,
            status="completed",
            input_content_type="text",
            input_text="This is test scan 2",
            scan_session_id=session.id,
            raw_input_payload={"text": "This is test scan 2"}
        )
        db.add(scan2)
        db.commit()
        db.refresh(scan2)
        logger.info(f"Created scan 2 with ID {scan2.id}")
        
        # Get all scans in the session
        session_scans = db.query(Scan).filter(Scan.scan_session_id == session.id).all()
        logger.info(f"Number of scans in session: {len(session_scans)}")
        for scan in session_scans:
            logger.info(f"Scan ID: {scan.id}, Text: {scan.input_text}")
        
        # Get all sessions for the user
        user_sessions = db.query(ScanSession).filter(ScanSession.owner_id == test_user.id).all()
        logger.info(f"Number of sessions for user: {len(user_sessions)}")
        for session in user_sessions:
            logger.info(f"Session ID: {session.id}, Title: {session.title}")
        
        # Get previous scans in the session
        previous_scans = db.query(Scan).filter(
            Scan.scan_session_id == session.id,
            Scan.id < scan2.id
        ).all()
        logger.info(f"Number of previous scans in session: {len(previous_scans)}")
        for scan in previous_scans:
            logger.info(f"Previous scan ID: {scan.id}, Text: {scan.input_text}")
        
        # Clean up
        for scan in session_scans:
            db.delete(scan)
        for session in user_sessions:
            db.delete(session)
        db.delete(test_user)
        db.commit()
        logger.info("Test data deleted")
        
    finally:
        db.close()

def main():
    """Run the test."""
    test_scan_session_db()

if __name__ == "__main__":
    main()
