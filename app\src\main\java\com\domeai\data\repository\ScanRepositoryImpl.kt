package com.domeai.data.repository

import android.content.Context
import android.net.Uri
import android.util.Log
import com.domeai.data.model.DetectionDetail
import com.domeai.data.model.RiskLevel
import com.domeai.data.model.ScanResult
import com.domeai.data.model.ScanSourceType
import com.domeai.data.model.ScanType
import com.google.gson.Gson
import com.domeai.data.model.network.ScanDetailsRequestData
import com.domeai.data.model.network.ScanResponse
import com.domeai.data.model.network.ScanSubmissionPayload
import com.domeai.data.model.network.SubscriptionTiers
import com.domeai.data.network.ScanApiService
import com.domeai.data.preferences.AuthPreferences
import com.domeai.data.repository.SubscriptionRepository
import com.domeai.data.util.ApiResponse
import com.domeai.data.util.safeApiCall
import java.io.IOException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import java.io.FileOutputStream
import java.util.Date
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

private const val TAG = "ScanRepositoryImpl"

/**
 * Implementation of ScanRepository
 */
@Singleton
class ScanRepositoryImpl @Inject constructor(
    private val scanApiService: ScanApiService,
    private val authPreferences: AuthPreferences,
    private val subscriptionRepository: SubscriptionRepository
) : ScanRepository {

    // In-memory cache of scan results
    private val scanResults = MutableStateFlow<List<ScanResult>>(generateMockScans())

    override fun getAllScans(): Flow<List<ScanResult>> = scanResults

    override fun getRecentScans(limit: Int): Flow<List<ScanResult>> =
        scanResults.map { it.sortedByDescending { scan -> scan.timestamp }.take(limit) }

    override fun getScanById(id: String): Flow<ScanResult?> =
        scanResults.map { it.find { scan -> scan.id == id } }

    override suspend fun addScan(scan: ScanResult) {
        val currentScans = scanResults.value.toMutableList()
        currentScans.add(scan)
        scanResults.value = currentScans
    }

    override suspend fun updateScan(scan: ScanResult) {
        val currentScans = scanResults.value.toMutableList()
        val index = currentScans.indexOfFirst { it.id == scan.id }
        if (index != -1) {
            currentScans[index] = scan
            scanResults.value = currentScans
        }
    }

    override suspend fun deleteScan(id: String) {
        val currentScans = scanResults.value.toMutableList()
        currentScans.removeIf { it.id == id }
        scanResults.value = currentScans
    }

    override suspend fun toggleFavorite(id: String) {
        Log.d(TAG, "Toggle favorite for scan $id")
    }

    override fun searchScans(query: String): Flow<List<ScanResult>> =
        scanResults.map { scans ->
            if (query.isBlank()) {
                scans
            } else {
                scans.filter {
                    it.sourceContent?.contains(query, ignoreCase = true) == true ||
                    it.explanation.contains(query, ignoreCase = true)
                }
            }
        }

    override fun filterByRiskLevel(riskLevel: RiskLevel?): Flow<List<ScanResult>> =
        scanResults.map { scans ->
            if (riskLevel == null) {
                scans
            } else {
                scans.filter { it.riskLevel == riskLevel }
            }
        }

    override fun filterByType(type: ScanType?): Flow<List<ScanResult>> =
        scanResults.map { scans ->
            if (type == null) {
                scans
            } else {
                scans.filter { it.sourceType == ScanSourceType.MANUAL_TEXT }
            }
        }

    override fun filterByDateRange(startDate: Date?, endDate: Date?): Flow<List<ScanResult>> =
        scanResults.map { scans ->
            var filtered = scans

            if (startDate != null) {
                val startTime = startDate.time
                filtered = filtered.filter { it.timestamp >= startTime }
            }

            if (endDate != null) {
                val endTime = endDate.time
                filtered = filtered.filter { it.timestamp <= endTime }
            }

            filtered
        }

    override fun getFavoriteScans(): Flow<List<ScanResult>> =
        scanResults.map { it.take(2) }

    override suspend fun submitTextScan(
        text: String,
        userContext: String?,
        sessionId: String?
    ): Result<ScanResponse> {
        return try {
            // Get auth token
            val authHeader = authPreferences.authHeader.first()
                ?: return Result.failure(Exception("Not authenticated"))

            // Get current subscription
            val subscription = subscriptionRepository.getCurrentSubscription()

            // Determine subscription tier
            val subscriptionTier = when {
                subscription.planName.contains("Expert", ignoreCase = true) -> SubscriptionTiers.EXPERT
                subscription.planName.contains("Premium", ignoreCase = true) -> SubscriptionTiers.PREMIUM
                else -> SubscriptionTiers.BASIC
            }

            // Check scan limits based on subscription tier
            if (subscriptionTier == SubscriptionTiers.BASIC &&
                subscription.scansThisMonth >= SubscriptionTiers.BASIC_SCAN_LIMIT) {
                return Result.failure(Exception("You have reached your monthly scan limit. Upgrade to Premium or Expert for more scans."))
            }

            if (subscriptionTier == SubscriptionTiers.PREMIUM &&
                subscription.scansThisMonth >= 100) { // Premium has 100 scans limit
                return Result.failure(Exception("You have reached your monthly scan limit for Premium tier."))
            }

            if (subscriptionTier == SubscriptionTiers.EXPERT &&
                subscription.scansThisMonth >= 100) { // Expert has 100 regular scans limit
                return Result.failure(Exception("You have reached your monthly regular scan limit for Expert tier."))
            }

            // Determine if expert scan should be used
            val useExpertScan = subscriptionTier == SubscriptionTiers.EXPERT &&
                                subscription.expertScansThisMonth < SubscriptionTiers.EXPERT_ADVANCED_SCAN_LIMIT

            Log.d(TAG, "Subscription tier: $subscriptionTier, Using expert scan: $useExpertScan")

            // Create request payload with the correct structure
            val textContent = text?.trim() ?: ""

            // Validate that text is not empty
            if (textContent.isEmpty()) {
                return Result.failure(Exception("Text content cannot be empty for text scans"))
            }

            // Create the inner scan details object
            val scanDetails = ScanDetailsRequestData(
                inputContentType = "text",
                inputText = textContent, // This is required when input_content_type is "text"
                userProvidedContext = userContext,
                scanSessionId = sessionId
            )

            // Create the outer payload with nested scan_in
            val payload = ScanSubmissionPayload(scanIn = scanDetails)

            // Log the payload for debugging
            Log.d(TAG, "Text scan details: inputText=${scanDetails.inputText}, inputContentType=${scanDetails.inputContentType}")

            // Convert payload to JSON for debugging
            val gson = Gson()
            val jsonPayload = gson.toJson(payload)
            Log.d(TAG, "Text scan JSON payload: $jsonPayload")

            Log.d(TAG, "Submitting text scan with payload: $payload, auth: $authHeader, useExpertScan: $useExpertScan")

            // Call API
            when (val response = safeApiCall {
                scanApiService.submitTextOrUrlScan(
                    token = authHeader,
                    payload = payload,
                    useExpertScan = useExpertScan
                )
            }) {
                is ApiResponse.Success -> {
                    Log.d(TAG, "Text scan submitted successfully: ${response.data}")

                    // After successful scan, refresh subscription to update scan counts
                    try {
                        subscriptionRepository.getCurrentSubscription(forceRefresh = true)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error refreshing subscription after scan", e)
                    }

                    Result.success(response.data)
                }
                is ApiResponse.Error -> {
                    Log.e(TAG, "Error submitting text scan: ${response.errorMessage}")
                    Log.e(TAG, "Error details: ${response.errorBody}")
                    Result.failure(Exception(response.errorMessage))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception submitting text scan", e)
            Result.failure(e)
        }
    }

    override suspend fun submitUrlScan(
        url: String,
        userContext: String?,
        sessionId: String?
    ): Result<ScanResponse> {
        return try {
            // Get auth token
            val authHeader = authPreferences.authHeader.first()
                ?: return Result.failure(Exception("Not authenticated"))

            // Get current subscription
            val subscription = subscriptionRepository.getCurrentSubscription()

            // Determine subscription tier
            val subscriptionTier = when {
                subscription.planName.contains("Expert", ignoreCase = true) -> SubscriptionTiers.EXPERT
                subscription.planName.contains("Premium", ignoreCase = true) -> SubscriptionTiers.PREMIUM
                else -> SubscriptionTiers.BASIC
            }

            // Check scan limits based on subscription tier
            if (subscriptionTier == SubscriptionTiers.BASIC &&
                subscription.scansThisMonth >= SubscriptionTiers.BASIC_SCAN_LIMIT) {
                return Result.failure(Exception("You have reached your monthly scan limit. Upgrade to Premium or Expert for more scans."))
            }

            if (subscriptionTier == SubscriptionTiers.PREMIUM &&
                subscription.scansThisMonth >= 100) { // Premium has 100 scans limit
                return Result.failure(Exception("You have reached your monthly scan limit for Premium tier."))
            }

            if (subscriptionTier == SubscriptionTiers.EXPERT &&
                subscription.scansThisMonth >= 100) { // Expert has 100 regular scans limit
                return Result.failure(Exception("You have reached your monthly regular scan limit for Expert tier."))
            }

            // Determine if expert scan should be used
            val useExpertScan = subscriptionTier == SubscriptionTiers.EXPERT &&
                                subscription.expertScansThisMonth < SubscriptionTiers.EXPERT_ADVANCED_SCAN_LIMIT

            Log.d(TAG, "Subscription tier: $subscriptionTier, Using expert scan: $useExpertScan")

            // Create request payload with the correct structure
            val urlContent = url?.trim() ?: ""

            // Validate that URL is not empty
            if (urlContent.isEmpty()) {
                return Result.failure(Exception("URL content cannot be empty for URL scans"))
            }

            // Create the inner scan details object
            val scanDetails = ScanDetailsRequestData(
                inputContentType = "url",
                inputUrl = urlContent, // This is required when input_content_type is "url"
                userProvidedContext = userContext,
                scanSessionId = sessionId
            )

            // Create the outer payload with nested scan_in
            val payload = ScanSubmissionPayload(scanIn = scanDetails)

            // Log the payload for debugging
            Log.d(TAG, "URL scan details: inputUrl=${scanDetails.inputUrl}, inputContentType=${scanDetails.inputContentType}")

            // Convert payload to JSON for debugging
            val gson = Gson()
            val jsonPayload = gson.toJson(payload)
            Log.d(TAG, "URL scan JSON payload: $jsonPayload")

            Log.d(TAG, "Submitting URL scan with payload: $payload, auth: $authHeader, useExpertScan: $useExpertScan")

            // Call API
            when (val response = safeApiCall {
                scanApiService.submitTextOrUrlScan(
                    token = authHeader,
                    payload = payload,
                    useExpertScan = useExpertScan
                )
            }) {
                is ApiResponse.Success -> {
                    Log.d(TAG, "URL scan submitted successfully: ${response.data}")

                    // After successful scan, refresh subscription to update scan counts
                    try {
                        subscriptionRepository.getCurrentSubscription(forceRefresh = true)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error refreshing subscription after scan", e)
                    }

                    Result.success(response.data)
                }
                is ApiResponse.Error -> {
                    Log.e(TAG, "Error submitting URL scan: ${response.errorMessage}")
                    Log.e(TAG, "Error details: ${response.errorBody}")
                    Result.failure(Exception(response.errorMessage))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception submitting URL scan", e)
            Result.failure(e)
        }
    }

    override suspend fun submitImageScan(
        imageUri: Uri,
        userContext: String?,
        sessionId: String?,
        context: Context,
        useExpertScan: Boolean
    ): Result<ScanResponse> {
        Log.d("DomeUIImageURI", "ScanRepositoryImpl.submitImageScan called with URI: $imageUri")
        return try {
            // Get auth token
            val authHeader = authPreferences.authHeader.first()
                ?: return Result.failure(Exception("Not authenticated"))

            // Get current subscription
            val subscription = subscriptionRepository.getCurrentSubscription()

            // Determine subscription tier
            val subscriptionTier = when {
                subscription.planName.contains("Expert", ignoreCase = true) -> SubscriptionTiers.EXPERT
                subscription.planName.contains("Premium", ignoreCase = true) -> SubscriptionTiers.PREMIUM
                else -> SubscriptionTiers.BASIC
            }

            // Check scan limits based on subscription tier
            if (subscriptionTier == SubscriptionTiers.BASIC &&
                subscription.scansThisMonth >= SubscriptionTiers.BASIC_SCAN_LIMIT) {
                return Result.failure(Exception("You have reached your monthly scan limit. Upgrade to Premium or Expert for more scans."))
            }

            if (subscriptionTier == SubscriptionTiers.PREMIUM &&
                subscription.scansThisMonth >= 100) { // Premium has 100 scans limit
                return Result.failure(Exception("You have reached your monthly scan limit for Premium tier."))
            }

            if (subscriptionTier == SubscriptionTiers.EXPERT &&
                subscription.scansThisMonth >= 100) { // Expert has 100 regular scans limit
                return Result.failure(Exception("You have reached your monthly regular scan limit for Expert tier."))
            }

            // Check if expert scan is requested and allowed
            val canUseExpertScan = subscriptionTier == SubscriptionTiers.EXPERT &&
                                subscription.expertScansThisMonth < SubscriptionTiers.EXPERT_ADVANCED_SCAN_LIMIT

            // Only use expert scan if requested AND allowed
            val finalUseExpertScan = useExpertScan && canUseExpertScan

            Log.d(TAG, "Subscription tier: $subscriptionTier, Requested expert scan: $useExpertScan, Can use expert scan: $canUseExpertScan, Final: $finalUseExpertScan")

            // Log the URI details
            Log.d(TAG, "Processing image URI: $imageUri, scheme: ${imageUri.scheme}, path: ${imageUri.path}")

            // Validate the URI
            if (imageUri.toString().isBlank()) {
                return Result.failure(Exception("Image URI is blank or null"))
            }

            // Check if the URI is accessible
            try {
                context.contentResolver.openInputStream(imageUri)?.use {
                    Log.d(TAG, "Successfully opened input stream for URI: $imageUri")
                } ?: return Result.failure(Exception("Failed to open input stream for URI: $imageUri"))
            } catch (e: Exception) {
                Log.e(TAG, "Error accessing URI", e)
                return Result.failure(Exception("Error accessing URI: ${e.message}"))
            }

            // Convert Uri to File
            val file = try {
                uriToFile(imageUri, context)
                    ?: return Result.failure(Exception("Failed to convert URI to File"))
            } catch (e: Exception) {
                Log.e(TAG, "Error converting URI to File", e)
                return Result.failure(Exception("Error converting URI to File: ${e.message}"))
            }

            if (!file.exists()) {
                return Result.failure(Exception("File does not exist after conversion: ${file.absolutePath}"))
            }

            if (file.length() == 0L) {
                return Result.failure(Exception("File is empty: ${file.absolutePath}"))
            }

            Log.d(TAG, "Image file created: ${file.absolutePath}, exists: ${file.exists()}, size: ${file.length()} bytes")

            // Create MultipartBody.Part for the image
            val imagePart = try {
                // Determine the correct MIME type based on file extension
                val mimeType = when (file.extension.lowercase()) {
                    "jpg", "jpeg" -> "image/jpeg"
                    "png" -> "image/png"
                    "gif" -> "image/gif"
                    "webp" -> "image/webp"
                    else -> {
                        // Try to get MIME type from content resolver
                        context.contentResolver.getType(imageUri) ?: "image/jpeg"
                    }
                }

                Log.d(TAG, "Detected MIME type: $mimeType for file: ${file.name}")

                val requestFile = file.asRequestBody(mimeType.toMediaTypeOrNull())
                MultipartBody.Part.createFormData("file", file.name, requestFile)
            } catch (e: Exception) {
                Log.e(TAG, "Error creating MultipartBody.Part", e)
                return Result.failure(Exception("Error creating MultipartBody.Part: ${e.message}"))
            }

            // Log the image part details
            Log.d(TAG, "Created image part with name: 'file', filename: ${file.name}")

            // Create RequestBody parts for userContext and sessionId if they are not null
            val userContextPart = userContext?.let {
                it.toRequestBody("text/plain".toMediaTypeOrNull())
            }

            val sessionIdPart = sessionId?.let {
                it.toRequestBody("text/plain".toMediaTypeOrNull())
            }

            // Log the image scan details
            Log.d(TAG, "Submitting image scan with file: ${file.name}, size: ${file.length()} bytes")
            Log.d(TAG, "Image scan auth: $authHeader, useExpertScan: $finalUseExpertScan")
            Log.d(TAG, "Image scan userContext: $userContext, sessionId: $sessionId")

            // Create useExpertScan part
            val useExpertScanPart = finalUseExpertScan.toString().toRequestBody("text/plain".toMediaTypeOrNull())

            // Call API with the correct parameters
            when (val response = safeApiCall {
                scanApiService.submitImageScan(
                    authHeader,
                    imagePart,
                    userContextPart,
                    sessionIdPart,
                    useExpertScanPart
                )
            }) {
                is ApiResponse.Success -> {
                    Log.d(TAG, "Image scan submitted successfully: ${response.data}")

                    // After successful scan, refresh subscription to update scan counts
                    try {
                        subscriptionRepository.getCurrentSubscription(forceRefresh = true)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error refreshing subscription after scan", e)
                    }

                    Result.success(response.data)
                }
                is ApiResponse.Error -> {
                    Log.e(TAG, "Error submitting image scan: ${response.errorMessage}")
                    Log.e(TAG, "Error details: ${response.errorBody}")
                    Result.failure(Exception(response.errorMessage))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception submitting image scan", e)
            Log.e(TAG, "Exception details: ${e.message}")
            e.printStackTrace()
            Result.failure(Exception("Error submitting image scan: ${e.message}"))
        }
    }

    override suspend fun getScanDetails(scanId: String): Result<ScanResponse> {
        return try {
            // Get auth token
            val authHeader = authPreferences.authHeader.first()
                ?: return Result.failure(Exception("Not authenticated"))

            Log.d(TAG, "Getting scan details for scan ID: $scanId")

            // Call API
            when (val response = safeApiCall {
                scanApiService.getScanDetails(authHeader, scanId.toInt())
            }) {
                is ApiResponse.Success -> {
                    Log.d(TAG, "Scan details retrieved successfully: ${response.data}")
                    Result.success(response.data)
                }
                is ApiResponse.Error -> {
                    Log.e(TAG, "Error getting scan details: ${response.errorMessage}")
                    Log.e(TAG, "Error details: ${response.errorBody}")
                    Result.failure(Exception(response.errorMessage))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting scan details", e)
            Result.failure(e)
        }
    }



    /**
     * Convert a Uri to a File
     */
    private fun uriToFile(uri: Uri, context: Context): File? {
        return try {
            Log.d(TAG, "Converting URI to file: $uri, scheme: ${uri.scheme}")

            // Handle content:// URIs
            if (uri.scheme == "content") {
                try {
                    val inputStream = context.contentResolver.openInputStream(uri)
                        ?: throw IOException("Failed to open input stream for URI: $uri")

                    // Get the file extension from the URI
                    val mimeType = context.contentResolver.getType(uri)
                    Log.d(TAG, "MIME type for URI: $mimeType")

                    val extension = when {
                        mimeType?.contains("jpeg") == true || mimeType?.contains("jpg") == true -> ".jpg"
                        mimeType?.contains("png") == true -> ".png"
                        mimeType?.contains("gif") == true -> ".gif"
                        mimeType?.contains("webp") == true -> ".webp"
                        mimeType?.contains("image") == true -> ".jpg" // Generic image type
                        else -> ".jpg" // Default to jpg
                    }

                    val tempFile = File(context.cacheDir, "scan_image_${System.currentTimeMillis()}$extension")
                    Log.d(TAG, "Creating temp file: ${tempFile.absolutePath}")

                    FileOutputStream(tempFile).use { outputStream ->
                        inputStream.use { input ->
                            val buffer = ByteArray(8192)
                            var bytesRead: Int
                            var totalBytesRead = 0

                            while (input.read(buffer).also { bytesRead = it } != -1) {
                                outputStream.write(buffer, 0, bytesRead)
                                totalBytesRead += bytesRead
                            }

                            Log.d(TAG, "Total bytes read: $totalBytesRead")
                        }
                    }

                    if (tempFile.exists() && tempFile.length() > 0) {
                        Log.d(TAG, "File created successfully, size: ${tempFile.length()} bytes")
                        return tempFile
                    } else {
                        Log.e(TAG, "File creation failed or file is empty: ${tempFile.absolutePath}")
                        return null
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing content URI: $uri", e)
                    throw e
                }
            }
            // Handle file:// URIs
            else if (uri.scheme == "file") {
                val path = uri.path
                if (path != null) {
                    val file = File(path)
                    if (file.exists()) {
                        Log.d(TAG, "Using existing file: ${file.absolutePath}, size: ${file.length()} bytes")
                        return file
                    } else {
                        Log.e(TAG, "File does not exist: $path")
                    }
                } else {
                    Log.e(TAG, "Path is null for URI: $uri")
                }
            } else {
                Log.e(TAG, "Unsupported URI scheme: ${uri.scheme}")
            }

            // If we get here, we couldn't handle the URI
            Log.e(TAG, "Failed to convert URI to file: $uri")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error converting Uri to File", e)
            e.printStackTrace()
            null
        }
    }

    /**
     * Generate mock scan results for testing
     */
    private fun generateMockScans(): List<ScanResult> {
        val now = System.currentTimeMillis()
        val oneDayMs = 24 * 60 * 60 * 1000L

        return listOf(
            ScanResult(
                id = UUID.randomUUID().toString(),
                timestamp = now - oneDayMs,
                riskScore = 85,
                riskLevel = RiskLevel.HIGH_RISK,
                redFlags = listOf(
                    "Suspicious domain",
                    "Recently registered domain",
                    "Contains login form"
                ),
                explanation = "This website appears to be a phishing attempt designed to steal your personal information.",
                sourceType = ScanSourceType.MANUAL_TEXT,
                sourceContent = "https://malicious-website.com",
                isGeneralQuestion = false
            ),
            ScanResult(
                id = UUID.randomUUID().toString(),
                timestamp = now - 2 * oneDayMs,
                riskScore = 65,
                riskLevel = RiskLevel.MEDIUM_RISK,
                redFlags = listOf(
                    "Shortened URL",
                    "Free prize claim"
                ),
                explanation = "This message contains characteristics of scam giveaways.",
                sourceType = ScanSourceType.MANUAL_TEXT,
                sourceContent = "You've won a free iPhone! Click here to claim: bit.ly/claim-prize",
                isGeneralQuestion = false
            ),
            ScanResult(
                id = UUID.randomUUID().toString(),
                timestamp = now - 3 * oneDayMs,
                riskScore = 90,
                riskLevel = RiskLevel.HIGH_RISK,
                redFlags = listOf(
                    "Domain spoofing",
                    "Impersonating a trusted company"
                ),
                explanation = "This email address is attempting to impersonate Amazon support.",
                sourceType = ScanSourceType.MANUAL_TEXT,
                sourceContent = "<EMAIL>",
                isGeneralQuestion = false
            ),
            ScanResult(
                id = UUID.randomUUID().toString(),
                timestamp = now - 4 * oneDayMs,
                riskScore = 5,
                riskLevel = RiskLevel.SAFE,
                redFlags = emptyList(),
                explanation = "This appears to be a legitimate banking website with proper security measures.",
                sourceType = ScanSourceType.MANUAL_TEXT,
                sourceContent = "https://legitimate-bank.com",
                isGeneralQuestion = false
            ),
            ScanResult(
                id = UUID.randomUUID().toString(),
                timestamp = now - 5 * oneDayMs,
                riskScore = 30,
                riskLevel = RiskLevel.LOW_RISK,
                redFlags = listOf(
                    "Reported for spam calls"
                ),
                explanation = "This phone number has been reported for spam calls a few times.",
                sourceType = ScanSourceType.MANUAL_TEXT,
                sourceContent = "******-123-4567",
                isGeneralQuestion = false
            ),
            ScanResult(
                id = UUID.randomUUID().toString(),
                timestamp = now,
                riskScore = 60,
                riskLevel = RiskLevel.MEDIUM_RISK,
                redFlags = listOf(
                    "Excessive permissions",
                    "Aggressive ad networks"
                ),
                explanation = "This flashlight app requests excessive permissions that are not necessary for its functionality.",
                sourceType = ScanSourceType.MANUAL_TEXT,
                sourceContent = "FlashLight Pro Ultimate",
                isGeneralQuestion = false
            )
        )
    }
}
