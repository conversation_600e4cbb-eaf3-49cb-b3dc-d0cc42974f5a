#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test a single scan with the real AI service.
"""

import asyncio
import logging
import sys
import uuid
import os
import time
from datetime import datetime, timezone

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan_session import ScanSession
from app.models.scan import Scan
from app.crud import crud_scan_session
from app.services.ai_service_factory import get_ai_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Test image path (create a simple text file as a placeholder)
TEST_IMAGE_PATH = "temp_test_image.jpg"

async def test_single_scan_with_real_ai():
    """Test a single scan with the real AI service."""
    logger.info("Testing Single Scan with Real AI")
    
    # Create a test image
    with open(TEST_IMAGE_PATH, "w") as f:
        f.write("This is a test image")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create a test user
        unique_email = f"test_user_{int(time.time())}@example.com"
        test_user = User(
            email=unique_email,
            hashed_password="hashed_password",
            subscription_tier="premium",
            monthly_scan_allowance=100,
            scans_this_month=0,
            scan_counter_reset_at=datetime.now(timezone.utc)
        )
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        logger.info(f"Created test user with ID {test_user.id}")
        
        # Create a scan session
        session = crud_scan_session.create_scan_session(
            db=db, owner_id=test_user.id, title="Test Session"
        )
        logger.info(f"Created scan session with ID {session.id}")
        logger.info(f"Initial last_activity_at: {session.last_activity_at}")
        
        # Create a scan
        scan = Scan(
            owner_id=test_user.id,
            status="pending",
            input_content_type="image_path",
            user_provided_context="This is a test scan",
            raw_input_payload={"file_path": TEST_IMAGE_PATH},
            scan_session_id=session.id
        )
        db.add(scan)
        db.commit()
        db.refresh(scan)
        logger.info(f"Created scan with ID {scan.id}")
        
        # Update session activity
        updated_session = crud_scan_session.update_scan_session_activity(
            db=db, db_session=session
        )
        logger.info(f"Updated last_activity_at: {updated_session.last_activity_at}")
        
        # Process the scan with the real AI service
        try:
            # Get the AI service
            ai_service = get_ai_service(user_tier="premium")
            
            # Update scan status to "processing"
            scan.status = "processing"
            db.add(scan)
            db.commit()
            db.refresh(scan)
            
            # Get multimodal analysis
            logger.info(f"Getting multimodal analysis for image at {TEST_IMAGE_PATH}")
            multimodal_result = await ai_service.get_multimodal_analysis(
                image_path=TEST_IMAGE_PATH
            )
            
            # Extract text and get embedding
            extracted_text = multimodal_result.get("extracted_text", "")
            logger.info(f"Extracted text: {extracted_text}")
            
            # Get embedding
            logger.info(f"Getting text embedding")
            embedding = await ai_service.get_text_embedding(text=extracted_text)
            logger.info(f"Embedding dimensions: {len(embedding)}")
            
            # Perform RAG analysis
            logger.info(f"Performing RAG analysis")
            rag_result = await ai_service.perform_scam_analysis_with_rag(
                query_text=extracted_text,
                query_embedding=embedding,
                original_image_description=multimodal_result.get("image_description", ""),
                original_platform_identified=multimodal_result.get("platform_identified", ""),
                db=db
            )
            
            # Combine results
            analysis_result = {
                **multimodal_result,
                **rag_result
            }
            
            # Update scan with result
            scan.status = "completed"
            scan.analysis_result = analysis_result
            db.add(scan)
            db.commit()
            db.refresh(scan)
            
            logger.info(f"Completed processing of scan {scan.id}")
            logger.info(f"Analysis result: {analysis_result}")
            
        except Exception as e:
            logger.error(f"Error processing scan: {str(e)}")
            scan.status = "failed"
            scan.error_message = str(e)
            db.add(scan)
            db.commit()
        
        # Clean up
        db.delete(scan)
        db.delete(session)
        db.delete(test_user)
        db.commit()
        logger.info("Test data deleted")
        
    finally:
        db.close()
        
        # Delete the test image
        if os.path.exists(TEST_IMAGE_PATH):
            os.remove(TEST_IMAGE_PATH)
            logger.info(f"Deleted test image: {TEST_IMAGE_PATH}")

def main():
    """Run the test."""
    asyncio.run(test_single_scan_with_real_ai())

if __name__ == "__main__":
    main()
