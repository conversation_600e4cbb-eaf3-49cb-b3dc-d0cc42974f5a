#!/usr/bin/env python
"""
Script to test the real AI service with knowledge base retrieval.
"""

import asyncio
import logging
import sys
import os
import time
from typing import List

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session

from app.services.ai_services import OpenAIModelService
from app.core.database import SessionLocal
from app.crud import crud_kb

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

async def test_real_ai_with_kb():
    """Test the real AI service with knowledge base retrieval."""
    logger.info("Testing real AI service with knowledge base retrieval")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create an OpenAI service
        ai_service = OpenAIModelService(tier="premium")
        
        # Generate a test query
        test_query = "I received a cashier's check for more than the amount I was selling my item for. The buyer asked me to wire back the difference. Is this legitimate?"
        logger.info(f"Test query: {test_query}")
        
        # Get embedding for the test query
        logger.info("Getting embedding for test query")
        embedding = await ai_service.get_text_embedding(text=test_query)
        logger.info(f"Embedding dimensions: {len(embedding)}")
        
        # Find similar knowledge base chunks
        logger.info("Finding similar knowledge base chunks")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=embedding,
            top_k=3
        )
        
        logger.info(f"Found {len(similar_chunks)} similar chunks")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Similar chunk {i+1}: ID={chunk.id}, Source={chunk.source}")
            if hasattr(chunk, 'similarity_score'):
                logger.info(f"Similarity score: {chunk.similarity_score}")
            logger.info(f"Content: {chunk.content[:100]}...")
        
        # Perform RAG analysis
        logger.info("Performing RAG analysis")
        rag_result = await ai_service.perform_scam_analysis_with_rag(
            query_text=test_query,
            query_embedding=embedding,
            db=db
        )
        
        logger.info("RAG analysis result:")
        logger.info(f"Risk score: {rag_result.risk_score}")
        logger.info(f"Explanation: {rag_result.explanation[:200]}...")
        logger.info(f"Detected red flags: {rag_result.detected_red_flags}")
        logger.info(f"Recommendations: {rag_result.recommendations[:200]}...")
        logger.info(f"Confidence level: {rag_result.confidence_level}")
        logger.info(f"Model used: {rag_result.model_used}")
        
    finally:
        db.close()

def main():
    """Run the test."""
    asyncio.run(test_real_ai_with_kb())

if __name__ == "__main__":
    main()
