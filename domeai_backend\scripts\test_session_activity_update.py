#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to test the session activity update functionality.
"""

import logging
import sys
import uuid
import os
import time
from datetime import datetime, timezone

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan_session import ScanSession
from app.models.scan import Scan
from app.crud import crud_scan_session

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def test_session_activity_update():
    """Test the session activity update functionality."""
    logger.info("Testing Session Activity Update")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create a test user
        unique_email = f"test_user_{int(time.time())}@example.com"
        test_user = User(
            email=unique_email,
            hashed_password="hashed_password",
            subscription_tier="premium",
            monthly_scan_allowance=100,
            scans_this_month=0,
            scan_counter_reset_at=datetime.now(timezone.utc)
        )
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        logger.info(f"Created test user with ID {test_user.id}")
        
        # Create a scan session
        session = crud_scan_session.create_scan_session(
            db=db, owner_id=test_user.id, title="Test Session"
        )
        logger.info(f"Created scan session with ID {session.id}")
        logger.info(f"Initial last_activity_at: {session.last_activity_at}")
        
        # Wait a moment to ensure the timestamp changes
        time.sleep(2)
        
        # Update session activity
        updated_session = crud_scan_session.update_scan_session_activity(
            db=db, db_session=session
        )
        logger.info(f"Updated last_activity_at: {updated_session.last_activity_at}")
        
        # Verify the timestamp was updated
        if updated_session.last_activity_at > session.last_activity_at:
            logger.info("Session activity timestamp was successfully updated")
        else:
            logger.error("Session activity timestamp was not updated")
        
        # Create a scan in the session
        scan = Scan(
            owner_id=test_user.id,
            status="completed",
            input_content_type="text",
            input_text="This is a test scan",
            scan_session_id=session.id
        )
        db.add(scan)
        db.commit()
        db.refresh(scan)
        logger.info(f"Created scan with ID {scan.id}")
        
        # Wait a moment to ensure the timestamp changes
        time.sleep(2)
        
        # Update session activity again
        updated_session_2 = crud_scan_session.update_scan_session_activity(
            db=db, db_session=updated_session
        )
        logger.info(f"Updated last_activity_at (2): {updated_session_2.last_activity_at}")
        
        # Verify the timestamp was updated again
        if updated_session_2.last_activity_at > updated_session.last_activity_at:
            logger.info("Session activity timestamp was successfully updated again")
        else:
            logger.error("Session activity timestamp was not updated again")
        
        # Clean up
        db.delete(scan)
        db.delete(session)
        db.delete(test_user)
        db.commit()
        logger.info("Test data deleted")
        
    finally:
        db.close()

def main():
    """Run the test."""
    test_session_activity_update()

if __name__ == "__main__":
    main()
