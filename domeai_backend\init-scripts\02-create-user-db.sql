-- Create user and database
-- Use dollar-quoted string literals to avoid issues with special characters
DO $do$
BEGIN
   IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'dome_app_user') THEN
      CREATE USER dome_app_user WITH PASSWORD 'DomeAppPassword123';
   ELSE
      ALTER USER dome_app_user WITH PASSWORD 'DomeAppPassword123';
   END IF;
END
$do$;

DO
$$
BEGIN
   IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'dome_app_main_db') THEN
      CREATE DATABASE dome_app_main_db;
   END IF;
END
$$;

GRANT ALL PRIVILEGES ON DATABASE dome_app_main_db TO dome_app_user;

-- Connect to the new database
\c dome_app_main_db

-- Enable pgvector extension in the new database
CREATE EXTENSION IF NOT EXISTS vector;
