package com.domeai.data.util

import com.domeai.data.model.ValidationResult
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class for validating user input
 */
@Singleton
class Validator @Inject constructor() {

    /**
     * Validate email format
     */
    fun validateEmail(email: String): ValidationResult {
        if (email.isBlank()) {
            return ValidationResult.Invalid("Email cannot be empty")
        }

        if (!email.contains("@") || !email.contains(".")) {
            return ValidationResult.Invalid("Invalid email format")
        }

        return ValidationResult.Valid
    }

    /**
     * Validate password
     */
    fun validatePassword(password: String): ValidationResult {
        if (password.isBlank()) {
            return ValidationResult.Invalid("Password cannot be empty")
        }

        if (password.length < 8) {
            return ValidationResult.Invalid("Password must be at least 8 characters")
        }

        if (!password.any { it.isUpperCase() }) {
            return ValidationResult.Invalid("Password must contain at least one uppercase letter")
        }

        if (!password.any { it.isLowerCase() }) {
            return ValidationResult.Invalid("Password must contain at least one lowercase letter")
        }

        if (!password.any { it.isDigit() }) {
            return ValidationResult.Invalid("Password must contain at least one number")
        }

        if (!password.any { !it.isLetterOrDigit() }) {
            return ValidationResult.Invalid("Password must contain at least one special character")
        }

        return ValidationResult.Valid
    }

    /**
     * Validate password strength
     */
    fun validatePasswordStrength(password: String): ValidationResult {
        val criteria = com.domeai.ui.composables.evaluatePasswordStrength(password)

        if (!criteria.isStrong) {
            return ValidationResult.Invalid("Password does not meet strength requirements")
        }

        return ValidationResult.Valid
    }

    /**
     * Validate password confirmation
     */
    fun validatePasswordConfirmation(password: String, confirmPassword: String): ValidationResult {
        if (confirmPassword.isBlank()) {
            return ValidationResult.Invalid("Please confirm your password")
        }

        if (password != confirmPassword) {
            return ValidationResult.Invalid("Passwords do not match")
        }

        return ValidationResult.Valid
    }

    /**
     * Validate terms acceptance
     */
    fun validateTermsAccepted(accepted: Boolean): ValidationResult {
        if (!accepted) {
            return ValidationResult.Invalid("You must accept the terms and conditions")
        }

        return ValidationResult.Valid
    }
}
