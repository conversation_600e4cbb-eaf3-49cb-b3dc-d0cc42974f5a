#!/usr/bin/env python3
"""
Diagnostic script to check pgvector extension and knowledge base table status
"""
import os
import sys
import asyncio
import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_database_status():
    """Check the current status of pgvector and knowledge base table"""
    
    # Create engine and session
    engine = create_engine(str(settings.DATABASE_URL))
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    db = SessionLocal()
    try:
        logger.info("=== PGVECTOR DIAGNOSTIC REPORT ===")
        
        # 1. Check if pgvector extension exists
        logger.info("\n1. Checking pgvector extension...")
        result = db.execute(text("SELECT * FROM pg_extension WHERE extname = 'vector'"))
        extensions = result.fetchall()
        if extensions:
            logger.info(f"✅ pgvector extension is installed: {extensions[0]}")
        else:
            logger.error("❌ pgvector extension is NOT installed")
            
        # 2. Check available operators
        logger.info("\n2. Checking available vector operators...")
        try:
            result = db.execute(text("""
                SELECT oprname, oprleft::regtype, oprright::regtype 
                FROM pg_operator 
                WHERE oprname IN ('<->', '<#>', '<=>') 
                ORDER BY oprname
            """))
            operators = result.fetchall()
            if operators:
                logger.info("✅ Found vector operators:")
                for op in operators:
                    logger.info(f"   {op[0]}: {op[1]} {op[0]} {op[2]}")
            else:
                logger.error("❌ No vector operators found")
        except Exception as e:
            logger.error(f"❌ Error checking operators: {e}")
            
        # 3. Check knowledge_base_chunks table structure
        logger.info("\n3. Checking knowledge_base_chunks table...")
        try:
            result = db.execute(text("""
                SELECT column_name, data_type, udt_name 
                FROM information_schema.columns 
                WHERE table_name = 'knowledge_base_chunks' 
                ORDER BY ordinal_position
            """))
            columns = result.fetchall()
            if columns:
                logger.info("✅ knowledge_base_chunks table exists:")
                for col in columns:
                    logger.info(f"   {col[0]}: {col[1]} ({col[2]})")
            else:
                logger.error("❌ knowledge_base_chunks table does not exist")
        except Exception as e:
            logger.error(f"❌ Error checking table: {e}")
            
        # 4. Check if we have any data
        logger.info("\n4. Checking data in knowledge_base_chunks...")
        try:
            result = db.execute(text("SELECT COUNT(*) FROM knowledge_base_chunks"))
            count = result.scalar()
            logger.info(f"✅ Found {count} rows in knowledge_base_chunks")
            
            if count > 0:
                # Check a sample row
                result = db.execute(text("SELECT id, source, LENGTH(content) as content_length FROM knowledge_base_chunks LIMIT 1"))
                sample = result.fetchone()
                logger.info(f"   Sample row: ID={sample[0]}, Source={sample[1]}, Content Length={sample[2]}")
        except Exception as e:
            logger.error(f"❌ Error checking data: {e}")
            
        # 5. Test a simple vector operation
        logger.info("\n5. Testing vector operations...")
        try:
            # Try to create a test vector
            test_vector = "[" + ",".join(["0.1"] * 1536) + "]"
            result = db.execute(text("SELECT :vector::vector(1536) as test"), {"vector": test_vector})
            test_result = result.fetchone()
            logger.info("✅ Vector casting works")
            
            # Try a similarity search
            result = db.execute(text("""
                SELECT COUNT(*) FROM knowledge_base_chunks 
                WHERE embedding IS NOT NULL
            """))
            count_with_embeddings = result.scalar()
            logger.info(f"✅ Found {count_with_embeddings} rows with embeddings")
            
        except Exception as e:
            logger.error(f"❌ Error in vector operations: {e}")
            
        logger.info("\n=== END DIAGNOSTIC REPORT ===")
        
    finally:
        db.close()

if __name__ == "__main__":
    check_database_status()
