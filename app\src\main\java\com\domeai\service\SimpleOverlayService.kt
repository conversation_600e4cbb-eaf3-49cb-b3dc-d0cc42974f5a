package com.domeai.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.Toast
import androidx.core.app.NotificationCompat
import com.domeai.R

/**
 * A very simple overlay service with minimal functionality
 */
class SimpleOverlayService : Service() {
    
    private var windowManager: WindowManager? = null
    private var overlayButton: Button? = null
    private var initialX: Int = 0
    private var initialY: Int = 0
    private var initialTouchX: Float = 0f
    private var initialTouchY: Float = 0f
    
    companion object {
        private const val NOTIFICATION_ID = 9999
        private const val CHANNEL_ID = "SimpleOverlayChannel"
        private const val TAG = "SimpleOverlayService"
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Service created")
        
        // Create notification channel for Android O+
        createNotificationChannel()
        
        // Start as foreground service
        startForeground(NOTIFICATION_ID, createNotification())
        
        // Show toast for debugging
        Toast.makeText(this, "SimpleOverlayService created", Toast.LENGTH_SHORT).show()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "Service started")
        
        // Check if we have overlay permission
        if (!Settings.canDrawOverlays(this)) {
            Log.e(TAG, "No overlay permission")
            Toast.makeText(this, "No overlay permission", Toast.LENGTH_LONG).show()
            stopSelf()
            return START_NOT_STICKY
        }
        
        // Create and show the overlay button
        showOverlayButton()
        
        return START_STICKY
    }
    
    private fun showOverlayButton() {
        try {
            // Get window manager
            windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
            
            // Create a simple button
            overlayButton = Button(this).apply {
                text = "DomeAI"
                setBackgroundColor(Color.RED)
                setTextColor(Color.WHITE)
            }
            
            // Set layout parameters
            val params = WindowManager.LayoutParams(
                200, // Width
                200, // Height
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) 
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY 
                else 
                    WindowManager.LayoutParams.TYPE_PHONE,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT
            ).apply {
                gravity = Gravity.TOP or Gravity.START
                x = 100
                y = 100
            }
            
            // Set touch listener for dragging
            overlayButton?.setOnTouchListener { view, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        initialX = params.x
                        initialY = params.y
                        initialTouchX = event.rawX
                        initialTouchY = event.rawY
                        true
                    }
                    MotionEvent.ACTION_MOVE -> {
                        params.x = initialX + (event.rawX - initialTouchX).toInt()
                        params.y = initialY + (event.rawY - initialTouchY).toInt()
                        windowManager?.updateViewLayout(view, params)
                        true
                    }
                    MotionEvent.ACTION_UP -> {
                        // Check if it's a click (minimal movement)
                        val isClick = Math.abs(event.rawX - initialTouchX) < 10 && 
                                Math.abs(event.rawY - initialTouchY) < 10
                        
                        if (isClick) {
                            Toast.makeText(this, "Button clicked!", Toast.LENGTH_SHORT).show()
                        }
                        true
                    }
                    else -> false
                }
            }
            
            // Add the button to window manager
            windowManager?.addView(overlayButton, params)
            
            Toast.makeText(this, "Overlay button added", Toast.LENGTH_SHORT).show()
            Log.d(TAG, "Overlay button added successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error showing overlay button: ${e.message}")
            Toast.makeText(this, "Error: ${e.message}", Toast.LENGTH_LONG).show()
            e.printStackTrace()
        }
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Simple Overlay Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Keeps the overlay button running"
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("DomeAI Overlay")
            .setContentText("Overlay is active")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        
        // Remove the overlay button
        if (overlayButton != null && windowManager != null) {
            try {
                windowManager?.removeView(overlayButton)
                overlayButton = null
                Toast.makeText(this, "Overlay button removed", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Log.e(TAG, "Error removing overlay button: ${e.message}")
            }
        }
        
        Log.d(TAG, "Service destroyed")
    }
}
