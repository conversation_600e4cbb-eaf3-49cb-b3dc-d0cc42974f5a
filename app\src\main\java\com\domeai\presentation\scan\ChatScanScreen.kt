package com.domeai.presentation.scan

import android.content.Context
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color as ComposeColor
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.unit.Velocity
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.AttachFile
import androidx.compose.material.icons.filled.Camera
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.Link
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.TextFields
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledTonalButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SegmentedButton
import androidx.compose.material3.SegmentedButtonDefaults
import androidx.compose.material3.SingleChoiceSegmentedButtonRow
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.derivedStateOf
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.isImeVisible
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.material3.ScaffoldDefaults
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import com.domeai.ui.composables.bringIntoViewOnFocus
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.domeai.data.model.RiskLevel
import com.domeai.data.model.ScanResult
import com.domeai.data.model.ScanSourceType
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID

/**
 * Data class representing a chat message
 */
data class ChatMessage(
    val id: String = UUID.randomUUID().toString(),
    val content: String,
    val timestamp: Long = System.currentTimeMillis(),
    val isUser: Boolean,
    val scanResult: ScanResult? = null,
    val imageUri: Uri? = null,
    val inputType: ScanInputType? = null,
    val isNewScanButton: Boolean = false // For the "Or start a new scan" button message
)

/**
 * Screen for chat-based scanning interface
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)
@Composable
fun ChatScanScreen(
    onScanDetail: (String) -> Unit,
    isStandalone: Boolean = false // Add parameter to control whether this is a standalone screen or a tab
) {
    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()
    val lazyListState = rememberLazyListState()
    val focusManager = LocalFocusManager.current
    val context = LocalContext.current

    // State for the input text
    var inputText by remember { mutableStateOf("") }

    // State for the selected image
    var selectedImageUri by remember { mutableStateOf<Uri?>(null) }

    // State for the selected scan type
    var selectedScanType by remember { mutableStateOf(ScanInputType.TEXT) }

    // State for showing the scan type selector
    var showScanTypeSelector by remember { mutableStateOf(false) }

    // State for loading
    var isLoading by remember { mutableStateOf(false) }

    // Initial greeting message
    val initialGreeting = ChatMessage(
        content = "Hello! I'm DomeAI, your personal scam detector. I can help you analyze text, URLs, or images to identify potential scams. How can I assist you today?",
        isUser = false,
        timestamp = System.currentTimeMillis()
    )

    // State for chat messages
    var chatMessages by remember { mutableStateOf<List<ChatMessage>>(listOf(initialGreeting)) }

    // Function to start a new chat - defined before it's used
    fun startNewChat() {
        chatMessages = listOf(initialGreeting)
        inputText = ""
        selectedImageUri = null
        selectedScanType = ScanInputType.TEXT
        showScanTypeSelector = false
        isLoading = false
    }

    // Observe the chat reset trigger from SharedPreferences
    val sharedPrefs = remember { context.getSharedPreferences("dome_ai_prefs", Context.MODE_PRIVATE) }
    var resetTrigger by remember { mutableStateOf(sharedPrefs.getLong("chat_reset_trigger", 0L)) }

    // Check for reset trigger changes
    LaunchedEffect(Unit) {
        while (true) {
            // Check every 500ms for changes to the reset trigger
            delay(500)
            val newTrigger = sharedPrefs.getLong("chat_reset_trigger", 0L)
            if (newTrigger != resetTrigger) {
                resetTrigger = newTrigger
                // Reset the chat
                startNewChat()
            }
        }
    }

    // Scroll to bottom when scan type selector appears or disappears
    LaunchedEffect(showScanTypeSelector) {
        if (chatMessages.isNotEmpty()) {
            lazyListState.animateScrollToItem(chatMessages.size - 1)
        }
    }

    // Image picker launcher
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        selectedImageUri = uri
        if (uri != null) {
            selectedScanType = ScanInputType.IMAGE
        }
    }

    // Camera launcher
    val cameraLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture()
    ) { success ->
        if (!success) {
            selectedImageUri = null
        }
    }

    // Scroll to bottom when new messages are added
    LaunchedEffect(chatMessages.size) {
        if (chatMessages.isNotEmpty()) {
            lazyListState.animateScrollToItem(chatMessages.size - 1)
        }
    }

    // Function to add a user message
    fun addUserMessage(content: String, imageUri: Uri? = null, inputType: ScanInputType = ScanInputType.TEXT) {
        val message = ChatMessage(
            content = content,
            isUser = true,
            timestamp = System.currentTimeMillis(),
            imageUri = imageUri,
            inputType = inputType
        )
        chatMessages = chatMessages + message

        // Clear input after sending
        inputText = ""
        selectedImageUri = null
    }

    // Function to add an AI response
    fun addAIResponse(scanResult: ScanResult) {
        isLoading = false

        val riskLevelText = when (scanResult.riskLevel) {
            RiskLevel.SAFE -> "Safe"
            RiskLevel.LOW_RISK -> "Low Risk"
            RiskLevel.MEDIUM_RISK -> "Medium Risk"
            RiskLevel.HIGH_RISK -> "High Risk"
        }

        val message = ChatMessage(
            content = "Analysis Result: $riskLevelText (${scanResult.riskScore}%)\n\n${scanResult.explanation}",
            isUser = false,
            timestamp = System.currentTimeMillis(),
            scanResult = scanResult
        )
        chatMessages = chatMessages + message
    }

    // Function to handle scan
    fun performScan() {
        // Validate input
        when (selectedScanType) {
            ScanInputType.TEXT, ScanInputType.URL -> {
                if (inputText.isBlank()) {
                    coroutineScope.launch {
                        snackbarHostState.showSnackbar("Please enter some text to scan")
                    }
                    return
                }
                addUserMessage(inputText, null, selectedScanType)
            }
            ScanInputType.IMAGE -> {
                if (selectedImageUri == null) {
                    coroutineScope.launch {
                        snackbarHostState.showSnackbar("Please select an image to scan")
                    }
                    return
                }
                addUserMessage("Image scan", selectedImageUri, ScanInputType.IMAGE)
            }
        }

        // Start loading
        isLoading = true

        // Simulate scanning process (will be replaced with actual API call)
        coroutineScope.launch {
            kotlinx.coroutines.delay(2000)

            // Create mock scan result
            val scanResult = ScanResult(
                id = UUID.randomUUID().toString(),
                timestamp = System.currentTimeMillis(),
                riskScore = (0..100).random(),
                riskLevel = RiskLevel.values().random(),
                redFlags = listOf(
                    "Suspicious URL structure",
                    "Recently registered domain",
                    "Requests sensitive information"
                ).take((0..3).random()),
                explanation = "This content appears to contain elements commonly associated with scams. " +
                        "The language used is designed to create urgency and the offer seems too good to be true. " +
                        "I recommend exercising caution and verifying the source before proceeding.",
                sourceType = when (selectedScanType) {
                    ScanInputType.TEXT -> ScanSourceType.MANUAL_TEXT
                    ScanInputType.URL -> ScanSourceType.MANUAL_TEXT
                    ScanInputType.IMAGE -> ScanSourceType.MANUAL_IMAGE
                },
                sourceContent = when (selectedScanType) {
                    ScanInputType.TEXT, ScanInputType.URL -> inputText
                    ScanInputType.IMAGE -> selectedImageUri?.toString()
                }
            )

            // Add AI response
            addAIResponse(scanResult)
        }
    }

    // Function to handle follow-up question
    fun askFollowUpQuestion() {
        if (inputText.isBlank()) {
            coroutineScope.launch {
                snackbarHostState.showSnackbar("Please enter a question")
            }
            return
        }

        // Add user message
        addUserMessage(inputText)

        // Start loading
        isLoading = true

        // Simulate AI response (will be replaced with actual API call)
        coroutineScope.launch {
            kotlinx.coroutines.delay(1500)

            // Add AI response
            val message = ChatMessage(
                content = "Based on my analysis, I can provide more information about this potential scam. " +
                        "This type of content often targets vulnerable individuals by creating a false sense of urgency. " +
                        "The sender typically wants you to act quickly without thinking. " +
                        "I recommend not clicking any links, not providing any personal information, and reporting this to the relevant platform.",
                isUser = false,
                timestamp = System.currentTimeMillis()
            )
            chatMessages = chatMessages + message
            isLoading = false
        }
    }

    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
        // Only show TopAppBar if this is a standalone screen, not when used as a tab
        topBar = if (isStandalone) {
            {
                TopAppBar(
                    title = { Text("Dome AI") },
                    actions = {
                        // New Chat button
                        IconButton(onClick = { startNewChat() }) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = "New Chat"
                            )
                        }

                        // Profile button (placeholder)
                        IconButton(onClick = { /* Profile menu will be implemented later */ }) {
                            Icon(
                                imageVector = Icons.Default.Person,
                                contentDescription = "Profile"
                            )
                        }
                    }
                )
            }
        } else {
            // Return an empty lambda when not standalone
            {}
        },
        // Set to zero insets so we can handle them manually
        contentWindowInsets = WindowInsets(0, 0, 0, 0)
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Get Premium Button - only visible when there are no user messages
            // Check if there are only AI messages (just the initial greeting)
            val showGetPremiumButton = chatMessages.all { !it.isUser }

            // Use AnimatedVisibility for smooth transitions
            AnimatedVisibility(
                visible = showGetPremiumButton,
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically()
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    FilledTonalButton(
                        onClick = { /* Will be implemented later */ },
                        modifier = Modifier
                            .width(IntrinsicSize.Min)
                            .padding(horizontal = 16.dp)
                    ) {
                        Text(
                            text = "Get Premium",
                            style = MaterialTheme.typography.labelLarge,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }

            // Main content area with chat messages
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(horizontal = 16.dp)
                    // Add overscroll effect for better UX
                    .then(OverscrollEffect()),
                state = lazyListState,
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                item {
                    Spacer(modifier = Modifier.height(8.dp))
                }

                items(chatMessages) { message ->
                    ChatMessageItem(
                        message = message,
                        onViewDetails = { scanResult ->
                            if (scanResult != null) {
                                onScanDetail(scanResult.id)
                            }
                        }
                    )
                }

                if (isLoading) {
                    item {
                        AITypingIndicator()
                    }
                }

                item {
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }

            // Input bar container - following Jetchat's approach with navigationBarsPadding and imePadding
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .navigationBarsPadding()
                    .imePadding(),
                tonalElevation = 2.dp,
                shadowElevation = 2.dp
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                ) {
                    // Selected image preview
                    AnimatedVisibility(
                        visible = selectedImageUri != null,
                        enter = fadeIn() + expandVertically(),
                        exit = fadeOut() + shrinkVertically()
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(200.dp)
                                .padding(bottom = 8.dp)
                                .clip(RoundedCornerShape(8.dp))
                                .border(
                                    width = 1.dp,
                                    color = MaterialTheme.colorScheme.outline,
                                    shape = RoundedCornerShape(8.dp)
                                )
                        ) {
                            AsyncImage(
                                model = ImageRequest.Builder(LocalContext.current)
                                    .data(selectedImageUri)
                                    .crossfade(true)
                                    .build(),
                                contentDescription = "Selected Image",
                                contentScale = ContentScale.Fit,
                                modifier = Modifier.fillMaxSize()
                            )

                            // Clear button
                            IconButton(
                                onClick = { selectedImageUri = null },
                                modifier = Modifier
                                    .align(Alignment.TopEnd)
                                    .padding(4.dp)
                                    .size(32.dp)
                                    .background(
                                        color = MaterialTheme.colorScheme.surface.copy(alpha = 0.7f),
                                        shape = CircleShape
                                    )
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Clear,
                                    contentDescription = "Clear Image",
                                    tint = MaterialTheme.colorScheme.onSurface,
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                        }
                    }

                    // Scan type selector
                    AnimatedVisibility(
                        visible = showScanTypeSelector,
                        enter = fadeIn() + expandVertically(),
                        exit = fadeOut() + shrinkVertically()
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 8.dp)
                        ) {
                            SingleChoiceSegmentedButtonRow(
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                SegmentedButton(
                                    selected = selectedScanType == ScanInputType.TEXT,
                                    onClick = {
                                        selectedScanType = ScanInputType.TEXT
                                        selectedImageUri = null
                                    },
                                    shape = SegmentedButtonDefaults.itemShape(index = 0, count = 3),
                                    icon = {
                                        Icon(
                                            imageVector = Icons.Default.TextFields,
                                            contentDescription = null,
                                            modifier = Modifier.size(SegmentedButtonDefaults.IconSize)
                                        )
                                    }
                                ) {
                                    Text("Text")
                                }

                                SegmentedButton(
                                    selected = selectedScanType == ScanInputType.URL,
                                    onClick = {
                                        selectedScanType = ScanInputType.URL
                                        selectedImageUri = null
                                    },
                                    shape = SegmentedButtonDefaults.itemShape(index = 1, count = 3),
                                    icon = {
                                        Icon(
                                            imageVector = Icons.Default.Link,
                                            contentDescription = null,
                                            modifier = Modifier.size(SegmentedButtonDefaults.IconSize)
                                        )
                                    }
                                ) {
                                    Text("URL")
                                }

                                SegmentedButton(
                                    selected = selectedScanType == ScanInputType.IMAGE,
                                    onClick = {
                                        selectedScanType = ScanInputType.IMAGE
                                        imagePickerLauncher.launch("image/*")
                                    },
                                    shape = SegmentedButtonDefaults.itemShape(index = 2, count = 3),
                                    icon = {
                                        Icon(
                                            imageVector = Icons.Default.Image,
                                            contentDescription = null,
                                            modifier = Modifier.size(SegmentedButtonDefaults.IconSize)
                                        )
                                    }
                                ) {
                                    Text("Image")
                                }
                            }

                            if (selectedScanType == ScanInputType.IMAGE && selectedImageUri == null) {
                                Spacer(modifier = Modifier.height(8.dp))

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    // Gallery option
                                    Card(
                                        modifier = Modifier
                                            .weight(1f)
                                            .height(80.dp)
                                            .clickable { imagePickerLauncher.launch("image/*") },
                                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                                    ) {
                                        Row(
                                            modifier = Modifier
                                                .fillMaxSize()
                                                .padding(8.dp),
                                            horizontalArrangement = Arrangement.Center,
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Image,
                                                contentDescription = "Gallery",
                                                modifier = Modifier.size(24.dp),
                                                tint = MaterialTheme.colorScheme.primary
                                            )
                                            Spacer(modifier = Modifier.size(8.dp))
                                            Text(
                                                text = "Gallery",
                                                style = MaterialTheme.typography.bodyMedium,
                                                fontWeight = FontWeight.Medium
                                            )
                                        }
                                    }

                                    // Camera option
                                    Card(
                                        modifier = Modifier
                                            .weight(1f)
                                            .height(80.dp)
                                            .clickable {
                                                // Create a URI for the camera to save the image to
                                                val uri = Uri.parse("content://temp_camera_image_${UUID.randomUUID()}")
                                                selectedImageUri = uri
                                                cameraLauncher.launch(uri)
                                            },
                                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                                    ) {
                                        Row(
                                            modifier = Modifier
                                                .fillMaxSize()
                                                .padding(8.dp),
                                            horizontalArrangement = Arrangement.Center,
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Camera,
                                                contentDescription = "Camera",
                                                modifier = Modifier.size(24.dp),
                                                tint = MaterialTheme.colorScheme.primary
                                            )
                                            Spacer(modifier = Modifier.size(8.dp))
                                            Text(
                                                text = "Camera",
                                                style = MaterialTheme.typography.bodyMedium,
                                                fontWeight = FontWeight.Medium
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Input bar
                    Surface(
                        modifier = Modifier.fillMaxWidth(),
                        color = MaterialTheme.colorScheme.surface,
                        shape = RoundedCornerShape(24.dp),
                        tonalElevation = 1.dp
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 4.dp, vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Attachment button
                            IconButton(
                                onClick = { showScanTypeSelector = !showScanTypeSelector },
                                modifier = Modifier.size(40.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.AttachFile,
                                    contentDescription = "Attach",
                                    tint = MaterialTheme.colorScheme.primary
                                )
                            }

                            // Text input field - supports multi-line input with proper height behavior
                            OutlinedTextField(
                                value = inputText,
                                onValueChange = { inputText = it },
                                placeholder = {
                                    Text(
                                        when {
                                            selectedImageUri != null -> "Add a message about this image..."
                                            selectedScanType == ScanInputType.URL -> "Enter a URL to scan..."
                                            else -> "Ask anything"
                                        }
                                    )
                                },
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(horizontal = 4.dp)
                                    // Allow the TextField to grow with content up to a max height
                                    .heightIn(min = 40.dp, max = 120.dp)
                                    .bringIntoViewOnFocus(),
                                keyboardOptions = KeyboardOptions(
                                    imeAction = ImeAction.Send
                                ),
                                keyboardActions = KeyboardActions(
                                    onSend = {
                                        if (selectedImageUri != null || selectedScanType != ScanInputType.TEXT) {
                                            performScan()
                                            focusManager.clearFocus() // Dismiss keyboard
                                        } else {
                                            askFollowUpQuestion()
                                            focusManager.clearFocus() // Dismiss keyboard
                                        }
                                    }
                                ),
                                maxLines = 5, // Allow multiple lines
                                singleLine = false, // Not single line
                                shape = RoundedCornerShape(24.dp),
                                // Use standard colors with no border
                                colors = androidx.compose.material3.OutlinedTextFieldDefaults.colors(
                                    unfocusedBorderColor = ComposeColor.Transparent,
                                    focusedBorderColor = ComposeColor.Transparent
                                )
                            )

                            // Send button
                            IconButton(
                                onClick = {
                                    if (selectedImageUri != null || selectedScanType != ScanInputType.TEXT) {
                                        performScan()
                                        focusManager.clearFocus() // Dismiss keyboard
                                    } else {
                                        askFollowUpQuestion()
                                        focusManager.clearFocus() // Dismiss keyboard
                                    }
                                },
                                enabled = !isLoading && (inputText.isNotBlank() || selectedImageUri != null),
                                modifier = Modifier.size(40.dp)
                            ) {
                                if (isLoading) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(24.dp),
                                        strokeWidth = 2.dp
                                    )
                                } else {
                                    Icon(
                                        imageVector = Icons.AutoMirrored.Filled.Send,
                                        contentDescription = "Send",
                                        tint = if (inputText.isNotBlank() || selectedImageUri != null)
                                            MaterialTheme.colorScheme.primary
                                        else
                                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * Composable for displaying a chat message
 */
@Composable
fun ChatMessageItem(
    message: ChatMessage,
    onViewDetails: (ScanResult?) -> Unit,
    onStartNewScan: (() -> Unit)? = null
) {
    val isUser = message.isUser
    val dateFormat = remember { SimpleDateFormat("h:mm a", Locale.getDefault()) }
    val formattedTime = dateFormat.format(Date(message.timestamp))

    // Different styling for user vs AI messages
    if (isUser) {
        // USER MESSAGE - Right-aligned with bubble
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.End
        ) {
            // Message bubble for user
            Card(
                modifier = Modifier
                    .padding(vertical = 2.dp)
                    .widthIn(max = 280.dp),
                shape = RoundedCornerShape(16.dp, 4.dp, 16.dp, 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    contentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(12.dp)
                ) {
                    // Image if present
                    if (message.imageUri != null) {
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(message.imageUri)
                                .crossfade(true)
                                .build(),
                            contentDescription = "Image",
                            contentScale = ContentScale.Fit,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(180.dp)
                                .clip(RoundedCornerShape(8.dp))
                        )

                        Spacer(modifier = Modifier.height(8.dp))
                    }

                    // Message content
                    Text(
                        text = message.content,
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }

            // Timestamp
            Text(
                text = formattedTime,
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                modifier = Modifier.padding(horizontal = 4.dp, vertical = 2.dp)
            )
        }
    } else {
        // AI MESSAGE - Left-aligned without bubble, full width
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.Start
        ) {
            // AI messages don't use a card/bubble - they span full width
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 4.dp, top = 4.dp, bottom = 4.dp, end = 8.dp)
            ) {
                // Message content with markdown support and loading animation for analyzing messages
                if (message.content.contains("Analyzing your submission")) {
                    // Special handling for analyzing message with loading animation
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = parseSimpleMarkdown(message.content),
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onBackground,
                            modifier = Modifier.weight(1f)
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        // Modern loading animation
                        LoadingAnimation()
                    }
                } else {
                    // Regular message content
                    Text(
                        text = parseSimpleMarkdown(message.content),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onBackground,
                        modifier = Modifier.padding(end = 8.dp) // Add extra end padding to prevent text from touching screen edge
                    )
                }

                // View details button for scan results
                if (message.scanResult != null) {
                    Spacer(modifier = Modifier.height(24.dp))

                    // Centered, styled View Details button
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        FilledTonalButton(
                            onClick = { onViewDetails(message.scanResult) },
                            modifier = Modifier
                                .fillMaxWidth(0.6f) // 60% of available width
                                .height(48.dp),
                            shape = RoundedCornerShape(12.dp),
                            colors = ButtonDefaults.filledTonalButtonColors(
                                containerColor = MaterialTheme.colorScheme.primaryContainer,
                                contentColor = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        ) {
                            Icon(
                                imageVector = Icons.Default.Visibility,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "View Full Report",
                                style = MaterialTheme.typography.labelLarge,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }

                // New scan button for special messages
                if (message.isNewScanButton && onStartNewScan != null) {
                    Spacer(modifier = Modifier.height(16.dp))

                    // Blue "Start New Scan" button
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        OutlinedButton(
                            onClick = { onStartNewScan() },
                            modifier = Modifier
                                .fillMaxWidth(0.5f) // 50% of available width for lighter look
                                .height(40.dp), // Slightly smaller height
                            shape = RoundedCornerShape(20.dp), // More rounded for lighter look
                            border = BorderStroke(
                                width = 1.dp,
                                color = MaterialTheme.colorScheme.primary // Blue border
                            ),
                            colors = ButtonDefaults.outlinedButtonColors(
                                containerColor = ComposeColor.Transparent,
                                contentColor = MaterialTheme.colorScheme.primary // Blue text
                            )
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(6.dp))
                            Text(
                                text = "Start New Scan",
                                style = MaterialTheme.typography.labelMedium,
                                fontWeight = FontWeight.Normal
                            )
                        }
                    }
                }
            }

            // Timestamp
            Text(
                text = formattedTime,
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                modifier = Modifier.padding(horizontal = 4.dp, vertical = 2.dp)
            )
        }
    }
}

/**
 * Custom overscroll effect implementation for LazyColumn
 * Provides a visual indicator when scrolling beyond the content boundaries
 */
@Composable
fun OverscrollEffect(): Modifier {
    val overscrollColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)

    // Track overscroll amount
    val overscrollY = remember { mutableStateOf(0f) }

    // Create nested scroll connection to detect overscroll
    val nestedScrollConnection = remember {
        object : NestedScrollConnection {
            override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
                // Reset overscroll when scrolling in the opposite direction
                if ((overscrollY.value > 0 && available.y < 0) ||
                    (overscrollY.value < 0 && available.y > 0)) {
                    overscrollY.value = 0f
                }
                return Offset.Zero
            }

            override fun onPostScroll(
                consumed: Offset,
                available: Offset,
                source: NestedScrollSource
            ): Offset {
                // If there's unconsumed scroll, it means we're at the edge
                if (available.y != 0f) {
                    // Dampen the overscroll effect (only use a fraction of the available scroll)
                    overscrollY.value += available.y * 0.2f
                    // Limit maximum overscroll
                    overscrollY.value = overscrollY.value.coerceIn(-100f, 100f)
                }
                return Offset.Zero
            }

            override suspend fun onPostFling(consumed: Velocity, available: Velocity): Velocity {
                // Reset overscroll after flinging
                overscrollY.value = 0f
                return Velocity.Zero
            }
        }
    }

    return Modifier
        .nestedScroll(nestedScrollConnection)
        .drawWithContent {
            // Draw the content first
            drawContent()

            // Then draw overscroll indicators if needed
            val height = size.height
            val width = size.width

            if (overscrollY.value > 0) {
                // Top overscroll indicator
                drawRect(
                    brush = Brush.verticalGradient(
                        colors = listOf(overscrollColor, ComposeColor.Transparent),
                        startY = 0f,
                        endY = overscrollY.value
                    ),
                    size = size.copy(height = overscrollY.value)
                )
            } else if (overscrollY.value < 0) {
                // Bottom overscroll indicator
                drawRect(
                    brush = Brush.verticalGradient(
                        colors = listOf(ComposeColor.Transparent, overscrollColor),
                        startY = height + overscrollY.value,
                        endY = height
                    ),
                    topLeft = Offset(0f, height + overscrollY.value),
                    size = size.copy(height = -overscrollY.value)
                )
            }
        }
}

/**
 * Composable for displaying a typing indicator
 */
@Composable
fun AITypingIndicator() {
    Row(
        modifier = Modifier
            .padding(vertical = 4.dp)
            .widthIn(max = 100.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Typing dots
        Row(
            modifier = Modifier
                .clip(RoundedCornerShape(16.dp))
                .background(MaterialTheme.colorScheme.secondaryContainer)
                .padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            repeat(3) { index ->
                val delay = index * 300
                var visible by remember { mutableStateOf(false) }

                LaunchedEffect(Unit) {
                    while (true) {
                        kotlinx.coroutines.delay(delay.toLong())
                        visible = true
                        kotlinx.coroutines.delay(900)
                        visible = false
                        kotlinx.coroutines.delay(900)
                    }
                }

                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .clip(CircleShape)
                        .background(
                            if (visible)
                                MaterialTheme.colorScheme.onSecondaryContainer
                            else
                                MaterialTheme.colorScheme.onSecondaryContainer.copy(alpha = 0.3f)
                        )
                )
            }
        }
    }
}

/**
 * Parse simple markdown for bold text
 */
@Composable
fun parseSimpleMarkdown(text: String): androidx.compose.ui.text.AnnotatedString {
    return buildAnnotatedString {
        var currentIndex = 0
        val boldPattern = Regex("\\*\\*(.*?)\\*\\*")

        boldPattern.findAll(text).forEach { matchResult ->
            // Add text before the bold part
            if (matchResult.range.first > currentIndex) {
                append(text.substring(currentIndex, matchResult.range.first))
            }

            // Add bold text
            withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)) {
                append(matchResult.groupValues[1])
            }

            currentIndex = matchResult.range.last + 1
        }

        // Add remaining text
        if (currentIndex < text.length) {
            append(text.substring(currentIndex))
        }
    }
}

/**
 * Modern loading animation composable
 */
@Composable
fun LoadingAnimation() {
    val infiniteTransition = rememberInfiniteTransition(label = "loading")
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )

    Canvas(
        modifier = Modifier.size(20.dp)
    ) {
        val strokeWidth = 2.dp.toPx()
        val radius = (size.minDimension - strokeWidth) / 2

        rotate(rotation) {
            drawArc(
                color = androidx.compose.ui.graphics.Color.Blue,
                startAngle = 0f,
                sweepAngle = 270f,
                useCenter = false,
                style = androidx.compose.ui.graphics.drawscope.Stroke(
                    width = strokeWidth,
                    cap = androidx.compose.ui.graphics.StrokeCap.Round
                ),
                size = androidx.compose.ui.geometry.Size(radius * 2, radius * 2),
                topLeft = Offset(
                    (size.width - radius * 2) / 2,
                    (size.height - radius * 2) / 2
                )
            )
        }
    }
}