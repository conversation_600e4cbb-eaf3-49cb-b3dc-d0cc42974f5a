import requests

# Simple payload
payload = {
    "message": {
        "data": "test-data",
        "messageId": "test-message-id",
        "publishTime": "2023-05-20T10:00:00.000Z"
    },
    "subscription": "test-subscription"
}

# API endpoint
url = "http://localhost:8000/webhook"

print("Sending test webhook...")
try:
    response = requests.post(url, json=payload)
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")
except Exception as e:
    print(f"Error: {str(e)}")
