"""
<PERSON><PERSON><PERSON> to fix the webhooks router in the DomeAI backend.

This script will:
1. Create a new webhooks.py file with the correct router definition
2. Update the api.py file to include the webhooks router correctly
"""
import os
import shutil

# Define the correct webhooks.py content
webhooks_content = '''"""
Webhook endpoints for external services.

This module contains endpoints for receiving webhooks from external services,
such as Google Play for Real-Time Developer Notifications (RTDNs).
"""
import base64
import json
import logging
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.schemas.google_play import PubSubMessageData, DeveloperNotification
from app.services import google_play_service

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/googleplay/rtdn", status_code=status.HTTP_200_OK)
async def google_play_rtdn(
    payload: PubSubMessageData,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Receive and process Google Play Real-Time Developer Notifications (RTDNs).
    
    This endpoint receives notifications from Google Cloud Pub/Sub when events
    occur in Google Play, such as subscription purchases, renewals, or cancellations.
    
    Args:
        payload: The Pub/Sub message payload
        db: Database session
        
    Returns:
        A success response to acknowledge receipt of the notification
    """
    logger.info(f"Received Google Play RTDN from subscription: {payload.subscription}")
    
    try:
        # Extract and decode the base64-encoded data
        encoded_data = payload.message.data
        decoded_data = base64.b64decode(encoded_data).decode("utf-8")
        
        # Parse the decoded data as JSON
        notification_data = json.loads(decoded_data)
        
        # Convert to DeveloperNotification model
        notification = DeveloperNotification(**notification_data)
        
        # Log the notification details
        logger.info(f"RTDN Version: {notification.version}")
        logger.info(f"Package Name: {notification.packageName}")
        logger.info(f"Event Time: {notification.eventTimeMillis}")
        
        if notification.subscriptionNotification:
            logger.info(f"Subscription Notification Type: {notification.subscriptionNotification.notificationType}")
            logger.info(f"Subscription ID: {notification.subscriptionNotification.subscriptionId}")
        elif notification.testNotification:
            logger.info("Test Notification")
        elif notification.oneTimeProductNotification:
            logger.info(f"One-Time Product Notification Type: {notification.oneTimeProductNotification.notificationType}")
        elif notification.voidedPurchaseNotification:
            logger.info(f"Voided Purchase Notification Type: {notification.voidedPurchaseNotification.notificationType}")
        
        # Process the notification (just log for now, actual processing will be implemented later)
        await google_play_service.process_rtdn(db, notification)
        
        # Return a success response to acknowledge receipt
        return {"status": "success", "message": "RTDN received and processed"}
    
    except Exception as e:
        logger.error(f"Error processing Google Play RTDN: {str(e)}", exc_info=True)
        # Still return a success response to prevent Pub/Sub from retrying
        # In a production environment, you might want to handle certain errors differently
        return {"status": "error", "message": str(e)}
'''

# Define the correct api.py content
api_content = '''from fastapi import APIRouter

# Import endpoint routers
from app.api.v1.endpoints import auth, scans, sessions, webhooks

api_router = APIRouter()

# Include endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(scans.router, prefix="/scans", tags=["scans"])
api_router.include_router(sessions.router, prefix="/sessions", tags=["sessions"])
api_router.include_router(webhooks.router, prefix="/webhooks", tags=["webhooks"])
'''

def fix_webhooks_router():
    """
    Fix the webhooks router in the DomeAI backend.
    """
    # Create backup of the original files
    webhooks_path = os.path.join("domeai_backend", "app", "api", "v1", "endpoints", "webhooks.py")
    api_path = os.path.join("domeai_backend", "app", "api", "v1", "api.py")
    
    if os.path.exists(webhooks_path):
        shutil.copy2(webhooks_path, f"{webhooks_path}.bak")
    
    if os.path.exists(api_path):
        shutil.copy2(api_path, f"{api_path}.bak")
    
    # Create the directories if they don't exist
    os.makedirs(os.path.dirname(webhooks_path), exist_ok=True)
    os.makedirs(os.path.dirname(api_path), exist_ok=True)
    
    # Write the correct content to the files
    with open(webhooks_path, "w") as f:
        f.write(webhooks_content)
    
    with open(api_path, "w") as f:
        f.write(api_content)
    
    print(f"Fixed webhooks router in {webhooks_path}")
    print(f"Fixed API router in {api_path}")
    print("Restart the API server to apply the changes.")

if __name__ == "__main__":
    fix_webhooks_router()
