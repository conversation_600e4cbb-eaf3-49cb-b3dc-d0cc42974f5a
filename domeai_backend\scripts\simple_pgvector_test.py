#!/usr/bin/env python
"""
Simple script to test pgvector functionality.
"""

import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Use the existing database connection from app.core.database
from app.core.database import SessionLocal

def test_pgvector():
    """Test pgvector functionality."""
    print("Testing pgvector functionality")

    # Create a database session
    db = SessionLocal()
    try:
        # Check if the pgvector extension is installed
        result = db.execute(text("SELECT * FROM pg_extension WHERE extname = 'vector'"))
        extensions = result.fetchall()
        print(f"Found {len(extensions)} extensions with name 'vector'")
        for ext in extensions:
            print(f"Extension: {ext}")

        # Check the knowledge_base_chunks table structure
        result = db.execute(text("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'knowledge_base_chunks'"))
        columns = result.fetchall()
        print(f"Found {len(columns)} columns in knowledge_base_chunks table")
        for col in columns:
            print(f"Column: {col.column_name}, Type: {col.data_type}")

        # Count the number of rows in the knowledge_base_chunks table
        result = db.execute(text("SELECT COUNT(*) FROM knowledge_base_chunks"))
        count = result.scalar()
        print(f"Found {count} rows in knowledge_base_chunks table")

        # Get a sample row from the knowledge_base_chunks table
        if count > 0:
            result = db.execute(text("SELECT id, content, source FROM knowledge_base_chunks LIMIT 1"))
            row = result.fetchone()
            print(f"Sample row: ID={row.id}, Source={row.source}")
            print(f"Content: {row.content[:100]}...")

            # Try a simple vector operation
            print("Testing vector operation")
            try:
                # Create a test vector with square brackets for pgvector
                test_vector = "[" + ",".join(["0.1"] * 1536) + "]"

                # Use raw SQL with the pgvector operator
                query = text("""
                    SELECT id, content, source,
                           1 - (embedding <-> :embedding) AS similarity_score
                    FROM knowledge_base_chunks
                    ORDER BY embedding <-> :embedding
                    LIMIT 3
                """)

                # Execute the query with parameters
                result = db.execute(query, {"embedding": test_vector})

                # Process the results
                rows = result.fetchall()
                print(f"Found {len(rows)} similar rows")
                for row in rows:
                    print(f"Similar row: ID={row.id}, Source={row.source}, Similarity={row.similarity_score}")
            except Exception as e:
                print(f"Error in vector operation: {str(e)}")

    finally:
        db.close()

if __name__ == "__main__":
    test_pgvector()
