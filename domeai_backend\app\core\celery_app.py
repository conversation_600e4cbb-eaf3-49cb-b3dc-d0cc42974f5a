from celery import Celery
from celery.schedules import crontab

from app.core.config import settings

celery_app = Celery("domeai")
celery_app.conf.broker_url = settings.CELERY_BROKER_URL
celery_app.conf.result_backend = settings.CELERY_RESULT_BACKEND
celery_app.conf.task_routes = {"app.tasks.*": {"queue": "celery"}}

# Optional configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    worker_prefetch_multiplier=1,
    task_acks_late=True,
)

# Periodic tasks configuration to keep worker alive
celery_app.conf.beat_schedule = {
    'heartbeat-every-30-seconds': {
        'task': 'app.tasks.scan_tasks.heartbeat_task',
        'schedule': 30.0,  # Run every 30 seconds
    },
}
celery_app.conf.timezone = 'UTC'

# Import tasks to register them with Celery
import app.tasks.scan_tasks
