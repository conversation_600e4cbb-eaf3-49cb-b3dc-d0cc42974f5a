#!/usr/bin/env python
"""
Script to test the scan session API endpoints.
"""

import logging
import sys
import uuid
import os
import time
import json
import requests
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# API base URL
API_BASE_URL = "http://localhost:8000/api/v1"

# User context for the first scan
USER_CONTEXT = """They almost got me. My intuition is almost on point. I was so close too. Holy Shit. My intuition is "almost" on point. I finally got one. So I'm on a "dating" app. I matched with a girl in my area. She seemed a little too good to be true. So I of course proceeded with caution."""

def register_user():
    """Register a new user."""
    unique_email = f"test_premium_user_{int(time.time())}@example.com"
    password = "testpassword123"
    
    url = f"{API_BASE_URL}/auth/register"
    data = {
        "email": unique_email,
        "password": password
    }
    
    response = requests.post(url, json=data)
    
    if response.status_code == 201:
        logger.info(f"User registered successfully: {unique_email}")
        return unique_email, password
    else:
        logger.error(f"Failed to register user: {response.text}")
        return None, None

def login_user(email, password):
    """Login a user and get access token."""
    url = f"{API_BASE_URL}/auth/login"
    data = {
        "username": email,
        "password": password
    }
    
    response = requests.post(url, data=data)
    
    if response.status_code == 200:
        token = response.json().get("access_token")
        logger.info(f"User logged in successfully")
        return token
    else:
        logger.error(f"Failed to login: {response.text}")
        return None

def upgrade_user_to_premium(token):
    """Upgrade user to premium tier (this is a mock function)."""
    # In a real scenario, this would be done through an admin API or directly in the database
    # For testing purposes, we'll assume the user is already premium
    logger.info("User upgraded to premium tier (mock)")
    return True

def create_scan_session(token):
    """Create a new scan session."""
    url = f"{API_BASE_URL}/sessions/"
    headers = {"Authorization": f"Bearer {token}"}
    data = {
        "title": "Test Conversational Scam Session"
    }
    
    response = requests.post(url, json=data, headers=headers)
    
    if response.status_code == 201:
        session_id = response.json().get("id")
        logger.info(f"Scan session created successfully: {session_id}")
        return session_id
    else:
        logger.error(f"Failed to create scan session: {response.text}")
        return None

def submit_text_scan(token, text, session_id=None, user_provided_context=None):
    """Submit a text scan."""
    url = f"{API_BASE_URL}/scans/"
    headers = {"Authorization": f"Bearer {token}"}
    data = {
        "input_text": text,
        "input_content_type": "text",
        "user_provided_context": user_provided_context
    }
    
    if session_id:
        data["scan_session_id"] = session_id
    
    response = requests.post(url, json=data, headers=headers)
    
    if response.status_code == 202:
        scan_id = response.json().get("id")
        logger.info(f"Text scan submitted successfully: {scan_id}")
        return scan_id
    else:
        logger.error(f"Failed to submit text scan: {response.text}")
        return None

def get_user_info(token):
    """Get user information."""
    url = f"{API_BASE_URL}/auth/me"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        user_info = response.json()
        logger.info(f"User info retrieved successfully")
        return user_info
    else:
        logger.error(f"Failed to get user info: {response.text}")
        return None

def get_scan_session(token, session_id):
    """Get scan session details."""
    url = f"{API_BASE_URL}/sessions/{session_id}"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        session_info = response.json()
        logger.info(f"Session info retrieved successfully")
        return session_info
    else:
        logger.error(f"Failed to get session info: {response.text}")
        return None

def get_scan(token, scan_id):
    """Get scan details."""
    url = f"{API_BASE_URL}/scans/{scan_id}"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        scan_info = response.json()
        logger.info(f"Scan info retrieved successfully")
        return scan_info
    else:
        logger.error(f"Failed to get scan info: {response.text}")
        return None

def test_scan_session_api():
    """Test the scan session API endpoints."""
    logger.info("Testing Scan Session API")
    
    # Register a new user
    email, password = register_user()
    if not email or not password:
        logger.error("Failed to register user, aborting test")
        return
    
    # Login the user
    token = login_user(email, password)
    if not token:
        logger.error("Failed to login, aborting test")
        return
    
    # Upgrade user to premium tier
    upgrade_user_to_premium(token)
    
    # Get user info before scans
    user_before = get_user_info(token)
    if not user_before:
        logger.error("Failed to get user info, aborting test")
        return
    
    scans_before = user_before.get("scans_this_month", 0)
    logger.info(f"Scans before: {scans_before}")
    
    # Create a scan session
    session_id = create_scan_session(token)
    if not session_id:
        logger.error("Failed to create scan session, aborting test")
        return
    
    # Submit first scan in the session
    scan1_id = submit_text_scan(
        token, 
        "This is the first message in the conversation. The person is asking for personal information.",
        session_id=session_id,
        user_provided_context=USER_CONTEXT
    )
    if not scan1_id:
        logger.error("Failed to submit first scan, aborting test")
        return
    
    # Get user info after first scan
    user_after_scan1 = get_user_info(token)
    if not user_after_scan1:
        logger.error("Failed to get user info after first scan, aborting test")
        return
    
    scans_after_scan1 = user_after_scan1.get("scans_this_month", 0)
    logger.info(f"Scans after first scan: {scans_after_scan1}")
    logger.info(f"Scan credit consumed for first scan: {scans_after_scan1 - scans_before}")
    
    # Submit second scan in the session
    scan2_id = submit_text_scan(
        token, 
        "This is the second message in the conversation. The person is now asking for financial information.",
        session_id=session_id
    )
    if not scan2_id:
        logger.error("Failed to submit second scan, aborting test")
        return
    
    # Get user info after second scan
    user_after_scan2 = get_user_info(token)
    if not user_after_scan2:
        logger.error("Failed to get user info after second scan, aborting test")
        return
    
    scans_after_scan2 = user_after_scan2.get("scans_this_month", 0)
    logger.info(f"Scans after second scan: {scans_after_scan2}")
    logger.info(f"Scan credit consumed for second scan: {scans_after_scan2 - scans_after_scan1}")
    
    # Get session details
    session_info = get_scan_session(token, session_id)
    if not session_info:
        logger.error("Failed to get session info, aborting test")
        return
    
    logger.info(f"Session details:")
    logger.info(f"Session ID: {session_info.get('id')}")
    logger.info(f"Session title: {session_info.get('title')}")
    logger.info(f"Session created at: {session_info.get('created_at')}")
    logger.info(f"Session last activity at: {session_info.get('last_activity_at')}")
    
    # Get scan details
    scan1_info = get_scan(token, scan1_id)
    if scan1_info:
        logger.info(f"Scan 1 details:")
        logger.info(f"Scan ID: {scan1_info.get('id')}")
        logger.info(f"Scan status: {scan1_info.get('status')}")
        logger.info(f"Scan session ID: {scan1_info.get('scan_session_id')}")
    
    scan2_info = get_scan(token, scan2_id)
    if scan2_info:
        logger.info(f"Scan 2 details:")
        logger.info(f"Scan ID: {scan2_info.get('id')}")
        logger.info(f"Scan status: {scan2_info.get('status')}")
        logger.info(f"Scan session ID: {scan2_info.get('scan_session_id')}")
    
    logger.info("Test completed successfully")

def main():
    """Run the test."""
    test_scan_session_api()

if __name__ == "__main__":
    main()
