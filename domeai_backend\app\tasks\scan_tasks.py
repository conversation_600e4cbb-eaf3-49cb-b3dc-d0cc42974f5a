import os
import time
import logging
import asyncio
import uuid
from typing import Optional, Dict, Any, List

from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.core.config import settings
from app.core.database import SessionLocal
from app.core.celery_app import celery_app
from app.services.ai_service_factory import get_ai_service
from app.crud import crud_scan_session

logger = logging.getLogger(__name__)


@celery_app.task
def process_scan_task(scan_id: int, user_tier: str = "free", is_expert_scan: bool = False) -> Optional[dict]:
    """
    Celery task to process a scan asynchronously.

    Args:
        scan_id: ID of the scan to process
        user_tier: User's subscription tier ("free", "premium", or "expert")
        is_expert_scan: Whether to use expert scan (o4-mini) for analysis

    Returns:
        Dictionary with task result information or None if scan not found
    """
    logger.info(f"Starting processing of scan {scan_id} for user tier: {user_tier}, expert scan: {is_expert_scan}")

    # Create a new database session for this task
    db = SessionLocal()
    try:
        # Retrieve the scan record
        scan = db.query(models.scan.Scan).filter(models.scan.Scan.id == scan_id).first()

        if not scan:
            logger.error(f"Scan {scan_id} not found")
            return None

        # Update scan status to "processing"
        crud.crud_scan.update_scan_status(db=db, db_scan=scan, status="processing")

        # Check if this scan is part of a session
        previous_scans_in_session = []
        accumulated_session_context = None

        if scan.scan_session_id:
            logger.info(f"Scan {scan_id} is part of session {scan.scan_session_id}")

            # Get previous scans in this session
            previous_scans_in_session = crud_scan_session.get_previous_scans_in_session(
                db=db,
                session_id=scan.scan_session_id,
                current_scan_id=scan.id
            )

            # Log information about previous scans
            logger.info(f"Found {len(previous_scans_in_session)} previous scans in this session")

            # Compile accumulated session context if there are previous scans
            if previous_scans_in_session:
                context_parts = []

                for i, prev_scan in enumerate(previous_scans_in_session):
                    # Extract text from previous scan
                    prev_text = None
                    if prev_scan.analysis_result and "extracted_text" in prev_scan.analysis_result:
                        prev_text = prev_scan.analysis_result["extracted_text"]
                    elif prev_scan.input_text:
                        prev_text = prev_scan.input_text

                    if prev_text:
                        # Add scan number and text to context
                        context_parts.append(f"[Scan {i+1}]: {prev_text}")

                        # Add platform information if available
                        if prev_scan.analysis_result and "platform_identified" in prev_scan.analysis_result:
                            platform = prev_scan.analysis_result["platform_identified"]
                            if platform:
                                context_parts[-1] += f" (Platform: {platform})"

                # Join all parts into a single context string
                if context_parts:
                    accumulated_session_context = "\n---\n".join(context_parts)
                    logger.info(f"Compiled accumulated session context ({len(context_parts)} previous scans)")
                    logger.debug(f"Accumulated session context: {accumulated_session_context[:200]}...")

            # Log details of up to 3 most recent previous scans
            for prev_scan in previous_scans_in_session[-3:]:
                source = prev_scan.input_content_type

                # Extract text snippet if available
                text_snippet = None
                if prev_scan.analysis_result and "extracted_text" in prev_scan.analysis_result:
                    text_snippet = prev_scan.analysis_result["extracted_text"][:100] + "..."
                elif prev_scan.input_text:
                    text_snippet = prev_scan.input_text[:100] + "..."

                logger.info(f"Previous scan {prev_scan.id} - Source: {source}, Text: {text_snippet}")

        # Get the appropriate AI service based on user's subscription tier and expert scan flag
        # If this is an expert scan and the user is on the expert tier, use the expert service
        # Otherwise, use the service based on the user's tier
        if is_expert_scan and user_tier == "expert":
            logger.info(f"Using expert tier AI service with o4-mini for scan {scan_id}")
            ai_service = get_ai_service(user_tier="expert")
        else:
            logger.info(f"Using {user_tier} tier AI service for scan {scan_id}")
            ai_service = get_ai_service(user_tier=user_tier)

        # Run the async AI processing in the sync context
        analysis_result = asyncio.run(_process_scan_with_ai(scan, ai_service, accumulated_session_context))

        # Update scan with result and set status to "completed"
        updated_scan = crud.crud_scan.update_scan_result(
            db=db, db_scan=scan, analysis_result=analysis_result
        )



        # Clean up temporary files if this was an image scan
        if scan.input_content_type == "image_path" and scan.raw_input_payload and "file_path" in scan.raw_input_payload:
            file_path = scan.raw_input_payload["file_path"]
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.info(f"Deleted temporary file: {file_path}")
            except Exception as e:
                logger.warning(f"Failed to delete temporary file {file_path}: {str(e)}")

        logger.info(f"Completed processing of scan {scan_id}")
        return {"status": "success", "scan_id": scan_id}

    except Exception as e:
        logger.exception(f"Error processing scan {scan_id}: {str(e)}")

        # If we have a scan object, update its status to "failed"
        if 'scan' in locals() and scan:
            crud.crud_scan.update_scan_status(
                db=db, db_scan=scan, status="failed", error_message=str(e)
            )

        return {"status": "error", "scan_id": scan_id, "error": str(e)}

    finally:
        db.close()


async def _process_scan_with_ai(
    scan: models.scan.Scan,
    ai_service: Any,
    accumulated_session_context: Optional[str] = None
) -> schemas.scan.ScanResultData:
    """
    Process a scan using AI services.

    Args:
        scan: The scan record to process
        ai_service: The AI service to use
        accumulated_session_context: Optional context from previous scans in the session

    Returns:
        ScanResultData with the analysis results
    """
    # Extract content based on scan type
    multimodal_data = {}

    try:
        if scan.input_content_type == "image_path":
            # Process image from base64 data
            if scan.raw_input_payload and "base64_image" in scan.raw_input_payload:
                base64_image = scan.raw_input_payload["base64_image"]
                filename = scan.raw_input_payload.get("filename", "image.jpg")

                logger.info(f"Processing image from base64 data: {filename}")
                logger.info(f"Base64 data length: {len(base64_image)}")

                # Pass base64 data directly to AI service
                multimodal_data = await ai_service.get_multimodal_analysis(base64_image=base64_image)
                # Log the raw response for debugging
                logger.info(f"Raw multimodal_data: {multimodal_data}")
            elif scan.raw_input_payload and "file_path" in scan.raw_input_payload:
                # Fallback for old file_path method (for backward compatibility)
                image_path = scan.raw_input_payload["file_path"]

                # Fix for relative paths: if path is relative, make it absolute
                if not os.path.isabs(image_path):
                    image_path = os.path.join(os.getcwd(), image_path)

                logger.info(f"Processing image at path (legacy): {image_path}")

                # Debug: Check if file exists and list directory contents
                logger.info(f"File exists check: {os.path.exists(image_path)}")
                upload_dir = os.path.dirname(image_path)
                logger.info(f"Upload directory: {upload_dir}")
                if os.path.exists(upload_dir):
                    files_in_dir = os.listdir(upload_dir)
                    logger.info(f"Files in upload directory: {files_in_dir}")
                else:
                    logger.error(f"Upload directory does not exist: {upload_dir}")

                multimodal_data = await ai_service.get_multimodal_analysis(image_path=image_path)
                # Log the raw response for debugging
                logger.info(f"Raw multimodal_data: {multimodal_data}")
            else:
                raise ValueError("Missing base64_image or file_path in raw_input_payload for image_path scan")

        elif scan.input_content_type == "text":
            # Process text
            if scan.input_text:
                logger.info(f"Processing text input: {scan.input_text[:100]}...")
                multimodal_data = await ai_service.get_multimodal_analysis(text_input=scan.input_text)
                # Log the raw response for debugging
                logger.info(f"Raw multimodal_data: {multimodal_data}")
            else:
                raise ValueError("Missing input_text for text scan")

        elif scan.input_content_type == "url":
            # Process URL
            if scan.input_url:
                logger.info(f"Processing URL: {scan.input_url}")
                multimodal_data = await ai_service.get_multimodal_analysis(url_input=scan.input_url)
                # Log the raw response for debugging
                logger.info(f"Raw multimodal_data: {multimodal_data}")
            else:
                raise ValueError("Missing input_url for URL scan")

        else:
            raise ValueError(f"Unsupported input_content_type: {scan.input_content_type}")
    except Exception as e:
        logger.error(f"Error during multimodal analysis: {str(e)}")
        # Return a generic error result
        return schemas.scan.ScanResultData(
            risk_score=0.0,
            detected_red_flags=["Error during analysis"],
            explanation=f"An error occurred during analysis: {str(e)}",
            recommendations="Please try again or contact support if the issue persists."
        )

    try:
        # Get text for embedding
        extracted_text = multimodal_data.get("extracted_text", "")
        if not extracted_text:
            if scan.input_text:
                extracted_text = scan.input_text
            elif scan.input_url:
                extracted_text = scan.input_url
            else:
                extracted_text = "No text content available for analysis."

        # Generate embedding for the extracted text
        logger.info(f"Generating embedding for text: {extracted_text[:100]}...")
        query_embedding = await ai_service.get_text_embedding(text=extracted_text)

        # Log embedding information
        embedding_length = len(query_embedding)
        logger.info(f"Generated embedding with {embedding_length} dimensions")
        logger.info(f"First 10 dimensions of the embedding: {query_embedding[:10]}")

        # Create a database session for the RAG query
        from app.core.database import SessionLocal
        db = SessionLocal()

        try:
            # Perform final analysis with RAG
            logger.info("Performing final analysis with RAG")

            # Log if we're using session context
            if accumulated_session_context:
                logger.info(f"Including accumulated session context from {accumulated_session_context.count('---') + 1} previous scans")

            # Log user provided context
            if scan.user_provided_context:
                logger.info(f"Including user provided context: {scan.user_provided_context[:100]}...")
            else:
                logger.info("No user provided context available")

            final_analysis_result = await ai_service.perform_scam_analysis_with_rag(
                query_text=extracted_text,
                original_image_description=multimodal_data.get("image_description"),
                original_platform_identified=multimodal_data.get("platform_identified"),
                query_embedding=query_embedding,
                accumulated_session_context=accumulated_session_context,
                user_provided_context=scan.user_provided_context,
                db=db
            )

            logger.info(f"RAG analysis complete. Risk score: {final_analysis_result.risk_score}")
            logger.info(f"Detected red flags: {final_analysis_result.detected_red_flags}")
        finally:
            # Close the database session
            db.close()

        return final_analysis_result
    except Exception as e:
        logger.error(f"Error during analysis: {str(e)}")
        # Return a generic error result
        return schemas.scan.ScanResultData(
            risk_score=0.0,
            detected_red_flags=["Error during analysis"],
            explanation=f"An error occurred during analysis: {str(e)}",
            recommendations="Please try again or contact support if the issue persists."
        )


@celery_app.task
def heartbeat_task():
    """
    Simple heartbeat task to keep the Celery worker alive.
    This prevents Render from killing the worker due to inactivity.
    """
    current_time = time.time()
    logger.info(f"Heartbeat task executed at {current_time}")
    return {"status": "heartbeat", "timestamp": current_time}
