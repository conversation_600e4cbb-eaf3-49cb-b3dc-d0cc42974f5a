import uuid
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps

router = APIRouter()


@router.post(
    "/",
    response_model=schemas.scan_session.ScanSession,
    status_code=status.HTTP_201_CREATED
)
def create_scan_session(
    *,
    db: Session = Depends(deps.get_db),
    session_in: schemas.scan_session.ScanSessionCreate,
    current_user: models.user.User = Depends(deps.get_current_user)
) -> models.scan_session.ScanSession:
    """
    Create a new scan session.
    """
    return crud.crud_scan_session.create_scan_session(
        db=db, owner_id=current_user.id, title=session_in.title
    )


@router.get(
    "/{session_id}",
    response_model=schemas.scan_session.ScanSession
)
def get_scan_session(
    *,
    db: Session = Depends(deps.get_db),
    session_id: uuid.UUID,
    current_user: models.user.User = Depends(deps.get_current_user)
) -> models.scan_session.ScanSession:
    """
    Get a specific scan session by ID.
    """
    session = crud.crud_scan_session.get_scan_session(
        db=db, session_id=session_id, owner_id=current_user.id
    )
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scan session not found"
        )
    return session


@router.get(
    "/",
    response_model=List[schemas.scan_session.ScanSession]
)
def get_scan_sessions(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 10,
    current_user: models.user.User = Depends(deps.get_current_user)
) -> List[models.scan_session.ScanSession]:
    """
    Get all scan sessions for the current user.
    """
    return crud.crud_scan_session.get_scan_sessions_by_owner(
        db=db, owner_id=current_user.id, skip=skip, limit=limit
    )


@router.put(
    "/{session_id}",
    response_model=schemas.scan_session.ScanSession
)
def update_scan_session(
    *,
    db: Session = Depends(deps.get_db),
    session_id: uuid.UUID,
    session_in: schemas.scan_session.ScanSessionUpdate,
    current_user: models.user.User = Depends(deps.get_current_user)
) -> models.scan_session.ScanSession:
    """
    Update a scan session.
    """
    session = crud.crud_scan_session.get_scan_session(
        db=db, session_id=session_id, owner_id=current_user.id
    )
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scan session not found"
        )
    return crud.crud_scan_session.update_scan_session(
        db=db, db_session=session, title=session_in.title
    )
