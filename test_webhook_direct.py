import base64
import json
import requests

# Create a test notification
developer_notification = {
    "version": "1.0",
    "packageName": "com.domeai.scamdetector",
    "eventTimeMillis": "1621234567890",
    "testNotification": {
        "version": "1.0"
    }
}

# Encode the notification as base64
encoded_data = base64.b64encode(json.dumps(developer_notification).encode("utf-8")).decode("utf-8")

# Create the Pub/Sub message
pubsub_message = {
    "message": {
        "data": encoded_data,
        "messageId": "test-message-id",
        "publishTime": "2023-05-20T10:00:00.000Z"
    },
    "subscription": "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
}

# Print the request that would be sent to the webhook endpoint
print("Request that would be sent to the webhook endpoint:")
print(json.dumps(pubsub_message, indent=2))
print("\n")

# Print the decoded notification
print("Decoded notification:")
print(json.dumps(developer_notification, indent=2))
print("\n")

# Print instructions for manual testing
print("To manually test the webhook endpoint, you can use the following curl command:")
print(f"curl -X POST http://localhost:8000/api/v1/webhooks/googleplay/rtdn -H \"Content-Type: application/json\" -d '{json.dumps(pubsub_message)}'")
print("\n")

# Print the expected response
print("Expected response from the webhook endpoint:")
print(json.dumps({"status": "success", "message": "RTDN received and processed"}, indent=2))
print("\n")
