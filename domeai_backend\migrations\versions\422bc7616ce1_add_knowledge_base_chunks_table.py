"""Add knowledge_base_chunks table

Revision ID: 422bc7616ce1
Revises:
Create Date: 2025-05-11 21:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '422bc7616ce1'
down_revision = '005'
branch_labels = None
depends_on = None


def upgrade():
    # Create pgvector extension if it doesn't exist
    op.execute('CREATE EXTENSION IF NOT EXISTS vector')

    # Create knowledge_base_chunks table
    op.create_table('knowledge_base_chunks',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('embedding', postgresql.ARRAY(sa.Float()), nullable=False),
        sa.Column('source', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # Create index on id
    op.create_index(op.f('ix_knowledge_base_chunks_id'), 'knowledge_base_chunks', ['id'], unique=False)

    # Create GIN index on embedding for faster similarity search
    op.execute(
        'CREATE INDEX knowledge_base_chunks_embedding_idx ON knowledge_base_chunks USING gin (embedding);'
    )


def downgrade():
    # Drop indexes
    op.execute('DROP INDEX IF EXISTS knowledge_base_chunks_embedding_idx')
    op.drop_index(op.f('ix_knowledge_base_chunks_id'), table_name='knowledge_base_chunks')

    # Drop table
    op.drop_table('knowledge_base_chunks')

    # We don't drop the pgvector extension as it might be used by other tables
