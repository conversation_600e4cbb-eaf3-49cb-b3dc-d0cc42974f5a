from fastapi import APIRouter

# Import endpoint routers
from app.api.v1.endpoints import auth, scans, sessions, webhooks, subscriptions

api_router = APIRouter()

# Include endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(scans.router, prefix="/scans", tags=["scans"])
api_router.include_router(sessions.router, prefix="/sessions", tags=["sessions"])
api_router.include_router(webhooks.router, prefix="/webhooks", tags=["webhooks"])
api_router.include_router(subscriptions.router, prefix="/subscriptions", tags=["subscriptions"])
