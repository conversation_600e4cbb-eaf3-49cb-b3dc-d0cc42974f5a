package com.domeai.data.repository

import com.domeai.data.model.UserProfile
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for user-related operations
 */
interface UserRepository {
    /**
     * Get the user's profile
     */
    suspend fun getUserProfile(): UserProfile

    /**
     * Update the user's profile
     */
    suspend fun updateUserProfile(name: String, phone: String)

    /**
     * Update email notification settings
     */
    suspend fun updateEmailNotifications(enabled: Boolean)

    /**
     * Update push notification settings
     */
    suspend fun updatePushNotifications(enabled: Boolean)

    /**
     * Update the user's profile picture
     */
    suspend fun updateProfilePicture(uri: String)
}

/**
 * Implementation of UserRepository
 */
@Singleton
class UserRepositoryImpl @Inject constructor() : UserRepository {
    // In a real app, this would use a data source to fetch and update user data
    // For now, we'll use example data

    private var userProfile = UserProfile(
        name = "<PERSON>",
        email = "<EMAIL>",
        phone = "************",
        profilePictureUri = "",
        subscriptionType = "Premium Plan",
        subscriptionExpiryDate = "Dec 31, 2023",
        emailNotificationsEnabled = true,
        pushNotificationsEnabled = true
    )

    override suspend fun getUserProfile(): UserProfile {
        // In a real app, this would fetch the user's profile from a data source
        return userProfile
    }

    override suspend fun updateUserProfile(name: String, phone: String) {
        // In a real app, this would update the user's profile in a data source
        userProfile = userProfile.copy(
            name = name,
            phone = phone
        )
    }

    override suspend fun updateEmailNotifications(enabled: Boolean) {
        // In a real app, this would update the user's notification settings in a data source
        userProfile = userProfile.copy(
            emailNotificationsEnabled = enabled
        )
    }

    override suspend fun updatePushNotifications(enabled: Boolean) {
        // In a real app, this would update the user's notification settings in a data source
        userProfile = userProfile.copy(
            pushNotificationsEnabled = enabled
        )
    }

    override suspend fun updateProfilePicture(uri: String) {
        // In a real app, this would upload the image to a server and update the user's profile
        userProfile = userProfile.copy(
            profilePictureUri = uri
        )
    }
}
