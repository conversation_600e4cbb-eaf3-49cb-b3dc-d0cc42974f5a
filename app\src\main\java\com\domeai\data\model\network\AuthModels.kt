package com.domeai.data.model.network

import com.google.gson.annotations.SerializedName
import java.util.Date

/**
 * Request model for user registration
 */
data class RegisterRequest(
    val email: String,
    val password: String
)

/**
 * Request model for user login
 * 
 * Note: The backend expects form data with username and password fields,
 * but we'll use a JSON request and handle the conversion in the interceptor
 */
data class LoginRequest(
    @SerializedName("username") val email: String,
    val password: String
)

/**
 * Response model for token
 */
data class TokenResponse(
    @SerializedName("access_token") val accessToken: String,
    @SerializedName("token_type") val tokenType: String
)

/**
 * Response model for user
 */
data class UserResponse(
    val id: Int,
    val email: String,
    @SerializedName("is_active") val isActive: Boolean,
    @SerializedName("created_at") val createdAt: String,
    @SerializedName("updated_at") val updatedAt: String,
    @SerializedName("subscription_tier") val subscriptionTier: String? = null,
    @SerializedName("monthly_scan_allowance") val monthlyScanAllowance: Int? = null,
    @SerializedName("scans_this_month") val scansThisMonth: Int? = null,
    @SerializedName("expert_scan_allowance") val expertScanAllowance: Int? = null,
    @SerializedName("expert_scans_this_month") val expertScansThisMonth: Int? = null,
    @SerializedName("subscription_expiry_date") val subscriptionExpiryDate: String? = null
)
