from fastapi import FastAPI, Request

app = FastAPI()

@app.get("/")
def root():
    return {"message": "Simple RTDN FastAPI app is running!"}

@app.post("/api/v1/webhooks/googleplay/rtdn")
async def google_play_rtdn(request: Request):
    data = await request.json()
    print(f"Received RTDN: {data}")
    return {"status": "success", "message": "RTDN received and processed"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
