#!/usr/bin/env python
"""
Simple script to test the vector similarity search functionality.
"""

import asyncio
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import from app
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.database import SessionLocal
from app.services.ai_services import OpenAIModelService
from app.core.config import settings
from app.crud import crud_kb

async def main():
    # Create a database session
    db = SessionLocal()

    try:
        # Initialize the OpenAI service
        logger.info("Initializing OpenAI service...")
        ai_service = OpenAIModelService(api_key=settings.OPENAI_API_KEY)

        # Get the knowledge base chunk with ID 5
        chunk_id = 5
        logger.info(f"Getting chunk with ID {chunk_id}...")
        chunk = crud_kb.get_kb_chunk(db, chunk_id)
        if not chunk:
            logger.error(f"Chunk with ID {chunk_id} not found")
            return

        logger.info(f"Testing with chunk ID {chunk_id}: {chunk.source}")
        logger.info(f"Content: {chunk.content[:150]}...")

        # Check if the chunk has an embedding
        if not chunk.embedding or len(chunk.embedding) == 0:
            logger.error(f"Chunk with ID {chunk_id} has no embedding")
            return

        logger.info(f"Chunk has embedding with {len(chunk.embedding)} dimensions")

        # Use the chunk's existing embedding as the query embedding
        logger.info("Using the chunk's existing embedding as the query embedding...")
        query_embedding = chunk.embedding

        # Find similar chunks
        logger.info("Finding similar chunks...")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=query_embedding,
            top_k=3
        )

        # Print the results
        logger.info(f"Found {len(similar_chunks)} similar chunks")
        for i, similar_chunk in enumerate(similar_chunks):
            logger.info(f"Similar chunk {i+1}: ID {similar_chunk.id}, Source: {similar_chunk.source}")
            logger.info(f"Similarity score: {getattr(similar_chunk, 'similarity_score', 'N/A')}")
            logger.info(f"Content: {similar_chunk.content[:150]}...")

    except Exception as e:
        logger.error(f"Error: {str(e)}", exc_info=True)

    finally:
        db.close()

if __name__ == '__main__':
    asyncio.run(main())
