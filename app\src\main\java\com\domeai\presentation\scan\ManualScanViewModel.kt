package com.domeai.presentation.scan

import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.domeai.data.model.ScanResult
import com.domeai.data.processor.ScanResultProcessor
import com.domeai.data.repository.ScanRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * UI state for the manual scan screen
 */
data class ManualScanUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val scanInputType: ScanInputType = ScanInputType.TEXT,
    val textInput: String = "",
    val imageUri: Uri? = null
)

/**
 * Events emitted by the manual scan view model
 */
sealed class ManualScanEvent {
    data class NavigateToScanResult(val scanId: String) : ManualScanEvent()
    data class ShowError(val message: String) : ManualScanEvent()
}

/**
 * ViewModel for the manual scan screen
 */
@HiltViewModel
class ManualScanViewModel @Inject constructor(
    private val scanResultProcessor: ScanResultProcessor,
    private val scanRepository: ScanRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ManualScanUiState())
    val uiState: StateFlow<ManualScanUiState> = _uiState.asStateFlow()
    
    private val _events = MutableSharedFlow<ManualScanEvent>()
    val events: SharedFlow<ManualScanEvent> = _events.asSharedFlow()
    
    /**
     * Set the scan input type
     */
    fun setScanInputType(type: ScanInputType) {
        _uiState.value = _uiState.value.copy(scanInputType = type)
    }
    
    /**
     * Set the text input
     */
    fun setTextInput(text: String) {
        _uiState.value = _uiState.value.copy(textInput = text)
    }
    
    /**
     * Set the image URI
     */
    fun setImageUri(uri: Uri?) {
        _uiState.value = _uiState.value.copy(imageUri = uri)
    }
    
    /**
     * Perform a scan based on the current input
     */
    fun performScan() {
        val currentState = _uiState.value
        
        // Validate input
        when (currentState.scanInputType) {
            ScanInputType.TEXT, ScanInputType.URL -> {
                if (currentState.textInput.isBlank()) {
                    viewModelScope.launch {
                        _events.emit(ManualScanEvent.ShowError("Please enter some text to scan"))
                    }
                    return
                }
            }
            ScanInputType.IMAGE -> {
                if (currentState.imageUri == null) {
                    viewModelScope.launch {
                        _events.emit(ManualScanEvent.ShowError("Please select an image to scan"))
                    }
                    return
                }
            }
        }
        
        // Set loading state
        _uiState.value = _uiState.value.copy(isLoading = true, error = null)
        
        viewModelScope.launch {
            try {
                // Process the scan based on the input type
                val scanResult = when (currentState.scanInputType) {
                    ScanInputType.TEXT -> scanResultProcessor.processTextInput(currentState.textInput)
                    ScanInputType.URL -> scanResultProcessor.processUrlInput(currentState.textInput)
                    ScanInputType.IMAGE -> scanResultProcessor.processImageInput(currentState.imageUri!!)
                }
                
                // Save the scan result
                scanRepository.addScan(scanResult)
                
                // Reset loading state
                _uiState.value = _uiState.value.copy(isLoading = false)
                
                // Navigate to the scan result
                _events.emit(ManualScanEvent.NavigateToScanResult(scanResult.id))
            } catch (e: Exception) {
                // Handle error
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "An unknown error occurred"
                )
                
                _events.emit(ManualScanEvent.ShowError(e.message ?: "An unknown error occurred"))
            }
        }
    }
    
    /**
     * Clear the current input
     */
    fun clearInput() {
        when (_uiState.value.scanInputType) {
            ScanInputType.TEXT, ScanInputType.URL -> setTextInput("")
            ScanInputType.IMAGE -> setImageUri(null)
        }
    }
}
