import requests
import json

def test_simple_upload():
    """Test uploading just 2 chunks to see if the basic functionality works"""
    
    # Simple test chunks
    test_chunks = [
        {
            "content": "Test scam detection knowledge: Cashier check overpayment scams involve receiving a check for more than the agreed amount.",
            "source": "test_chunk_1"
        },
        {
            "content": "Test scam detection knowledge: Romance scams involve building emotional relationships before requesting money.",
            "source": "test_chunk_2"
        }
    ]
    
    url = "https://domeai-backend.onrender.com/admin/upload-kb-chunks"
    payload = {"chunks": test_chunks}
    
    print(f"🧪 Testing upload with {len(test_chunks)} simple chunks...")
    
    try:
        response = requests.post(url, json=payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Test upload completed!")
            print(f"Status: {result.get('status')}")
            print(f"Successful: {result.get('successful')}")
            print(f"Failed: {result.get('failed')}")
            print(f"Final count: {result.get('final_count_in_db')}")
            
            if result.get('details'):
                for detail in result.get('details', []):
                    print(f"   {detail}")
                    
            return result.get('successful', 0) > 0
        else:
            print(f"❌ Test failed: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Test request failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Simple Upload Test")
    print("=" * 30)
    
    success = test_simple_upload()
    
    if success:
        print("\n✅ Basic upload works! Ready for full 57 chunks.")
    else:
        print("\n❌ Basic upload failed. Need to fix the issue first.")
