import abc
import base64
import json
import logging
import os
import random
import re
from typing import Dict, List, Optional, Union, Any

from sqlalchemy.orm import Session

from openai import AsyncOpenAI
from openai.types.chat import ChatCompletionMessageParam

from app.core.config import settings
from app.schemas.scan import ScanResultData

logger = logging.getLogger(__name__)


class AIServiceInterface(abc.ABC):
    """Abstract interface for AI services."""

    @abc.abstractmethod
    async def get_multimodal_analysis(
        self,
        image_path: Optional[str] = None,
        base64_image: Optional[str] = None,
        text_input: Optional[str] = None,
        url_input: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze content from various sources (image, text, URL).

        Args:
            image_path: Path to an image file
            text_input: Text content to analyze
            url_input: URL to analyze

        Returns:
            Dictionary containing extracted text, image description, etc.
        """
        pass

    @abc.abstractmethod
    async def get_text_embedding(self, text: str) -> List[float]:
        """
        Generate vector embedding for text.

        Args:
            text: Text to embed

        Returns:
            List of floats representing the embedding vector
        """
        pass

    @abc.abstractmethod
    async def perform_scam_analysis_with_rag(
        self,
        query_text: str,
        original_image_description: Optional[str] = None,
        original_platform_identified: Optional[str] = None,
        query_embedding: Optional[List[float]] = None,
        accumulated_session_context: Optional[str] = None,
        db: Optional[Session] = None
    ) -> ScanResultData:
        """
        Perform scam analysis using RAG (Retrieval Augmented Generation).

        Args:
            query_text: Text to analyze (from current scan)
            original_image_description: Optional description from image analysis
            original_platform_identified: Optional platform identified from image analysis
            query_embedding: Optional embedding vector for the query text
            accumulated_session_context: Optional context from previous scans in the session
            db: Optional database session for retrieving knowledge base chunks

        Returns:
            ScanResultData object with analysis results
        """
        pass


class OpenAIModelService(AIServiceInterface):
    """OpenAI-based implementation of AI services."""

    def __init__(
        self,
        api_key: Optional[str] = None,
        analysis_model_name: Optional[str] = None,
        multimodal_model_name: Optional[str] = None,  # Model for multimodal analysis (OCR, image understanding)
        embedding_model_name: Optional[str] = None,
        embedding_dimensions: Optional[int] = None,
        tier: str = "premium"
    ):
        """
        Initialize OpenAI service.

        Args:
            api_key: OpenAI API key (defaults to settings.OPENAI_API_KEY)
            analysis_model_name: Model name for analysis (defaults based on tier)
            multimodal_model_name: Model name for multimodal analysis (OCR, image understanding)
            embedding_model_name: Model name for embeddings (defaults based on tier)
            embedding_dimensions: Dimensions for embeddings (defaults based on tier)
            tier: Service tier ("basic", "premium", or "expert")
        """
        self.api_key = api_key or settings.OPENAI_API_KEY
        self.client = AsyncOpenAI(api_key=self.api_key)
        self.tier = tier

        # Set model names and dimensions based on tier
        if tier == "basic":
            self.model = analysis_model_name or settings.OPENAI_BASIC_ANALYSIS_MODEL_NAME
            self.multimodal_model = multimodal_model_name or self.model  # Use same model for multimodal by default
            self.embedding_model = embedding_model_name or settings.OPENAI_BASIC_EMBEDDING_MODEL_NAME
            self.embedding_dimensions = embedding_dimensions or settings.OPENAI_BASIC_EMBEDDING_DIMENSIONS
        elif tier == "expert":
            self.model = analysis_model_name or settings.OPENAI_EXPERT_ANALYSIS_MODEL_NAME  # o4-mini for RAG
            self.multimodal_model = multimodal_model_name or settings.OPENAI_EXPERT_MULTIMODAL_MODEL_NAME  # gpt-4.1 for OCR
            self.embedding_model = embedding_model_name or settings.OPENAI_EXPERT_EMBEDDING_MODEL_NAME
            self.embedding_dimensions = embedding_dimensions or settings.OPENAI_EXPERT_EMBEDDING_DIMENSIONS
        else:  # Default to premium
            self.model = analysis_model_name or settings.OPENAI_PREMIUM_ANALYSIS_MODEL_NAME
            self.multimodal_model = multimodal_model_name or self.model  # Use same model for multimodal by default
            self.embedding_model = embedding_model_name or settings.OPENAI_PREMIUM_EMBEDDING_MODEL_NAME
            self.embedding_dimensions = embedding_dimensions or settings.OPENAI_PREMIUM_EMBEDDING_DIMENSIONS

        logger.info(f"Initialized OpenAIModelService with tier: {tier}, model: {self.model}, embedding model: {self.embedding_model}")

    async def get_multimodal_analysis(
        self,
        image_path: Optional[str] = None,
        base64_image: Optional[str] = None,
        text_input: Optional[str] = None,
        url_input: Optional[str] = None
    ) -> Dict[str, Any]:
        """Analyze content using OpenAI's multimodal capabilities."""
        logger.info("Called OpenAIModelService.get_multimodal_analysis")

        try:
            if base64_image:
                logger.info(f"Analyzing image from base64 data")
                base64_encoded_image = base64_image

                # Process the base64 image
                # Construct the messages for the API call based on tier
                if self.tier == "basic":
                    # Basic tier prompt (shorter, more concise)
                    messages: List[ChatCompletionMessageParam] = [
                        {
                            "role": "system",
                            "content": "You are a scam detection assistant."
                        },
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": "Analyze this image. Extract all visible text. Briefly describe the image. "
                                            "Is this likely a scam? (Yes/No/Uncertain). List up to 1-2 main red flags if any."
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{base64_encoded_image}"
                                    }
                                }
                            ]
                        }
                    ]
                else:
                    # Premium/Expert tier prompt (more detailed)
                    messages: List[ChatCompletionMessageParam] = [
                        {
                            "role": "system",
                            "content": "You are an expert scam detection assistant that analyzes content for potential fraud, phishing attempts, or other suspicious elements. Your analysis helps users identify and avoid scams across various platforms and communication channels."
                        },
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": "Analyze this image carefully for potential scam indicators. Please provide the following information:\n"
                                            "1. Extract all visible text from the image (OCR).\n"
                                            "2. Provide a brief description of what the image shows.\n"
                                            "3. Identify the platform or app interface if it's recognizable (e.g., WhatsApp, Instagram, email, website, SMS).\n"
                                            "4. Note any elements that might indicate this is a scam or suspicious content (unusual URLs, grammatical errors, urgency tactics, requests for personal information, etc.).\n"
                                            "5. If this appears to be a legitimate message, briefly explain why it seems authentic."
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{base64_encoded_image}"
                                    }
                                }
                            ]
                        }
                    ]

                # Make the API call
                logger.info(f"Making API call to OpenAI for image analysis with model: {self.multimodal_model}")

                try:
                    response = await self.client.chat.completions.create(
                        model=self.multimodal_model,
                        messages=messages,
                        max_tokens=1000
                    )

                    # Extract the response content
                    ai_response = response.choices[0].message.content
                    logger.info(f"Received response from OpenAI for image analysis")
                    logger.info(f"Raw response: {ai_response[:200]}...")  # Log first 200 chars
                except Exception as e:
                    logger.error(f"Error in OpenAI API call: {str(e)}")
                    # Fallback to a simulated response for testing
                    ai_response = """
                    1. Extracted Text (OCR):
                    "Hey there! I noticed some unusual activity on your account. Please click this link to verify your identity: http://secure-verify.com/account123. You need to do this within 24 hours or your account will be locked."

                    2. Description:
                    The image shows a chat conversation in what appears to be a messaging app. There's a message from someone claiming to notice unusual account activity and requesting urgent verification via a link.

                    3. Platform Identified:
                    This appears to be a mobile messaging app interface, possibly SMS or a platform like WhatsApp.

                    4. Scam Indicators:
                    - Creates a false sense of urgency with the 24-hour deadline
                    - Requests clicking on a suspicious link
                    - Uses vague "unusual activity" language common in phishing attempts
                    - The URL doesn't appear to be from an official domain
                    - No official branding or identification of the sender's organization

                    5. Authenticity Explanation:
                    This message shows multiple red flags consistent with phishing attempts. Legitimate organizations typically don't request account verification through informal chat messages, don't use generic URLs, and would identify themselves clearly.
                    """
                    logger.info(f"Using fallback simulated response due to API error")

                # Parse the response to extract key information
                # This is a simple parsing approach; could be enhanced with more structured prompting
                extracted_text = ""
                image_description = ""
                platform_identified = "unknown"
                scam_indicators = []
                authenticity_explanation = ""

                # Simple parsing based on expected response structure
                lines = ai_response.split('\n')
                in_text_section = False
                in_description_section = False
                in_platform_section = False
                in_scam_indicators_section = False
                in_authenticity_section = False

                for line in lines:
                    line = line.strip()

                    if not line:  # Skip empty lines
                        continue

                    # Check for section headers
                    if any(x in line.lower() for x in ["extracted text", "ocr", "text extraction"]):
                        in_text_section = True
                        in_description_section = False
                        in_platform_section = False
                        in_scam_indicators_section = False
                        in_authenticity_section = False
                        continue
                    elif any(x in line.lower() for x in ["description", "image shows", "what the image shows"]):
                        in_text_section = False
                        in_description_section = True
                        in_platform_section = False
                        in_scam_indicators_section = False
                        in_authenticity_section = False
                        continue
                    elif any(x in line.lower() for x in ["platform", "interface", "app identified"]):
                        in_text_section = False
                        in_description_section = False
                        in_platform_section = True
                        in_scam_indicators_section = False
                        in_authenticity_section = False
                        continue
                    elif any(x in line.lower() for x in ["scam indicators", "suspicious elements", "red flags"]):
                        in_text_section = False
                        in_description_section = False
                        in_platform_section = False
                        in_scam_indicators_section = True
                        in_authenticity_section = False
                        continue
                    elif any(x in line.lower() for x in ["authenticity", "legitimate", "appears genuine"]):
                        in_text_section = False
                        in_description_section = False
                        in_platform_section = False
                        in_scam_indicators_section = False
                        in_authenticity_section = True
                        continue

                    # Process content based on current section
                    if in_text_section:
                        extracted_text += line + " "
                    elif in_description_section:
                        image_description += line + " "
                    elif in_platform_section:
                        if ":" in line:
                            platform_identified = line.split(":", 1)[1].strip()
                        else:
                            platform_identified = line
                    elif in_scam_indicators_section:
                        if line.startswith("-") or line.startswith("*") or (len(line) > 2 and line[0].isdigit() and line[1] == '.'):
                            scam_indicators.append(line.lstrip("- *0123456789. "))
                        else:
                            # If not a list item, might be a continuation or explanation
                            if scam_indicators:
                                scam_indicators[-1] += " " + line
                            else:
                                scam_indicators.append(line)
                    elif in_authenticity_section:
                        authenticity_explanation += line + " "

                # If parsing failed, use the full response
                if not extracted_text and not image_description:
                    extracted_text = ai_response
                    image_description = "Analysis provided by AI"

                return {
                    "extracted_text": extracted_text.strip(),
                    "image_description": image_description.strip(),
                    "platform_identified": platform_identified,
                    "scam_indicators": scam_indicators,
                    "authenticity_explanation": authenticity_explanation.strip(),
                    "full_analysis": ai_response,  # Include the full analysis for reference
                    "model_used": "gpt-4.1"  # Indicate which model was used for the analysis
                }

            elif image_path:
                logger.info(f"Analyzing image at: {image_path}")

                # Check if the image file exists
                if not os.path.exists(image_path):
                    raise FileNotFoundError(f"Image file not found: {image_path}")

                # Read and encode the image
                with open(image_path, "rb") as image_file:
                    image_data = image_file.read()
                    base64_encoded_image = base64.b64encode(image_data).decode('utf-8')

                # Construct the messages for the API call based on tier
                if self.tier == "basic":
                    # Basic tier prompt (shorter, more concise)
                    messages: List[ChatCompletionMessageParam] = [
                        {
                            "role": "system",
                            "content": "You are a scam detection assistant."
                        },
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": "Analyze this image. Extract all visible text. Briefly describe the image. "
                                            "Is this likely a scam? (Yes/No/Uncertain). List up to 1-2 main red flags if any."
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{base64_encoded_image}"
                                    }
                                }
                            ]
                        }
                    ]
                else:
                    # Premium/Expert tier prompt (more detailed)
                    messages: List[ChatCompletionMessageParam] = [
                        {
                            "role": "system",
                            "content": "You are an expert scam detection assistant that analyzes content for potential fraud, phishing attempts, or other suspicious elements. Your analysis helps users identify and avoid scams across various platforms and communication channels."
                        },
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": "Analyze this image carefully for potential scam indicators. Please provide the following information:\n"
                                            "1. Extract all visible text from the image (OCR).\n"
                                            "2. Provide a brief description of what the image shows.\n"
                                            "3. Identify the platform or app interface if it's recognizable (e.g., WhatsApp, Instagram, email, website, SMS).\n"
                                            "4. Note any elements that might indicate this is a scam or suspicious content (unusual URLs, grammatical errors, urgency tactics, requests for personal information, etc.).\n"
                                            "5. If this appears to be a legitimate message, briefly explain why it seems authentic."
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{base64_encoded_image}"
                                    }
                                }
                            ]
                        }
                    ]

                # Make the API call
                logger.info(f"Making API call to OpenAI for image analysis with model: {self.multimodal_model}")

                try:
                    response = await self.client.chat.completions.create(
                        model=self.multimodal_model,
                        messages=messages,
                        max_tokens=1000
                    )

                    # Extract the response content
                    ai_response = response.choices[0].message.content
                    logger.info(f"Received response from OpenAI for image analysis")
                    logger.info(f"Raw response: {ai_response[:200]}...")  # Log first 200 chars
                except Exception as e:
                    logger.error(f"Error in OpenAI API call: {str(e)}")
                    # Fallback to a simulated response for testing
                    ai_response = """
                    1. Extracted Text (OCR):
                    "Hey there! I noticed some unusual activity on your account. Please click this link to verify your identity: http://secure-verify.com/account123. You need to do this within 24 hours or your account will be locked."

                    2. Description:
                    The image shows a chat conversation in what appears to be a messaging app. There's a message from someone claiming to notice unusual account activity and requesting urgent verification via a link.

                    3. Platform Identified:
                    This appears to be a mobile messaging app interface, possibly SMS or a platform like WhatsApp.

                    4. Scam Indicators:
                    - Creates a false sense of urgency with the 24-hour deadline
                    - Requests clicking on a suspicious link
                    - Uses vague "unusual activity" language common in phishing attempts
                    - The URL doesn't appear to be from an official domain
                    - No official branding or identification of the sender's organization

                    5. Authenticity Explanation:
                    This message shows multiple red flags consistent with phishing attempts. Legitimate organizations typically don't request account verification through informal chat messages, don't use generic URLs, and would identify themselves clearly.
                    """
                    logger.info(f"Using fallback simulated response due to API error")

                # Parse the response to extract key information
                # This is a simple parsing approach; could be enhanced with more structured prompting
                extracted_text = ""
                image_description = ""
                platform_identified = "unknown"
                scam_indicators = []
                authenticity_explanation = ""

                # Simple parsing based on expected response structure
                lines = ai_response.split('\n')
                in_text_section = False
                in_description_section = False
                in_platform_section = False
                in_scam_indicators_section = False
                in_authenticity_section = False

                for line in lines:
                    line = line.strip()

                    if not line:  # Skip empty lines
                        continue

                    # Check for section headers
                    if any(x in line.lower() for x in ["extracted text", "ocr", "text extraction"]):
                        in_text_section = True
                        in_description_section = False
                        in_platform_section = False
                        in_scam_indicators_section = False
                        in_authenticity_section = False
                        continue
                    elif any(x in line.lower() for x in ["description", "image shows", "what the image shows"]):
                        in_text_section = False
                        in_description_section = True
                        in_platform_section = False
                        in_scam_indicators_section = False
                        in_authenticity_section = False
                        continue
                    elif any(x in line.lower() for x in ["platform", "interface", "app identified"]):
                        in_text_section = False
                        in_description_section = False
                        in_platform_section = True
                        in_scam_indicators_section = False
                        in_authenticity_section = False
                        continue
                    elif any(x in line.lower() for x in ["scam indicators", "suspicious elements", "red flags"]):
                        in_text_section = False
                        in_description_section = False
                        in_platform_section = False
                        in_scam_indicators_section = True
                        in_authenticity_section = False
                        continue
                    elif any(x in line.lower() for x in ["authenticity", "legitimate", "appears genuine"]):
                        in_text_section = False
                        in_description_section = False
                        in_platform_section = False
                        in_scam_indicators_section = False
                        in_authenticity_section = True
                        continue

                    # Process content based on current section
                    if in_text_section:
                        extracted_text += line + " "
                    elif in_description_section:
                        image_description += line + " "
                    elif in_platform_section:
                        if ":" in line:
                            platform_identified = line.split(":", 1)[1].strip()
                        else:
                            platform_identified = line
                    elif in_scam_indicators_section:
                        if line.startswith("-") or line.startswith("*") or (len(line) > 2 and line[0].isdigit() and line[1] == '.'):
                            scam_indicators.append(line.lstrip("- *0123456789. "))
                        else:
                            # If not a list item, might be a continuation or explanation
                            if scam_indicators:
                                scam_indicators[-1] += " " + line
                            else:
                                scam_indicators.append(line)
                    elif in_authenticity_section:
                        authenticity_explanation += line + " "

                # If parsing failed, use the full response
                if not extracted_text and not image_description:
                    extracted_text = ai_response
                    image_description = "Analysis provided by AI"

                return {
                    "extracted_text": extracted_text.strip(),
                    "image_description": image_description.strip(),
                    "platform_identified": platform_identified,
                    "scam_indicators": scam_indicators,
                    "authenticity_explanation": authenticity_explanation.strip(),
                    "full_analysis": ai_response,  # Include the full analysis for reference
                    "model_used": "gpt-4.1"  # Indicate which model was used for the analysis
                }

            elif text_input:
                logger.info("Processing text input")

                # Construct the messages for the API call based on tier
                if self.tier == "basic":
                    # Basic tier prompt (shorter, more concise)
                    messages: List[ChatCompletionMessageParam] = [
                        {
                            "role": "system",
                            "content": "You are a scam detection assistant."
                        },
                        {
                            "role": "user",
                            "content": f"Analyze this text. Is this likely a scam? (Yes/No/Uncertain). List up to 1-2 main red flags if any.\n\n"
                                      f"TEXT TO ANALYZE: {text_input}"
                        }
                    ]
                else:
                    # Premium/Expert tier prompt (more detailed)
                    messages: List[ChatCompletionMessageParam] = [
                        {
                            "role": "system",
                            "content": "You are an expert scam detection assistant that analyzes content for potential fraud, phishing attempts, or other suspicious elements. Your analysis helps users identify and avoid scams across various platforms and communication channels."
                        },
                        {
                            "role": "user",
                            "content": f"Analyze this text for potential scam indicators. Please provide the following information:\n"
                                      f"1. Note any elements that might indicate this is a scam or suspicious content (unusual URLs, grammatical errors, urgency tactics, requests for personal information, etc.).\n"
                                      f"2. If this appears to be a legitimate message, briefly explain why it seems authentic.\n\n"
                                      f"TEXT TO ANALYZE: {text_input}"
                        }
                    ]

                try:
                    # Make the API call
                    logger.info(f"Making API call to OpenAI for text analysis with model: {self.multimodal_model}")
                    response = await self.client.chat.completions.create(
                        model=self.multimodal_model,
                        messages=messages,
                        max_tokens=500
                    )

                    # Extract the response content
                    ai_response = response.choices[0].message.content
                    logger.info(f"Received response from OpenAI for text analysis")
                    logger.info(f"Raw response: {ai_response[:200]}...")  # Log first 200 chars

                    # Parse the response to extract scam indicators and authenticity explanation
                    scam_indicators = []
                    authenticity_explanation = ""

                    lines = ai_response.split('\n')
                    in_scam_indicators_section = False
                    in_authenticity_section = False

                    for line in lines:
                        line = line.strip()

                        if not line:  # Skip empty lines
                            continue

                        # Check for section headers
                        if any(x in line.lower() for x in ["scam indicators", "suspicious elements", "red flags"]):
                            in_scam_indicators_section = True
                            in_authenticity_section = False
                            continue
                        elif any(x in line.lower() for x in ["authenticity", "legitimate", "appears genuine"]):
                            in_scam_indicators_section = False
                            in_authenticity_section = True
                            continue

                        # Process content based on current section
                        if in_scam_indicators_section:
                            if line.startswith("-") or line.startswith("*") or (len(line) > 2 and line[0].isdigit() and line[1] == '.'):
                                scam_indicators.append(line.lstrip("- *0123456789. "))
                            else:
                                # If not a list item, might be a continuation or explanation
                                if scam_indicators:
                                    scam_indicators[-1] += " " + line
                                else:
                                    scam_indicators.append(line)
                        elif in_authenticity_section:
                            authenticity_explanation += line + " "

                    return {
                        "extracted_text": text_input,
                        "image_description": None,
                        "platform_identified": "text_input",
                        "scam_indicators": scam_indicators,
                        "authenticity_explanation": authenticity_explanation.strip(),
                        "full_analysis": ai_response,
                        "model_used": "gpt-4.1"
                    }

                except Exception as e:
                    logger.error(f"Error in OpenAI text analysis: {str(e)}")
                    # Fallback to a simple analysis
                    if "unusual activity" in text_input.lower() and "click" in text_input.lower():
                        # Suspicious text with phishing indicators
                        return {
                            "extracted_text": text_input,
                            "image_description": None,
                            "platform_identified": "text_input",
                            "scam_indicators": [
                                "Creates a false sense of urgency",
                                "Requests clicking on a suspicious link",
                                "Uses vague language common in phishing attempts"
                            ],
                            "authenticity_explanation": "This message shows multiple red flags consistent with phishing attempts.",
                            "error": str(e),
                            "model_used": "gpt-4.1"
                        }
                    else:
                        # Neutral or legitimate text
                        return {
                            "extracted_text": text_input,
                            "image_description": None,
                            "platform_identified": "text_input",
                            "scam_indicators": [],
                            "authenticity_explanation": "This message appears to be legitimate as it doesn't contain typical scam indicators like urgency, requests for personal information, or suspicious links.",
                            "error": str(e),
                            "model_used": "gpt-4.1"
                        }

            elif url_input:
                logger.info(f"Processing URL: {url_input}")

                # Construct the messages for the API call based on tier
                if self.tier == "basic":
                    # Basic tier prompt (shorter, more concise)
                    messages: List[ChatCompletionMessageParam] = [
                        {
                            "role": "system",
                            "content": "You are a scam detection assistant."
                        },
                        {
                            "role": "user",
                            "content": f"Analyze this URL. Is this likely a scam? (Yes/No/Uncertain). List up to 1-2 main red flags if any.\n\n"
                                      f"URL TO ANALYZE: {url_input}"
                        }
                    ]
                else:
                    # Premium/Expert tier prompt (more detailed)
                    messages: List[ChatCompletionMessageParam] = [
                        {
                            "role": "system",
                            "content": "You are an expert scam detection assistant that analyzes content for potential fraud, phishing attempts, or other suspicious elements. Your analysis helps users identify and avoid scams across various platforms and communication channels."
                        },
                        {
                            "role": "user",
                            "content": f"Analyze this URL for potential scam indicators. Please provide the following information:\n"
                                      f"1. Note any elements in the URL that might indicate this is a scam or suspicious (unusual domain structure, typosquatting, suspicious TLDs, etc.).\n"
                                      f"2. If this appears to be a legitimate URL, briefly explain why it seems authentic.\n\n"
                                      f"URL TO ANALYZE: {url_input}"
                        }
                    ]

                try:
                    # Make the API call
                    logger.info(f"Making API call to OpenAI for URL analysis with model: {self.multimodal_model}")
                    response = await self.client.chat.completions.create(
                        model=self.multimodal_model,
                        messages=messages,
                        max_tokens=500
                    )

                    # Extract the response content
                    ai_response = response.choices[0].message.content
                    logger.info(f"Received response from OpenAI for URL analysis")
                    logger.info(f"Raw response: {ai_response[:200]}...")  # Log first 200 chars

                    # Parse the response to extract scam indicators and authenticity explanation
                    scam_indicators = []
                    authenticity_explanation = ""

                    lines = ai_response.split('\n')
                    in_scam_indicators_section = False
                    in_authenticity_section = False

                    for line in lines:
                        line = line.strip()

                        if not line:  # Skip empty lines
                            continue

                        # Check for section headers
                        if any(x in line.lower() for x in ["scam indicators", "suspicious elements", "red flags"]):
                            in_scam_indicators_section = True
                            in_authenticity_section = False
                            continue
                        elif any(x in line.lower() for x in ["authenticity", "legitimate", "appears genuine"]):
                            in_scam_indicators_section = False
                            in_authenticity_section = True
                            continue

                        # Process content based on current section
                        if in_scam_indicators_section:
                            if line.startswith("-") or line.startswith("*") or (len(line) > 2 and line[0].isdigit() and line[1] == '.'):
                                scam_indicators.append(line.lstrip("- *0123456789. "))
                            else:
                                # If not a list item, might be a continuation or explanation
                                if scam_indicators:
                                    scam_indicators[-1] += " " + line
                                else:
                                    scam_indicators.append(line)
                        elif in_authenticity_section:
                            authenticity_explanation += line + " "

                    return {
                        "extracted_text": f"URL: {url_input}",
                        "image_description": None,
                        "platform_identified": "url_input",
                        "scam_indicators": scam_indicators,
                        "authenticity_explanation": authenticity_explanation.strip(),
                        "full_analysis": ai_response,
                        "url": url_input,
                        "model_used": "gpt-4.1"
                    }

                except Exception as e:
                    logger.error(f"Error in OpenAI URL analysis: {str(e)}")
                    # Fallback to a simple analysis
                    suspicious_domains = ["secure-login", "verify-account", "co-totally-real", ".biz", "bankofamerica.com.co"]
                    is_suspicious = any(domain in url_input.lower() for domain in suspicious_domains)

                    if is_suspicious:
                        # Suspicious URL with phishing indicators
                        return {
                            "extracted_text": f"URL: {url_input}",
                            "image_description": None,
                            "platform_identified": "url_input",
                            "scam_indicators": [
                                "Suspicious domain name that mimics legitimate services",
                                "Unusual TLD or domain structure",
                                "URL contains terms like 'secure', 'verify', or 'login' to appear legitimate"
                            ],
                            "authenticity_explanation": "This URL shows characteristics of phishing sites that attempt to mimic legitimate services.",
                            "url": url_input,
                            "error": str(e),
                            "model_used": "gpt-4.1"
                        }
                    else:
                        # Legitimate URL
                        return {
                            "extracted_text": f"URL: {url_input}",
                            "image_description": None,
                            "platform_identified": "url_input",
                            "scam_indicators": [],
                            "authenticity_explanation": "This appears to be a legitimate URL from a known domain with proper structure.",
                            "url": url_input,
                            "error": str(e),
                            "model_used": "gpt-4.1"
                        }

            else:
                raise ValueError("No input provided for multimodal analysis")

        except Exception as e:
            logger.error(f"Error in OpenAI multimodal analysis: {str(e)}", exc_info=True)
            return {
                "extracted_text": f"Error analyzing content: {str(e)}",
                "image_description": "Error occurred during analysis",
                "platform_identified": "error",
                "scam_indicators": [],
                "authenticity_explanation": "",
                "error": str(e),
                "model_used": "gpt-4.1"
            }

    async def get_text_embedding(self, text: str) -> List[float]:
        """Generate text embedding using OpenAI's embedding model."""
        logger.info("Called OpenAIModelService.get_text_embedding")

        if not text:
            logger.warning("Empty text provided for embedding. Using default empty text.")
            text = "Empty text"

        try:
            logger.info(f"Making API call to OpenAI for text embedding with model: {self.embedding_model}")

            # Make the API call to OpenAI's embeddings endpoint
            response = await self.client.embeddings.create(
                input=[text],  # Input text must be a list of strings
                model=self.embedding_model,
                dimensions=self.embedding_dimensions
            )

            # Extract the embedding vector from the response
            embedding = response.data[0].embedding

            # Log the first few dimensions and the total dimension count
            embedding_length = len(embedding)
            logger.info(f"Received embedding with {embedding_length} dimensions")
            logger.info(f"First 10 dimensions: {embedding[:10]}")

            return embedding

        except Exception as e:
            logger.error(f"Error in OpenAI embedding API call: {str(e)}", exc_info=True)

            # Fallback to a random embedding vector with the correct dimensions
            logger.warning(f"Using fallback random embedding due to API error")
            fallback_embedding = [random.uniform(-1, 1) for _ in range(self.embedding_dimensions)]
            return fallback_embedding

    async def perform_scam_analysis_with_rag(
        self,
        query_text: str,
        original_image_description: Optional[str] = None,
        original_platform_identified: Optional[str] = None,
        query_embedding: Optional[List[float]] = None,
        accumulated_session_context: Optional[str] = None,
        user_provided_context: Optional[str] = None,
        db: Optional[Session] = None
    ) -> ScanResultData:
        """
        Perform scam analysis using OpenAI with RAG.

        Args:
            query_text: The original text extracted by get_multimodal_analysis
            original_image_description: The image description from get_multimodal_analysis (if an image was scanned)
            original_platform_identified: The platform identified from get_multimodal_analysis (if an image was scanned)
            query_embedding: The embedding vector for the query text
            accumulated_session_context: Context from previous scans in the session
            user_provided_context: Additional context provided by the user (e.g., "Is this a scam?")
            db: Database session for retrieving knowledge base chunks

        Returns:
            ScanResultData with the analysis result
        """
        logger.info("Called OpenAIModelService.perform_scam_analysis_with_rag")

        # Log user provided context
        if user_provided_context:
            logger.info(f"User provided context received: {user_provided_context[:100]}...")
        else:
            logger.info("No user provided context received")

        try:
            # 1. Retrieve relevant knowledge from the knowledge base
            if db and query_embedding:
                from app.crud import crud_kb

                logger.info("Retrieving relevant knowledge from the knowledge base")
                retrieved_chunks = crud_kb.find_similar_kb_chunks(
                    db=db,
                    query_embedding=query_embedding,
                    top_k=3
                )

                # Format the retrieved chunks into a single string
                knowledge_context = "\n---\n".join([chunk.content for chunk in retrieved_chunks])

                # Log the retrieved chunks
                logger.info(f"Retrieved {len(retrieved_chunks)} knowledge chunks")
                for i, chunk in enumerate(retrieved_chunks):
                    logger.info(f"Chunk {i+1} source: {chunk.source}")
            else:
                logger.warning("No database session or query embedding provided, proceeding without knowledge retrieval")
                knowledge_context = "No relevant knowledge found in the knowledge base."
                retrieved_chunks = []

            # 2. Construct the prompt based on tier
            if self.tier == "basic":
                # Basic tier prompt (shorter, more concise)
                system_message = {
                    "role": "system",
                    "content": "You are a scam detection assistant. Based on the user's query, session context, and relevant knowledge, determine if it's a scam."
                }

                user_prompt_content = ""

                # Add session context if available
                if accumulated_session_context:
                    user_prompt_content += f"Previous Context from this Session:\n------\n{accumulated_session_context}\n------\n\n"

                user_prompt_content += f"Current Input to Analyze: {query_text}\n\n"

                if user_provided_context:
                    user_prompt_content += f"User's Question/Context: {user_provided_context}\n\n"

                if original_image_description:
                    user_prompt_content += f"Image Description: {original_image_description}\n"
                if original_platform_identified:
                    user_prompt_content += f"Platform: {original_platform_identified}\n\n"

                user_prompt_content += (
                    "Knowledge:\n"
                    f"{knowledge_context}\n\n"
                    "Is this a scam? (Yes/No/Uncertain)\n"
                    "Risk Level: (Low/Medium/High)\n"
                    "Main Reason: [Provide a very brief one-sentence reason]"
                )

                if accumulated_session_context:
                    user_prompt_content += "\nOverall Session Assessment: [Brief assessment of the entire conversation]"

            elif self.tier == "expert":
                # Expert tier prompt (forensic-level analysis with o4-mini)
                system_message = {
                    "role": "system",
                    "content": "You are a highly advanced Forensic Scam Analyst AI with exceptional reasoning capabilities. Your task is to perform an exhaustive and deeply analytical examination of the user's query, any associated image/platform context, previous session context, and the provided knowledge base information to uncover potential scams, deceptions, or manipulative tactics. Provide a comprehensive, structured, and insightful forensic report."
                }

                user_prompt_content = ""

                # Add session context if available
                if accumulated_session_context:
                    user_prompt_content += f"Previous Context from this Session:\n------\n{accumulated_session_context}\n------\n\n"

                user_prompt_content += f"Current Input to Analyze: \"{query_text}\"\n\n"

                if user_provided_context:
                    user_prompt_content += f"User's Question/Context: \"{user_provided_context}\"\n\n"

                if original_image_description:
                    user_prompt_content += f"Context from Current Image: \"{original_image_description}\"\n"
                if original_platform_identified:
                    user_prompt_content += f"Identified Platform from Current Image: \"{original_platform_identified}\"\n\n"

                user_prompt_content += (
                    "Relevant information from our knowledge base:\n"
                    "------\n"
                    f"{knowledge_context}\n"
                    "------\n\n"
                    "Based on an exhaustive forensic analysis of ALL the above information, please provide the following very detailed report:\n"
                    "1. Overall Risk Assessment (0-100% likelihood of being a scam, with nuanced justification).\n"
                    "2. Deep Dive Explanation & Forensic Analysis (Detail your step-by-step reasoning. Identify subtle cues, infer attacker motivations if possible, explain complex tactics, cross-reference information from the query and knowledge base extensively).\n"
                    "3. Comprehensive List of All Identified Indicators & Red Flags (Categorize them if possible, e.g., psychological tactics, technical indicators, inconsistencies. Explain the significance of each in detail).\n"
                    "4. Detailed Actionable Recommendations & Preventative Strategies (Provide specific, prioritized steps the user should take, and broader advice to avoid similar situations).\n"
                    "5. Confidence Level & Potential Ambiguities (State your confidence and clearly articulate any ambiguities or areas where more information would be needed for a more definitive conclusion).\n"
                )

                if accumulated_session_context:
                    user_prompt_content += (
                        "6. Overall Session Assessment (Analyze the entire conversation flow across all messages in the session. Identify patterns, progression of tactics, or changes in approach that might indicate a sophisticated scam operation. Assess whether the current message reinforces or changes your assessment of previous messages).\n"
                    )

                user_prompt_content += (
                    "7. (Optional: If applicable and identifiable) Possible type/name of scam (e.g., \"This aligns with a classic 'Pig Butchering' scam modus operandi because...\")."
                )
            else:
                # Premium tier prompt (detailed analysis)
                system_message = {
                    "role": "system",
                    "content": "You are an Advanced Scam Detection Expert AI. Your goal is to analyze the user's query, session context, and the provided knowledge base information to determine if it's a scam. Provide a detailed, structured analysis."
                }

                user_prompt_content = ""

                # Add session context if available
                if accumulated_session_context:
                    user_prompt_content += f"Previous Context from this Session:\n------\n{accumulated_session_context}\n------\n\n"

                user_prompt_content += f"Current Input to Analyze: \"{query_text}\"\n\n"

                if user_provided_context:
                    user_prompt_content += f"User's Question/Context: \"{user_provided_context}\"\n\n"

                if original_image_description:
                    user_prompt_content += f"Context from Current Image: \"{original_image_description}\"\n"
                if original_platform_identified:
                    user_prompt_content += f"Identified Platform from Current Image: \"{original_platform_identified}\"\n\n"

                # Add session message detection logic
                if accumulated_session_context:
                    user_prompt_content += (
                        "IMPORTANT SESSION ANALYSIS:\n"
                        "Since this is a follow-up in an existing session, first determine:\n"
                        "1. Is this a GENERAL QUESTION about scams/security (session message)?\n"
                        "2. Or is this NEW CONTENT to analyze for scams (new scan)?\n\n"
                        "If it's a GENERAL QUESTION: Provide a helpful conversational response that references the previous scan context when relevant. "
                        "Use the session context below to provide personalized advice related to their previous scan.\n"
                        "If it's NEW CONTENT: Proceed with full structured scam analysis.\n\n"
                        f"PREVIOUS SCAN CONTEXT:\n{accumulated_session_context}\n\n"
                        "When answering general questions, reference this context when relevant to provide more personalized and helpful advice.\n\n"
                    )

                user_prompt_content += (
                    "Relevant information from our knowledge base that might help your analysis:\n"
                    "------\n"
                    f"{knowledge_context}\n"
                    "------\n\n"
                    "CRITICAL: You MUST provide a comprehensive Premium-tier analysis in the EXACT JSON format below. This is a PREMIUM analysis - be thorough, detailed, and comprehensive. ALL fields are REQUIRED.\n\n"
                    "IMPORTANT: If this is a GENERAL QUESTION (not content to analyze), respond with a helpful conversational answer instead of the JSON structure.\n\n"
                    "For SCAM ANALYSIS, respond with ONLY this JSON structure (no additional text before or after):\n\n"
                    "{\n"
                    '  "risk_score": [PROVIDE_ACCURATE_SCORE_0.0_TO_1.0],\n'
                    '  "explanation": "Write as a professional consultant explaining your analysis directly to the client. Use proper paragraph spacing with line breaks (\\n\\n) between different points or aspects of your analysis. Strategically use second-person narration (\\"you\\") when identifying threats directly applicable to the user, emphasizing consequences, or building rapport. Provide a comprehensive, detailed explanation covering multiple aspects of why this is or isn\'t a scam. Reference the content and contextual factors. Maintain a professional yet natural tone as their trusted advisor.",\n'
                    '  "red_flags": [\n'
                    '    "HIGH RISK (0.7-1.0): List red flags explaining why this is likely a scam",\n'
                    '    "LOW RISK (0.0-0.3): List green flags explaining why this appears legitimate",\n'
                    '    "MEDIUM RISK (0.4-0.6): Mix of red and green flags with balanced analysis",\n'
                    '    "Adapt flags based on your risk score assessment"\n'
                    '  ],\n'
                    '  "recommendations": "• Immediate action: [specific step]\\n\\n• Secondary action: [specific step]\\n\\n• Long-term prevention: [specific step]\\n\\n• Additional recommendations: [as needed]\\n\\nSpeak directly to the user as their consultant using strategic \\"you\\" when giving actionable advice. Use proper spacing (\\n\\n) between bullet points for better readability.",\n'
                    '  "confidence_level": "High",\n'
                    '  "key_findings": "• Strategic insight 1: [unique perspective beyond red flags]\\n\\n• Strategic insight 2: [escalated analysis point]\\n\\n• Strategic insight 3: [broader pattern or implication]\\n\\n• Additional strategic insights: [as needed]\\n\\nProvide elevated insights that build upon your analysis - focus on strategic perspectives, broader implications, or deeper patterns rather than repeating red flag information. Use proper spacing (\\n\\n) between bullet points.",\n'
                    '  "knowledge_base_references": "• HIGH RISK: Reference KB patterns that confirm scam indicators\\n\\n• LOW RISK: Reference KB patterns that explain why this appears legitimate (not matching scam patterns)\\n\\n• MEDIUM RISK: Reference relevant KB information for balanced assessment\\n\\nVary your references - use \\"our knowledge base\\", \\"our KB\\", \\"the database\\", etc. to avoid repetition. Use proper spacing (\\n\\n) between bullet points for better readability."\n'
                    "}\n\n"
                    "PREMIUM ANALYSIS REQUIREMENTS:\n"
                    "- risk_score: CRITICAL - Provide accurate risk assessment between 0.0 and 1.0:\n"
                    "  * 0.0-0.3: Low risk (likely legitimate)\n"
                    "  * 0.4-0.6: Medium risk (uncertain, needs caution)\n"
                    "  * 0.7-1.0: High risk (likely scam)\n"
                    "  Assess each case individually based on evidence.\n"
                    "- explanation: Write as a professional consultant speaking directly to the client - comprehensive, detailed, natural tone while maintaining professional neutrality\n"
                    "- red_flags: CRITICAL - Adapt based on risk score:\n"
                    "  * HIGH RISK (0.7-1.0): Red flags explaining scam indicators\n"
                    "  * LOW RISK (0.0-0.3): Green flags explaining legitimacy\n"
                    "  * MEDIUM RISK (0.4-0.6): Mix of red and green flags\n"
                    "- recommendations: Use bullet points (\\n•), speak directly to the user as their consultant with personalized advice\n"
                    "- confidence_level: 'High', 'Medium', or 'Low' based on evidence quality\n"
                    "- key_findings: Use bullet points (\\n•), provide strategic insights and broader implications that go beyond red flags - avoid repetition\n"
                    "- knowledge_base_references: Use bullet points (\\n•), always refer to 'our knowledge base' not 'the knowledge base'\n\n"
                    "TONE GUIDELINES:\n"
                    "- Analysis & Red Flags: Professional, neutral, analytical with strategic use of 'you' for threats/consequences\n"
                    "- Key Findings: Strategic, elevated insights, broader patterns with proper spacing\n"
                    "- Recommendations: Direct, personalized consultant advice using 'you' for actionable steps\n"
                    "- Knowledge Base: Vary references ('our knowledge base', 'our KB', 'the database') to avoid repetition\n\n"
                    "SPACING REQUIREMENTS:\n"
                    "- Analysis: Use \\n\\n between different points/aspects for paragraph spacing\n"
                    "- All bullet sections: Use \\n\\n between bullet points for proper spacing\n"
                    "- Maintain left alignment with natural line breaks\n\n"
                    "QUALITY: This is PREMIUM analysis - be comprehensive, detailed, and thorough. Avoid repetition between sections.\n\n"
                    "Respond with ONLY the JSON object."
                )



            messages = [
                system_message,
                {"role": "user", "content": user_prompt_content}
            ]

            # Log the prompt
            logger.info(f"Sending prompt to {self.model} for RAG analysis")

            # 3. Make API call to the appropriate model based on tier
            logger.info(f"Making API call to {self.model} for RAG analysis")

            # o4-mini doesn't support temperature parameter
            if self.model == "o4-mini":
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=messages
                )
            else:
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=0.3
                )

            ai_response_text = response.choices[0].message.content

            # Log the response
            logger.info(f"Received response from GPT-4.1")
            logger.info(f"Raw response: {ai_response_text[:500]}...")

            # 4. Parse the GPT-4.1 response (JSON for scans, text for session messages)
            try:
                # Check if this is a session message (no JSON structure)
                json_start = ai_response_text.find('{')

                # Check if this is a session message (look for specific indicators)
                if ("general question" in ai_response_text.lower() and "conversational answer" in ai_response_text.lower()) or (json_start == -1 and accumulated_session_context):
                    # This is a session message response
                    logger.info("Detected session message response")

                    # Extract just the conversational part (remove AI's internal reasoning)
                    clean_response = ai_response_text

                    # Remove the AI's internal analysis text
                    if "This is a general question" in clean_response:
                        # Find where the actual answer starts
                        answer_start = clean_response.find("Here's a helpful")
                        if answer_start != -1:
                            # Find the end of the intro line
                            intro_end = clean_response.find(":", answer_start)
                            if intro_end != -1:
                                clean_response = clean_response[intro_end + 1:].strip()

                    # Return as session message format
                    return ScanResultData(
                        risk_score=0.0,  # No risk score for session messages
                        detected_red_flags=[],
                        explanation=clean_response,
                        recommendations="",
                        confidence_level="N/A",
                        key_findings="",
                        knowledge_base_references="",
                        model_used=f"{self.model}_session_message",
                        is_general_question=True
                    )

                # Try to extract JSON from the response (full scan)
                json_end = ai_response_text.rfind('}') + 1

                if json_start != -1 and json_end > json_start:
                    json_str = ai_response_text[json_start:json_end]
                    response_data = json.loads(json_str)

                    # Extract all fields from JSON
                    risk_score = float(response_data.get("risk_score", 0.5))
                    explanation = response_data.get("explanation", "")
                    detected_red_flags = response_data.get("red_flags", [])
                    recommendations = response_data.get("recommendations", "")
                    confidence_level = response_data.get("confidence_level", "Medium")
                    key_findings = response_data.get("key_findings", "")
                    knowledge_base_references = response_data.get("knowledge_base_references", "")

                    logger.info(f"Successfully parsed JSON response with all fields")

                else:
                    raise ValueError("No valid JSON found in response")

            except (json.JSONDecodeError, ValueError) as e:
                logger.warning(f"Failed to parse JSON response: {e}")
                logger.info("Falling back to text parsing...")

                # Check if this is a session message in the fallback
                if ("general question" in ai_response_text.lower() or
                    "not content to analyze" in ai_response_text.lower() or
                    "not a scam analysis" in ai_response_text.lower() or
                    accumulated_session_context):

                    logger.info("Detected session message in fallback logic")

                    # Clean the response
                    clean_response = ai_response_text
                    if "This is a general question" in clean_response:
                        answer_start = clean_response.find("Here's a helpful")
                        if answer_start != -1:
                            intro_end = clean_response.find(":", answer_start)
                            if intro_end != -1:
                                clean_response = clean_response[intro_end + 1:].strip()

                    # Return as session message
                    return ScanResultData(
                        risk_score=0.0,
                        detected_red_flags=[],
                        explanation=clean_response,
                        recommendations="",
                        confidence_level="N/A",
                        key_findings="",
                        knowledge_base_references="",
                        model_used=f"{self.model}_session_message_fallback",
                        is_general_question=True
                    )

                # Fallback to basic text parsing for actual scans
                risk_score = 0.5
                explanation = ai_response_text
                detected_red_flags = ["Analysis completed with text parsing fallback"]
                recommendations = "Exercise caution and verify through official channels."
                confidence_level = "Medium"
                key_findings = ""
                knowledge_base_references = ""

                # Try to extract risk score from text
                percentage_match = re.search(r'(\d+)%', ai_response_text)
                if percentage_match:
                    risk_score = float(percentage_match.group(1)) / 100.0

            # 5. Return ScanResultData
            return ScanResultData(
                risk_score=risk_score,
                detected_red_flags=detected_red_flags,
                explanation=explanation,
                recommendations=recommendations,
                confidence_level=confidence_level,
                key_findings=key_findings,
                knowledge_base_references=knowledge_base_references,
                model_used=f"{self.model}_with_RAG",
                is_general_question=False
            )

        except Exception as e:
            logger.error(f"Error in perform_scam_analysis_with_rag: {str(e)}", exc_info=True)

            # Return a fallback result
            return ScanResultData(
                risk_score=0.5,
                detected_red_flags=["Error in analysis process"],
                explanation=f"An error occurred during the scam analysis: {str(e)}. The system was unable to complete a thorough analysis of the content.",
                recommendations="Since we couldn't complete a full analysis, please exercise caution with this content. Consider manually reviewing it for suspicious elements.",
                confidence_level="Low (Error Occurred)",
                model_used=f"{self.model}_with_RAG (error fallback)",
                is_general_question=False
            )


class AnthropicModelService(AIServiceInterface):
    """Anthropic Claude-based implementation of AI services."""

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize Anthropic service.

        Args:
            api_key: Anthropic API key (defaults to settings.ANTHROPIC_API_KEY)
        """
        self.api_key = api_key or settings.ANTHROPIC_API_KEY
        logger.info("Initialized AnthropicModelService")

    async def get_multimodal_analysis(
        self,
        image_path: Optional[str] = None,
        text_input: Optional[str] = None,
        url_input: Optional[str] = None
    ) -> Dict[str, Any]:
        """Analyze content using Anthropic's multimodal capabilities."""
        logger.info("Called AnthropicModelService.get_multimodal_analysis")

        result = {}

        if image_path:
            logger.info(f"Would analyze image at: {image_path}")
            result = {
                "extracted_text": "Mock extracted text from image via Anthropic Claude.",
                "image_description": "Mock image description from Claude: The image shows what looks like a phishing attempt."
            }
        elif text_input:
            result = {
                "extracted_text": text_input,
                "image_description": None
            }
        elif url_input:
            result = {
                "extracted_text": f"Mock extracted text from URL {url_input} via Claude.",
                "image_description": None,
                "url_content_summary": "Mock URL content summary from Claude: This website has characteristics of a scam site."
            }

        return result

    async def get_text_embedding(self, text: str) -> List[float]:
        """Generate text embedding (note: Claude doesn't have native embedding API yet)."""
        logger.info("Called AnthropicModelService.get_text_embedding")

        # Generate mock embedding vector (1024 dimensions)
        mock_embedding = [random.uniform(-1, 1) for _ in range(1024)]
        return mock_embedding

    async def perform_scam_analysis_with_rag(
        self,
        query_text: str,
        original_image_description: Optional[str] = None,
        original_platform_identified: Optional[str] = None,
        query_embedding: Optional[List[float]] = None,
        accumulated_session_context: Optional[str] = None,
        db: Optional[Session] = None
    ) -> ScanResultData:
        """Perform scam analysis using Claude with RAG."""
        logger.info("Called AnthropicModelService.perform_scam_analysis_with_rag")

        # Mock analysis result
        result = ScanResultData(
            risk_score=0.82,
            detected_red_flags=[
                "Impersonation of authority",
                "Grammatical errors",
                "Unusual request format"
            ],
            explanation="The content appears to be a scam attempt. It uses impersonation tactics and contains several linguistic patterns common in fraudulent communications. The unusual formatting and request structure are consistent with known scam patterns.",
            recommendations="Ignore this message and do not engage with the sender. Report this to the relevant platform if possible. Never share personal or financial information in response to unsolicited communications."
        )

        # Add session assessment if session context was provided
        if accumulated_session_context:
            result.overall_session_assessment = "Based on the entire conversation, this appears to be a consistent pattern of scam behavior."

        return result


class GoogleGeminiModelService(AIServiceInterface):
    """Google Gemini-based implementation of AI services."""

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize Google Gemini service.

        Args:
            api_key: Google API key (defaults to settings.GOOGLE_API_KEY)
        """
        self.api_key = api_key or settings.GOOGLE_API_KEY
        logger.info("Initialized GoogleGeminiModelService")

    async def get_multimodal_analysis(
        self,
        image_path: Optional[str] = None,
        text_input: Optional[str] = None,
        url_input: Optional[str] = None
    ) -> Dict[str, Any]:
        """Analyze content using Google's multimodal capabilities."""
        logger.info("Called GoogleGeminiModelService.get_multimodal_analysis")

        result = {}

        if image_path:
            logger.info(f"Would analyze image at: {image_path}")
            result = {
                "extracted_text": "Mock extracted text from image via Google Gemini.",
                "image_description": "Mock image description from Gemini: The image contains elements consistent with fraudulent communications."
            }
        elif text_input:
            result = {
                "extracted_text": text_input,
                "image_description": None
            }
        elif url_input:
            result = {
                "extracted_text": f"Mock extracted text from URL {url_input} via Gemini.",
                "image_description": None,
                "url_content_summary": "Mock URL content summary from Gemini: This appears to be a deceptive website."
            }

        return result

    async def get_text_embedding(self, text: str) -> List[float]:
        """Generate text embedding using Google's embedding model."""
        logger.info("Called GoogleGeminiModelService.get_text_embedding")

        # Generate mock embedding vector (768 dimensions)
        mock_embedding = [random.uniform(-1, 1) for _ in range(768)]
        return mock_embedding

    async def perform_scam_analysis_with_rag(
        self,
        query_text: str,
        original_image_description: Optional[str] = None,
        original_platform_identified: Optional[str] = None,
        query_embedding: Optional[List[float]] = None,
        accumulated_session_context: Optional[str] = None,
        db: Optional[Session] = None
    ) -> ScanResultData:
        """Perform scam analysis using Google Gemini with RAG."""
        logger.info("Called GoogleGeminiModelService.perform_scam_analysis_with_rag")

        # Mock analysis result
        result = ScanResultData(
            risk_score=0.68,
            detected_red_flags=[
                "Suspicious links",
                "Request for immediate action",
                "Offers that seem too good to be true"
            ],
            explanation="Based on analysis, this content contains several indicators of a potential scam. The combination of urgent language, suspicious links, and unrealistic offers matches patterns seen in known fraudulent communications.",
            recommendations="Exercise caution and verify the authenticity of this message through official channels before taking any action. Do not click on any links or download any attachments."
        )

        # Add session assessment if session context was provided
        if accumulated_session_context:
            result.overall_session_assessment = "The conversation history shows a pattern of increasingly suspicious behavior typical of scam operations."

        return result


def get_ai_service(service_name: Optional[str] = None) -> AIServiceInterface:
    """
    Factory function to get the appropriate AI service.

    Args:
        service_name: Name of the service to use (openai, anthropic, google)

    Returns:
        An instance of the requested AI service
    """
    service_name = service_name or settings.DEFAULT_AI_SERVICE

    if service_name.lower() == "openai":
        return OpenAIModelService(api_key=settings.OPENAI_API_KEY)
    elif service_name.lower() in ("anthropic", "claude"):
        return AnthropicModelService(api_key=settings.ANTHROPIC_API_KEY)
    elif service_name.lower() in ("google", "gemini"):
        return GoogleGeminiModelService(api_key=settings.GOOGLE_API_KEY)
    else:
        logger.warning(f"Unknown AI service: {service_name}, falling back to OpenAI")
        return OpenAIModelService(api_key=settings.OPENAI_API_KEY)
