<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/dismiss_area_background"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="16dp">

        <ImageView
            android:id="@+id/dismiss_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_close"
            android:tint="#FFFFFF"
            android:contentDescription="Drag here to dismiss" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Drag here to dismiss"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

    </LinearLayout>

</FrameLayout>
