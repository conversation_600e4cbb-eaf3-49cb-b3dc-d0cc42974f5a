#!/usr/bin/env python3
"""
Check scan 99 results to see what the frontend should be getting
"""
import requests
import json

API_BASE_URL = "https://domeai-backend.onrender.com"

def check_scan_99():
    # Login first
    login_url = f"{API_BASE_URL}/api/v1/auth/login"
    login_data = {
        "username": "<EMAIL>",
        "password": "@#48CrVGt3"
    }
    
    print("Logging in...")
    response = requests.post(login_url, data=login_data)
    if response.status_code != 200:
        print(f"Login failed: {response.status_code}")
        return
    
    token = response.json()["access_token"]
    print("✅ Login successful")
    
    # Check scan 99
    scan_url = f"{API_BASE_URL}/api/v1/scans/99"
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\nChecking scan 99...")
    response = requests.get(scan_url, headers=headers)
    
    print(f"Status Code: {response.status_code}")
    print(f"Headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        try:
            scan_data = response.json()
            print(f"✅ JSON Response received")
            print(f"Status: {scan_data.get('status')}")
            print(f"Scan ID: {scan_data.get('id')}")
            print(f"User Context: {scan_data.get('user_provided_context', 'None')[:100]}...")
            
            analysis = scan_data.get('analysis_result')
            if analysis:
                print(f"\n📊 Analysis Result:")
                print(f"  Risk Score: {analysis.get('risk_score')}")
                print(f"  Confidence: {analysis.get('confidence_level')}")
                print(f"  Red Flags Count: {len(analysis.get('detected_red_flags', []))}")
                print(f"  Recommendations Count: {len(analysis.get('recommendations', []))}")
                print(f"  Knowledge Base Refs: {len(analysis.get('knowledge_base_references', []))}")
                
                # Check for any problematic fields that might cause frontend issues
                print(f"\n🔍 Field Types Check:")
                for key, value in analysis.items():
                    print(f"  {key}: {type(value).__name__}")
                    if isinstance(value, list) and len(value) > 0:
                        print(f"    First item type: {type(value[0]).__name__}")
                
            else:
                print("❌ No analysis result found")
                
            # Save full response for debugging
            with open('scan_99_response.json', 'w') as f:
                json.dump(scan_data, f, indent=2, default=str)
            print(f"\n💾 Full response saved to scan_99_response.json")
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            print(f"Raw response: {response.text[:500]}...")
    else:
        print(f"❌ Failed to get scan: {response.status_code}")
        print(f"Response: {response.text}")

if __name__ == "__main__":
    check_scan_99()
