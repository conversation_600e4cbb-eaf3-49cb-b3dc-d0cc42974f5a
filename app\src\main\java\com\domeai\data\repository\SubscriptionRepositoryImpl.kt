package com.domeai.data.repository

import android.util.Log
import com.domeai.data.model.BillingCycle
import com.domeai.data.model.PaymentRecord
import com.domeai.data.model.PaymentStatus
import com.domeai.data.model.Subscription
import com.domeai.data.model.SubscriptionStatus
import com.domeai.data.model.network.ChangePlanRequest
import com.domeai.data.model.network.SubscriptionDetailsResponse
import com.domeai.data.network.SubscriptionApiService
import com.domeai.data.preferences.AuthPreferences
import com.domeai.data.util.ApiResponse
import com.domeai.data.util.safeApiCall
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

private const val TAG = "SubscriptionRepositoryImpl"

/**
 * Implementation of SubscriptionRepository that connects to the backend API
 */
@Singleton
class SubscriptionRepositoryImpl @Inject constructor(
    private val subscriptionApiService: SubscriptionApiService,
    private val authPreferences: AuthPreferences
) : SubscriptionRepository {

    // In-memory cache of subscription data
    private val subscriptionFlow = MutableStateFlow(
        Subscription(
            id = "sub123",
            userId = "user123",
            planName = "Basic Plan",
            price = 0.0,
            billingCycle = BillingCycle.FREE,
            startDate = Date(),
            endDate = Date(Long.MAX_VALUE),
            isAutoRenewEnabled = false,
            status = SubscriptionStatus.ACTIVE
        )
    )

    // In-memory cache of payment history
    private val paymentHistoryFlow = MutableStateFlow<List<PaymentRecord>>(emptyList())

    override suspend fun getCurrentSubscription(forceRefresh: Boolean): Subscription {
        try {
            // If not forcing refresh and we already have a subscription, return it
            if (!forceRefresh && subscriptionFlow.value.planName.isNotEmpty()) {
                Log.d(TAG, "Using cached subscription: ${subscriptionFlow.value.planName}, " +
                        "scansThisMonth=${subscriptionFlow.value.scansThisMonth}, " +
                        "expertScansThisMonth=${subscriptionFlow.value.expertScansThisMonth}")
                return subscriptionFlow.value
            }

            // Get auth token
            val authHeader = authPreferences.authHeader.first()
                ?: return subscriptionFlow.value

            Log.d(TAG, "Fetching subscription details from API (forceRefresh=$forceRefresh)")

            // Call API
            when (val response = safeApiCall {
                subscriptionApiService.getSubscriptionDetails(authHeader)
            }) {
                is ApiResponse.Success -> {
                    // Update subscription flow with data from API
                    Log.d(TAG, "Successfully fetched subscription details: ${response.data}")
                    updateSubscriptionFromResponse(response.data)
                }
                is ApiResponse.Error -> {
                    Log.e(TAG, "Error getting subscription details: ${response.errorMessage}")
                    Log.e(TAG, "Error details: ${response.errorBody}")

                    // If we get a 401 Unauthorized error, the token might be expired
                    if (response.code == 401) {
                        Log.e(TAG, "Unauthorized error - token might be expired")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception getting subscription details", e)
        }

        Log.d(TAG, "Returning subscription: ${subscriptionFlow.value.planName}, " +
                "scansThisMonth=${subscriptionFlow.value.scansThisMonth}, " +
                "expertScansThisMonth=${subscriptionFlow.value.expertScansThisMonth}")
        return subscriptionFlow.value
    }

    override suspend fun updateSubscriptionPlan(planName: String): Subscription {
        try {
            // Get auth token
            val authHeader = authPreferences.authHeader.first()
                ?: return subscriptionFlow.value

            // Create request payload
            val request = ChangePlanRequest(
                email = "<EMAIL>", // Hardcoded test user email
                newPlan = planName.lowercase()
            )

            Log.d(TAG, "Sending plan change request: $request with auth: $authHeader")

            // Call API
            when (val response = safeApiCall {
                subscriptionApiService.changePlan(authHeader, request)
            }) {
                is ApiResponse.Success -> {
                    Log.d(TAG, "Plan changed successfully: ${response.data}")

                    // Update subscription flow with new plan
                    val currentSubscription = subscriptionFlow.value

                    val (price, billingCycle) = when (planName.lowercase()) {
                        "basic" -> 0.0 to BillingCycle.FREE
                        "premium" -> 9.99 to BillingCycle.MONTHLY
                        "expert" -> 24.99 to BillingCycle.MONTHLY
                        else -> 0.0 to BillingCycle.FREE
                    }

                    val endDate = when (billingCycle) {
                        BillingCycle.FREE -> Date(Long.MAX_VALUE)
                        BillingCycle.MONTHLY -> Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000)
                        BillingCycle.ANNUAL -> Date(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000)
                    }

                    // Get updated scan allowances from the response
                    val monthlyScanAllowance = response.data.monthlyScanAllowance
                    val expertScanAllowance = response.data.expertScanAllowance

                    val updatedSubscription = currentSubscription.copy(
                        planName = "${planName.replaceFirstChar { it.uppercase() }} Plan",
                        price = price,
                        billingCycle = billingCycle,
                        startDate = Date(),
                        endDate = endDate,
                        status = SubscriptionStatus.ACTIVE,
                        // Reset scan counts when changing plans
                        scansThisMonth = 0,
                        expertScansThisMonth = 0,
                        monthlyScanAllowance = monthlyScanAllowance,
                        expertScanAllowance = expertScanAllowance
                    )

                    Log.d(TAG, "Updated subscription after plan change: " +
                            "tier=${response.data.subscriptionTier}, " +
                            "monthlyScanAllowance=$monthlyScanAllowance, " +
                            "expertScanAllowance=$expertScanAllowance")

                    subscriptionFlow.value = updatedSubscription

                    // Add a payment record if it's not a free plan
                    if (planName.lowercase() != "basic") {
                        val newPayment = PaymentRecord(
                            id = UUID.randomUUID().toString(),
                            subscriptionId = currentSubscription.id,
                            userId = currentSubscription.userId,
                            amount = price,
                            description = "${planName.replaceFirstChar { it.uppercase() }} Plan - ${billingCycle.name.lowercase().replaceFirstChar { it.uppercase() }}",
                            date = Date(),
                            status = PaymentStatus.COMPLETED
                        )

                        val currentPayments = paymentHistoryFlow.value.toMutableList()
                        currentPayments.add(0, newPayment)
                        paymentHistoryFlow.value = currentPayments
                    }

                    // After changing plan, immediately fetch the latest subscription details
                    // to ensure we have the most up-to-date information
                    try {
                        Log.d(TAG, "Fetching updated subscription details after plan change")
                        val detailsResponse = safeApiCall {
                            subscriptionApiService.getSubscriptionDetails(authHeader)
                        }
                        if (detailsResponse is ApiResponse.Success) {
                            Log.d(TAG, "Successfully fetched updated subscription details: ${detailsResponse.data}")
                            updateSubscriptionFromResponse(detailsResponse.data)
                        } else if (detailsResponse is ApiResponse.Error) {
                            Log.e(TAG, "Error fetching subscription details: ${detailsResponse.errorMessage}")
                            Log.e(TAG, "Error details: ${detailsResponse.errorBody}")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Exception fetching subscription details after plan change", e)
                    }

                    return subscriptionFlow.value
                }
                is ApiResponse.Error -> {
                    Log.e(TAG, "Error changing plan: ${response.errorMessage}")
                    Log.e(TAG, "Error details: ${response.errorBody}")
                    throw Exception("Failed to change plan: ${response.errorMessage}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception changing plan", e)
            throw e
        }

        return subscriptionFlow.value
    }

    override suspend fun setAutoRenew(enabled: Boolean): Subscription {
        // For now, just update the local subscription
        val updatedSubscription = subscriptionFlow.value.copy(
            isAutoRenewEnabled = enabled
        )
        subscriptionFlow.value = updatedSubscription
        return updatedSubscription
    }

    override suspend fun cancelSubscription(): Subscription {
        // For now, just update the local subscription to Basic plan
        return updateSubscriptionPlan("basic")
    }

    override suspend fun getPaymentHistory(): List<PaymentRecord> {
        return paymentHistoryFlow.value
    }

    override fun getPaymentHistoryFlow(): Flow<List<PaymentRecord>> {
        return paymentHistoryFlow
    }

    /**
     * Update the subscription flow with data from the API response
     */
    private fun updateSubscriptionFromResponse(response: SubscriptionDetailsResponse) {
        val currentSubscription = subscriptionFlow.value

        val (planName, price, billingCycle) = when (response.subscriptionTier.lowercase()) {
            "basic" -> Triple("Basic Plan", 0.0, BillingCycle.FREE)
            "premium" -> Triple("Premium Plan", 9.99, BillingCycle.MONTHLY)
            "expert" -> Triple("Expert Plan", 24.99, BillingCycle.MONTHLY)
            else -> Triple("Basic Plan", 0.0, BillingCycle.FREE)
        }

        val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
        val expiryDate = response.subscriptionExpiryDate?.let {
            try {
                dateFormat.parse(it)
            } catch (e: Exception) {
                Date(Long.MAX_VALUE)
            }
        } ?: Date(Long.MAX_VALUE)

        // Get scan allowances based on subscription tier
        val monthlyScanAllowance = response.monthlyScanAllowance ?: when (response.subscriptionTier.lowercase()) {
            "basic" -> 5
            "premium" -> 100
            "expert" -> 100
            else -> 5
        }

        val expertScanAllowance = response.expertScanAllowance ?: when (response.subscriptionTier.lowercase()) {
            "expert" -> 20
            else -> 0
        }

        val updatedSubscription = currentSubscription.copy(
            planName = planName,
            price = price,
            billingCycle = billingCycle,
            endDate = expiryDate,
            isAutoRenewEnabled = response.autoRenewStatus ?: false,
            scansThisMonth = response.scansThisMonth ?: 0,
            expertScansThisMonth = response.expertScansThisMonth ?: 0,
            monthlyScanAllowance = monthlyScanAllowance,
            expertScanAllowance = expertScanAllowance
        )

        Log.d(TAG, "Updated subscription: tier=${response.subscriptionTier}, " +
                "scansThisMonth=${response.scansThisMonth}, " +
                "expertScansThisMonth=${response.expertScansThisMonth}, " +
                "monthlyScanAllowance=$monthlyScanAllowance, " +
                "expertScanAllowance=$expertScanAllowance")

        subscriptionFlow.value = updatedSubscription
    }
}
