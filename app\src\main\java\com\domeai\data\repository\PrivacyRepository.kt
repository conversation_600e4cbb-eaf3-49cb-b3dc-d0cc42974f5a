package com.domeai.data.repository

import com.domeai.data.model.PrivacySettings
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for privacy-related operations
 */
interface PrivacyRepository {
    /**
     * Get the current privacy settings
     */
    suspend fun getPrivacySettings(): PrivacySettings
    
    /**
     * Update data sharing setting
     */
    suspend fun setDataSharing(enabled: Boolean): PrivacySettings
    
    /**
     * Update analytics collection setting
     */
    suspend fun setAnalyticsCollection(enabled: Boolean): PrivacySettings
    
    /**
     * Update personalization setting
     */
    suspend fun setPersonalization(enabled: Boolean): PrivacySettings
    
    /**
     * Update biometric authentication setting
     */
    suspend fun setBiometricAuth(enabled: Boolean): PrivacySettings
    
    /**
     * Update screen lock setting
     */
    suspend fun setScreenLock(enabled: Boolean): PrivacySettings
    
    /**
     * Update two-factor authentication setting
     */
    suspend fun setTwoFactorAuth(enabled: <PERSON>olean): PrivacySettings
    
    /**
     * Export user data
     */
    suspend fun exportData(): String
    
    /**
     * Delete user data
     */
    suspend fun deleteData(): Boolean
    
    /**
     * Get privacy settings as a Flow
     */
    fun getPrivacySettingsFlow(): Flow<PrivacySettings>
}
