package com.domeai.data.util

import retrofit2.Response
import java.io.IOException

/**
 * A sealed class to handle API responses
 */
sealed class ApiResponse<out T> {
    data class Success<out T>(val data: T) : ApiResponse<T>()
    data class Error(
        val errorMessage: String,
        val code: Int? = null,
        val errorBody: String? = null
    ) : ApiResponse<Nothing>()
}

/**
 * Utility function to handle API responses
 */
suspend fun <T> safeApiCall(apiCall: suspend () -> Response<T>): ApiResponse<T> {
    return try {
        val response = apiCall()

        // Log the response for debugging
        android.util.Log.d("ApiResponseHandler", "API response: ${response.code()}, message: ${response.message()}")

        if (response.isSuccessful) {
            val body = response.body()
            if (body != null) {
                android.util.Log.d("ApiResponseHandler", "Successful response with body: $body")
                ApiResponse.Success(body)
            } else {
                android.util.Log.e("ApiResponseHandler", "Response body is null despite successful response")
                ApiResponse.Error("Response body is null")
            }
        } else {
            val errorBody = response.errorBody()?.string()
            android.util.Log.e("ApiResponseHandler", "Error response: ${response.code()}, error body: $errorBody")

            val errorMessage = if (!errorBody.isNullOrEmpty()) {
                errorBody
            } else {
                "Error: ${response.code()} ${response.message()}"
            }
            ApiResponse.Error(
                errorMessage = errorMessage,
                code = response.code(),
                errorBody = errorBody
            )
        }
    } catch (e: IOException) {
        android.util.Log.e("ApiResponseHandler", "IOException during API call", e)
        ApiResponse.Error("Unable to connect to the server. Please check your internet connection.")
    } catch (e: Exception) {
        android.util.Log.e("ApiResponseHandler", "Exception during API call", e)
        ApiResponse.Error("An unexpected error occurred. Please try again later.")
    }
}
