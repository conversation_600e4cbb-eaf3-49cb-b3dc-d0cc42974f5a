package com.domeai.presentation.settings

import android.content.Context
import android.util.Log
import androidx.lifecycle.viewModelScope
import com.domeai.data.model.SubscriptionPlanInfo
import com.domeai.data.model.allSubscriptionPlans
import com.domeai.data.repository.SubscriptionRepository
import com.domeai.presentation.common.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Data class for payment history items
 */
data class PaymentHistoryItem(
    val description: String,
    val date: String,
    val amount: String
)

/**
 * UI State for Subscription Details Screen
 */
data class SubscriptionDetailsUiState(
    val currentPlan: SubscriptionPlanInfo = SubscriptionPlanInfo.Basic,
    val expiryDate: String = "",
    val isAutoRenewEnabled: Boolean = false,
    val isLoading: Boolean = false,
    val availablePlans: List<SubscriptionPlanInfo> = allSubscriptionPlans,
    val paymentHistory: List<PaymentHistoryItem> = emptyList(),
    val errorMessage: String? = null
) : com.domeai.presentation.common.UiState

/**
 * UI Events for Subscription Details Screen
 */
sealed class SubscriptionDetailsUiEvent : com.domeai.presentation.common.UiEvent {
    data object NavigateBack : SubscriptionDetailsUiEvent()
    data class ShowSnackbar(val message: String) : SubscriptionDetailsUiEvent()
}

/**
 * UI Actions for Subscription Details Screen
 */
sealed class SubscriptionDetailsUiAction : com.domeai.presentation.common.UiAction {
    data object NavigateBack : SubscriptionDetailsUiAction()
    data object ToggleAutoRenew : SubscriptionDetailsUiAction()
    data class SelectPlan(val plan: SubscriptionPlanInfo) : SubscriptionDetailsUiAction()
    data object CancelSubscription : SubscriptionDetailsUiAction()
}

/**
 * ViewModel for Subscription Details Screen
 */
@HiltViewModel
class SubscriptionDetailsViewModel @Inject constructor(
    private val subscriptionRepository: SubscriptionRepository,
    @ApplicationContext private val context: Context
) : BaseViewModel<SubscriptionDetailsUiState, SubscriptionDetailsUiEvent, SubscriptionDetailsUiAction>() {

    companion object {
        private const val TAG = "SubscriptionDetailsViewModel"
    }

    override fun createInitialState(): SubscriptionDetailsUiState = SubscriptionDetailsUiState()

    init {
        loadSubscriptionDetails()
    }

    override fun handleAction(action: SubscriptionDetailsUiAction) {
        when (action) {
            is SubscriptionDetailsUiAction.NavigateBack -> navigateBack()
            is SubscriptionDetailsUiAction.ToggleAutoRenew -> toggleAutoRenew()
            is SubscriptionDetailsUiAction.SelectPlan -> selectPlan(action.plan)
            is SubscriptionDetailsUiAction.CancelSubscription -> cancelSubscription()
        }
    }

    private fun loadSubscriptionDetails() {
        updateState { it.copy(isLoading = true) }

        viewModelScope.launch {
            try {
                // Load subscription details from the repository
                val subscription = subscriptionRepository.getCurrentSubscription()

                // Convert to UI state
                val currentPlan = when {
                    subscription.planName.contains("Basic", ignoreCase = true) -> SubscriptionPlanInfo.Basic
                    subscription.planName.contains("Premium", ignoreCase = true) -> SubscriptionPlanInfo.Premium
                    subscription.planName.contains("Expert", ignoreCase = true) -> SubscriptionPlanInfo.Expert
                    else -> SubscriptionPlanInfo.Basic
                }

                // Format expiry date
                val expiryDate = if (subscription.endDate.time == Long.MAX_VALUE) {
                    "Never"
                } else {
                    java.text.SimpleDateFormat("MMM dd, yyyy", java.util.Locale.US).format(subscription.endDate)
                }

                // Load payment history
                val paymentHistory = subscriptionRepository.getPaymentHistory().map { payment ->
                    PaymentHistoryItem(
                        description = payment.description,
                        date = java.text.SimpleDateFormat("MMM dd, yyyy", java.util.Locale.US).format(payment.date),
                        amount = "$${payment.amount}"
                    )
                }

                // Update state with loaded data
                updateState { it.copy(
                    isLoading = false,
                    currentPlan = currentPlan,
                    expiryDate = expiryDate,
                    isAutoRenewEnabled = subscription.isAutoRenewEnabled,
                    paymentHistory = paymentHistory
                ) }

            } catch (e: Exception) {
                // Handle error
                updateState { it.copy(isLoading = false, errorMessage = e.message) }
                sendEvent(SubscriptionDetailsUiEvent.ShowSnackbar("Failed to load subscription details: ${e.message}"))
            }
        }
    }

    private fun navigateBack() {
        sendEvent(SubscriptionDetailsUiEvent.NavigateBack)
    }

    private fun toggleAutoRenew() {
        val currentState = uiState.value
        val newAutoRenewValue = !currentState.isAutoRenewEnabled

        // Show loading state
        updateState { it.copy(isLoading = true) }

        // Update auto-renew setting in the repository
        viewModelScope.launch {
            try {
                // Call repository to update auto-renew setting
                val updatedSubscription = subscriptionRepository.setAutoRenew(newAutoRenewValue)

                // Update state with result
                updateState { it.copy(
                    isLoading = false,
                    isAutoRenewEnabled = updatedSubscription.isAutoRenewEnabled
                ) }

                // Show success message
                val message = if (updatedSubscription.isAutoRenewEnabled) {
                    "Auto-renew enabled"
                } else {
                    "Auto-renew disabled"
                }
                sendEvent(SubscriptionDetailsUiEvent.ShowSnackbar(message))

            } catch (e: Exception) {
                // Handle error
                updateState { it.copy(isLoading = false, errorMessage = e.message) }
                sendEvent(SubscriptionDetailsUiEvent.ShowSnackbar("Failed to update auto-renew setting: ${e.message}"))
            }
        }
    }

    private fun selectPlan(plan: SubscriptionPlanInfo) {
        // Show loading state
        updateState { it.copy(isLoading = true) }

        // Update the subscription plan in the repository
        viewModelScope.launch {
            try {
                // Convert SubscriptionPlanInfo to plan name string
                val planName = when (plan) {
                    is SubscriptionPlanInfo.Basic -> "basic"
                    is SubscriptionPlanInfo.Premium -> "premium"
                    is SubscriptionPlanInfo.Expert -> "expert"
                }

                Log.d(TAG, "Changing subscription plan to: $planName")

                // Call repository to update plan
                val updatedSubscription = subscriptionRepository.updateSubscriptionPlan(planName)

                Log.d(TAG, "Plan updated successfully: ${updatedSubscription.planName}, " +
                        "scansThisMonth=${updatedSubscription.scansThisMonth}, " +
                        "monthlyScanAllowance=${updatedSubscription.monthlyScanAllowance}")

                // Format expiry date
                val expiryDate = if (updatedSubscription.endDate.time == Long.MAX_VALUE) {
                    "Never"
                } else {
                    java.text.SimpleDateFormat("MMM dd, yyyy", java.util.Locale.US).format(updatedSubscription.endDate)
                }

                // Update state with new plan
                updateState { it.copy(
                    isLoading = false,
                    currentPlan = plan,
                    expiryDate = expiryDate,
                    isAutoRenewEnabled = updatedSubscription.isAutoRenewEnabled
                ) }

                // Reload payment history
                loadPaymentHistory()

                // Show success message
                sendEvent(SubscriptionDetailsUiEvent.ShowSnackbar("Successfully switched to ${plan.title}"))

                // Trigger a plan refresh in the ChatScanViewModel
                val sharedPrefs = context.getSharedPreferences("dome_ai_prefs", Context.MODE_PRIVATE)
                sharedPrefs.edit().putLong("plan_refresh_trigger", System.currentTimeMillis()).apply()
                Log.d(TAG, "Triggered plan refresh")

                // Force refresh the subscription in the repository to ensure all ViewModels have the latest data
                subscriptionRepository.getCurrentSubscription(forceRefresh = true)

            } catch (e: Exception) {
                // Handle error
                Log.e(TAG, "Error changing plan", e)
                updateState { it.copy(isLoading = false, errorMessage = e.message) }
                sendEvent(SubscriptionDetailsUiEvent.ShowSnackbar("Failed to update subscription plan: ${e.message}"))
            }
        }
    }

    /**
     * Load payment history from the repository
     */
    private fun loadPaymentHistory() {
        viewModelScope.launch {
            try {
                val paymentHistory = subscriptionRepository.getPaymentHistory().map { payment ->
                    PaymentHistoryItem(
                        description = payment.description,
                        date = java.text.SimpleDateFormat("MMM dd, yyyy", java.util.Locale.US).format(payment.date),
                        amount = "$${payment.amount}"
                    )
                }

                updateState { it.copy(paymentHistory = paymentHistory) }
            } catch (e: Exception) {
                Log.e("SubscriptionViewModel", "Error loading payment history", e)
            }
        }
    }

    private fun cancelSubscription() {
        // Show loading state
        updateState { it.copy(isLoading = true) }

        // Cancel the subscription in the repository
        viewModelScope.launch {
            try {
                // Call repository to cancel subscription
                val updatedSubscription = subscriptionRepository.cancelSubscription()

                // Update state to Basic Plan
                updateState { it.copy(
                    isLoading = false,
                    currentPlan = SubscriptionPlanInfo.Basic,
                    expiryDate = "Never",
                    isAutoRenewEnabled = false
                ) }

                // Reload payment history
                loadPaymentHistory()

                // Show success message
                sendEvent(SubscriptionDetailsUiEvent.ShowSnackbar("Subscription successfully cancelled"))

                // Trigger a plan refresh in the ChatScanViewModel
                val sharedPrefs = context.getSharedPreferences("dome_ai_prefs", Context.MODE_PRIVATE)
                sharedPrefs.edit().putLong("plan_refresh_trigger", System.currentTimeMillis()).apply()
                Log.d(TAG, "Triggered plan refresh after cancellation")

                // Force refresh the subscription in the repository to ensure all ViewModels have the latest data
                subscriptionRepository.getCurrentSubscription(forceRefresh = true)

            } catch (e: Exception) {
                // Handle error
                updateState { it.copy(isLoading = false, errorMessage = e.message) }
                sendEvent(SubscriptionDetailsUiEvent.ShowSnackbar("Failed to cancel subscription: ${e.message}"))
            }
        }
    }
}
