-- Create pgvector extension if it doesn't exist
CREATE EXTENSION IF NOT EXISTS vector;

-- Create scans table if it doesn't exist
CREATE TABLE IF NOT EXISTS scans (
    id SERIAL PRIMARY KEY,
    owner_id INTEGER NOT NULL REFERENCES users(id),
    status VARCHAR NOT NULL DEFAULT 'pending',
    input_text TEXT,
    input_url VARCHAR,
    input_content_type VARCHAR NOT NULL,
    user_provided_context TEXT,
    raw_input_payload JSON,
    analysis_result JSON,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    scan_session_id UUID
);

-- Create indexes for scans table
CREATE INDEX IF NOT EXISTS ix_scans_id ON scans(id);
CREATE INDEX IF NOT EXISTS ix_scans_owner_id ON scans(owner_id);
CREATE INDEX IF NOT EXISTS ix_scans_status ON scans(status);

-- Create scan_sessions table if it doesn't exist
CREATE TABLE IF NOT EXISTS scan_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id INTEGER NOT NULL REFERENCES users(id),
    title VARCHAR,
    created_at TIMESTAMP DEFAULT now() NOT NULL,
    last_activity_at TIMESTAMP DEFAULT now() NOT NULL
);

-- Create indexes for scan_sessions table
CREATE INDEX IF NOT EXISTS ix_scan_sessions_id ON scan_sessions(id);
CREATE INDEX IF NOT EXISTS ix_scan_sessions_owner_id ON scan_sessions(owner_id);

-- Create knowledge_base_chunks table if it doesn't exist
CREATE TABLE IF NOT EXISTS knowledge_base_chunks (
    id SERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    embedding FLOAT[] NOT NULL,
    source VARCHAR,
    created_at TIMESTAMP DEFAULT now()
);

-- Create indexes for knowledge_base_chunks table
CREATE INDEX IF NOT EXISTS ix_knowledge_base_chunks_id ON knowledge_base_chunks(id);

-- Create GIN index on embedding for faster similarity search
CREATE INDEX IF NOT EXISTS knowledge_base_chunks_embedding_idx ON knowledge_base_chunks USING gin (embedding);

-- Create or update alembic_version table to mark migrations as complete
CREATE TABLE IF NOT EXISTS alembic_version (
    version_num VARCHAR(32) NOT NULL,
    CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)
);

-- Insert the latest migration version
INSERT INTO alembic_version (version_num) VALUES ('422bc7616ce1') 
ON CONFLICT (version_num) DO UPDATE SET version_num = EXCLUDED.version_num;
