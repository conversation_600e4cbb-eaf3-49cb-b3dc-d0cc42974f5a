package com.domeai.domain.validation

import javax.inject.Inject
import javax.inject.Singleton

/**
 * Validator class for validating user input
 */
@Singleton
class Validator @Inject constructor() {
    
    /**
     * Validate email address
     */
    fun validateEmail(email: String): String? {
        if (email.isBlank()) {
            return "Email cannot be empty"
        }
        
        val emailRegex = Regex("^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,6}$")
        if (!emailRegex.matches(email)) {
            return "Invalid email format"
        }
        
        return null
    }
    
    /**
     * Validate password
     */
    fun validatePassword(password: String): String? {
        if (password.isBlank()) {
            return "Password cannot be empty"
        }
        
        if (password.length < 8) {
            return "Password must be at least 8 characters long"
        }
        
        // Check for at least one uppercase letter
        if (!password.any { it.isUpperCase() }) {
            return "Password must contain at least one uppercase letter"
        }
        
        // Check for at least one lowercase letter
        if (!password.any { it.isLowerCase() }) {
            return "Password must contain at least one lowercase letter"
        }
        
        // Check for at least one digit
        if (!password.any { it.isDigit() }) {
            return "Password must contain at least one number"
        }
        
        // Check for at least one special character
        val specialChars = "!@#$%^&*()_-+=<>?/[]{},.:;|'\\"
        if (!password.any { specialChars.contains(it) }) {
            return "Password must contain at least one special character"
        }
        
        return null
    }
    
    /**
     * Validate name
     */
    fun validateName(name: String): String? {
        if (name.isBlank()) {
            return "Name cannot be empty"
        }
        
        if (name.length < 2) {
            return "Name must be at least 2 characters long"
        }
        
        return null
    }
    
    /**
     * Validate phone number
     */
    fun validatePhone(phone: String): String? {
        if (phone.isBlank()) {
            return "Phone number cannot be empty"
        }
        
        val phoneRegex = Regex("^[+]?[0-9]{10,15}$")
        if (!phoneRegex.matches(phone)) {
            return "Invalid phone number format"
        }
        
        return null
    }
}
