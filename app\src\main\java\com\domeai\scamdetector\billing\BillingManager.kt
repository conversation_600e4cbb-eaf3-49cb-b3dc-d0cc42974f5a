package com.domeai.scamdetector.billing

import android.app.Activity
import android.content.Context
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.android.billingclient.api.*
import com.android.billingclient.api.BillingClient.BillingResponseCode
import com.android.billingclient.api.BillingClient.ProductType
import com.android.billingclient.api.Purchase.PurchaseState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Manages Google Play Billing operations for the app.
 */
class BillingManager(private val context: Context) : PurchasesUpdatedListener, BillingClientStateListener {

    companion object {
        private const val TAG = "BillingManager"
        
        // Subscription product IDs
        const val PREMIUM_MONTHLY = "sub_premium_monthly"
        const val PREMIUM_YEARLY = "sub_premium_yearly"
        const val EXPERT_MONTHLY = "sub_expert_monthly"
        const val EXPERT_YEARLY = "sub_expert_yearly"
    }
    
    // Billing client
    private val billingClient = BillingClient.newBuilder(context)
        .setListener(this)
        .enablePendingPurchases()
        .build()
    
    // Coroutine scope for async operations
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    
    // LiveData for subscription products
    private val _subscriptionProductsLiveData = MutableLiveData<List<ProductDetails>>()
    val subscriptionProductsLiveData: LiveData<List<ProductDetails>> = _subscriptionProductsLiveData
    
    // StateFlow for user subscription status
    private val _userSubscriptionFlow = MutableStateFlow<SubscriptionStatus>(SubscriptionStatus.Free)
    val userSubscriptionFlow: StateFlow<SubscriptionStatus> = _userSubscriptionFlow.asStateFlow()
    
    // Initialize the billing client
    init {
        startBillingConnection()
    }
    
    /**
     * Start the billing connection.
     */
    fun startBillingConnection() {
        billingClient.startConnection(this)
    }
    
    /**
     * Called when the billing client is ready.
     */
    override fun onBillingSetupFinished(billingResult: BillingResult) {
        if (billingResult.responseCode == BillingResponseCode.OK) {
            Log.d(TAG, "Billing client connected")
            // Query available subscriptions
            querySubscriptionProducts()
            // Query existing purchases
            queryExistingPurchases()
        } else {
            Log.e(TAG, "Billing client connection failed: ${billingResult.responseCode}")
        }
    }
    
    /**
     * Called when the billing client connection is lost.
     */
    override fun onBillingServiceDisconnected() {
        Log.d(TAG, "Billing client disconnected")
        // Retry connection
        startBillingConnection()
    }
    
    /**
     * Called when purchases are updated.
     */
    override fun onPurchasesUpdated(billingResult: BillingResult, purchases: List<Purchase>?) {
        if (billingResult.responseCode == BillingResponseCode.OK && purchases != null) {
            // Process the purchases
            for (purchase in purchases) {
                handlePurchase(purchase)
            }
        } else if (billingResult.responseCode == BillingResponseCode.USER_CANCELED) {
            Log.d(TAG, "User canceled the purchase")
        } else {
            Log.e(TAG, "Purchase failed: ${billingResult.responseCode}")
        }
    }
    
    /**
     * Query available subscription products.
     */
    private fun querySubscriptionProducts() {
        coroutineScope.launch {
            val params = QueryProductDetailsParams.newBuilder()
                .setProductList(
                    listOf(
                        QueryProductDetailsParams.Product.newBuilder()
                            .setProductId(PREMIUM_MONTHLY)
                            .setProductType(ProductType.SUBS)
                            .build(),
                        QueryProductDetailsParams.Product.newBuilder()
                            .setProductId(PREMIUM_YEARLY)
                            .setProductType(ProductType.SUBS)
                            .build(),
                        QueryProductDetailsParams.Product.newBuilder()
                            .setProductId(EXPERT_MONTHLY)
                            .setProductType(ProductType.SUBS)
                            .build(),
                        QueryProductDetailsParams.Product.newBuilder()
                            .setProductId(EXPERT_YEARLY)
                            .setProductType(ProductType.SUBS)
                            .build()
                    )
                )
                .build()
            
            val productDetailsResult = withContext(Dispatchers.IO) {
                billingClient.queryProductDetails(params)
            }
            
            if (productDetailsResult.billingResult.responseCode == BillingResponseCode.OK) {
                _subscriptionProductsLiveData.value = productDetailsResult.productDetailsList ?: emptyList()
                Log.d(TAG, "Subscription products: ${productDetailsResult.productDetailsList?.size ?: 0}")
            } else {
                Log.e(TAG, "Failed to query subscription products: ${productDetailsResult.billingResult.responseCode}")
            }
        }
    }
    
    /**
     * Query existing purchases.
     */
    private fun queryExistingPurchases() {
        coroutineScope.launch {
            val params = QueryPurchasesParams.newBuilder()
                .setProductType(ProductType.SUBS)
                .build()
            
            val purchasesResult = withContext(Dispatchers.IO) {
                billingClient.queryPurchasesAsync(params)
            }
            
            if (purchasesResult.billingResult.responseCode == BillingResponseCode.OK) {
                for (purchase in purchasesResult.purchasesList) {
                    handlePurchase(purchase)
                }
            } else {
                Log.e(TAG, "Failed to query existing purchases: ${purchasesResult.billingResult.responseCode}")
            }
        }
    }
    
    /**
     * Handle a purchase.
     */
    private fun handlePurchase(purchase: Purchase) {
        // Verify the purchase state
        if (purchase.purchaseState == PurchaseState.PURCHASED) {
            // Acknowledge the purchase if it hasn't been acknowledged yet
            if (!purchase.isAcknowledged) {
                acknowledgePurchase(purchase.purchaseToken)
            }
            
            // Update the user's subscription status
            updateSubscriptionStatus(purchase)
            
            // Verify the purchase with the backend
            verifyPurchaseWithBackend(purchase)
        } else if (purchase.purchaseState == PurchaseState.PENDING) {
            Log.d(TAG, "Purchase is pending: ${purchase.products}")
        }
    }
    
    /**
     * Acknowledge a purchase.
     */
    private fun acknowledgePurchase(purchaseToken: String) {
        coroutineScope.launch {
            val params = AcknowledgePurchaseParams.newBuilder()
                .setPurchaseToken(purchaseToken)
                .build()
            
            val result = withContext(Dispatchers.IO) {
                billingClient.acknowledgePurchase(params)
            }
            
            if (result.responseCode == BillingResponseCode.OK) {
                Log.d(TAG, "Purchase acknowledged: $purchaseToken")
            } else {
                Log.e(TAG, "Failed to acknowledge purchase: ${result.responseCode}")
            }
        }
    }
    
    /**
     * Update the user's subscription status based on a purchase.
     */
    private fun updateSubscriptionStatus(purchase: Purchase) {
        val productId = purchase.products.firstOrNull() ?: return
        
        val status = when {
            productId.contains("premium") -> SubscriptionStatus.Premium
            productId.contains("expert") -> SubscriptionStatus.Expert
            else -> SubscriptionStatus.Free
        }
        
        _userSubscriptionFlow.value = status
        Log.d(TAG, "User subscription status updated: $status")
    }
    
    /**
     * Verify a purchase with the backend.
     */
    private fun verifyPurchaseWithBackend(purchase: Purchase) {
        // TODO: Implement API call to verify purchase with backend
        Log.d(TAG, "Verifying purchase with backend: ${purchase.purchaseToken}")
    }
    
    /**
     * Launch the purchase flow for a subscription.
     */
    fun launchSubscriptionPurchaseFlow(activity: Activity, productDetails: ProductDetails, offerToken: String) {
        val productDetailsParamsList = listOf(
            BillingFlowParams.ProductDetailsParams.newBuilder()
                .setProductDetails(productDetails)
                .setOfferToken(offerToken)
                .build()
        )
        
        val billingFlowParams = BillingFlowParams.newBuilder()
            .setProductDetailsParamsList(productDetailsParamsList)
            .build()
        
        billingClient.launchBillingFlow(activity, billingFlowParams)
    }
    
    /**
     * Get the subscription product details by ID.
     */
    fun getSubscriptionProductDetails(productId: String): ProductDetails? {
        return _subscriptionProductsLiveData.value?.find { it.productId == productId }
    }
    
    /**
     * Get the offer token for a subscription product.
     */
    fun getOfferToken(productDetails: ProductDetails): String? {
        val subscriptionOfferDetails = productDetails.subscriptionOfferDetails ?: return null
        return subscriptionOfferDetails.firstOrNull()?.offerToken
    }
}

/**
 * Represents the user's subscription status.
 */
enum class SubscriptionStatus {
    Free,
    Premium,
    Expert
}
