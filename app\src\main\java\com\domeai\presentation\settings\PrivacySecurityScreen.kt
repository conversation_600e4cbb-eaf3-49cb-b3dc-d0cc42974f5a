package com.domeai.presentation.settings

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Download
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Security
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.domeai.ui.composables.VerticalSpacer

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PrivacySecurityScreen(
    onNavigateBack: () -> Unit,
    onNavigateToPrivacyPolicy: () -> Unit = {},
    onNavigateToTermsOfService: () -> Unit = {},
    viewModel: PrivacySecurityViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Handle UI events
    LaunchedEffect(key1 = viewModel) {
        viewModel.uiEvent.collect { event ->
            when (event) {
                is PrivacySecurityUiEvent.NavigateBack -> onNavigateBack()
                is PrivacySecurityUiEvent.ShowSnackbar -> {
                    snackbarHostState.showSnackbar(event.message)
                }
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Privacy & Security") },
                navigationIcon = {
                    IconButton(onClick = { viewModel.sendAction(PrivacySecurityUiAction.NavigateBack) }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp)
        ) {
            // Privacy Settings
            PrivacySettingsSection(
                dataSharing = uiState.dataSharingEnabled,
                analyticsCollection = uiState.analyticsCollectionEnabled,
                personalization = uiState.personalizationEnabled,
                onDataSharingChange = { viewModel.sendAction(PrivacySecurityUiAction.ToggleDataSharing) },
                onAnalyticsCollectionChange = { viewModel.sendAction(PrivacySecurityUiAction.ToggleAnalyticsCollection) },
                onPersonalizationChange = { viewModel.sendAction(PrivacySecurityUiAction.TogglePersonalization) }
            )

            VerticalSpacer(height = 24)

            // Security Settings
            SecuritySettingsSection(
                biometricAuth = uiState.biometricAuthEnabled,
                screenLock = uiState.screenLockEnabled,
                twoFactorAuth = uiState.twoFactorAuthEnabled,
                onBiometricAuthChange = { viewModel.sendAction(PrivacySecurityUiAction.ToggleBiometricAuth) },
                onScreenLockChange = { viewModel.sendAction(PrivacySecurityUiAction.ToggleScreenLock) },
                onTwoFactorAuthChange = { viewModel.sendAction(PrivacySecurityUiAction.ToggleTwoFactorAuth) }
            )

            VerticalSpacer(height = 24)

            // Data Management
            DataManagementSection(
                onExportData = { viewModel.sendAction(PrivacySecurityUiAction.ExportData) },
                onDeleteData = { viewModel.sendAction(PrivacySecurityUiAction.DeleteData) }
            )

            VerticalSpacer(height = 24)

            // Privacy Policy
            PrivacyPolicySection(
                onViewPrivacyPolicy = onNavigateToPrivacyPolicy,
                onViewTermsOfService = onNavigateToTermsOfService
            )
        }
    }
}

@Composable
fun PrivacySettingsSection(
    dataSharing: Boolean,
    analyticsCollection: Boolean,
    personalization: Boolean,
    onDataSharingChange: (Boolean) -> Unit,
    onAnalyticsCollectionChange: (Boolean) -> Unit,
    onPersonalizationChange: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Privacy Settings",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            VerticalSpacer(height = 16)

            // Data Sharing
            PrivacyToggleItem(
                icon = Icons.Default.Visibility,
                title = "Data Sharing",
                description = "Allow sharing of anonymized data to improve services",
                checked = dataSharing,
                onCheckedChange = onDataSharingChange
            )

            HorizontalDivider(modifier = Modifier.padding(vertical = 12.dp))

            // Analytics Collection
            PrivacyToggleItem(
                icon = Icons.Default.Info,
                title = "Analytics Collection",
                description = "Allow collection of usage data to improve app experience",
                checked = analyticsCollection,
                onCheckedChange = onAnalyticsCollectionChange
            )

            HorizontalDivider(modifier = Modifier.padding(vertical = 12.dp))

            // Personalization
            PrivacyToggleItem(
                icon = Icons.Default.Info,
                title = "Personalization",
                description = "Allow personalization of content based on your usage",
                checked = personalization,
                onCheckedChange = onPersonalizationChange
            )
        }
    }
}

@Composable
fun SecuritySettingsSection(
    biometricAuth: Boolean,
    screenLock: Boolean,
    twoFactorAuth: Boolean,
    onBiometricAuthChange: (Boolean) -> Unit,
    onScreenLockChange: (Boolean) -> Unit,
    onTwoFactorAuthChange: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Security Settings",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            VerticalSpacer(height = 16)

            // Biometric Authentication
            PrivacyToggleItem(
                icon = Icons.Default.Lock,
                title = "Biometric Authentication",
                description = "Use fingerprint or face recognition to unlock the app",
                checked = biometricAuth,
                onCheckedChange = onBiometricAuthChange
            )

            HorizontalDivider(modifier = Modifier.padding(vertical = 12.dp))

            // Screen Lock
            PrivacyToggleItem(
                icon = Icons.Default.Lock,
                title = "Screen Lock",
                description = "Lock the app when not in use",
                checked = screenLock,
                onCheckedChange = onScreenLockChange
            )

            HorizontalDivider(modifier = Modifier.padding(vertical = 12.dp))

            // Two-Factor Authentication
            PrivacyToggleItem(
                icon = Icons.Default.Security,
                title = "Two-Factor Authentication",
                description = "Add an extra layer of security to your account",
                checked = twoFactorAuth,
                onCheckedChange = onTwoFactorAuthChange
            )
        }
    }
}

@Composable
fun DataManagementSection(
    onExportData: () -> Unit,
    onDeleteData: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Data Management",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            VerticalSpacer(height = 16)

            // Export Data
            androidx.compose.material3.Button(
                onClick = onExportData,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.Download,
                    contentDescription = null
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text("Export My Data")
            }

            VerticalSpacer(height = 12)

            // Delete Data
            OutlinedButton(
                onClick = onDeleteData,
                modifier = Modifier.fillMaxWidth(),
                colors = androidx.compose.material3.ButtonDefaults.outlinedButtonColors(
                    contentColor = MaterialTheme.colorScheme.error
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = null
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text("Delete My Data")
            }
        }
    }
}

@Composable
fun PrivacyPolicySection(
    onViewPrivacyPolicy: () -> Unit,
    onViewTermsOfService: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Legal",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            VerticalSpacer(height = 16)

            // Privacy Policy
            androidx.compose.material3.Button(
                onClick = onViewPrivacyPolicy,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("View Privacy Policy")
            }

            VerticalSpacer(height = 12)

            // Terms of Service
            androidx.compose.material3.Button(
                onClick = onViewTermsOfService,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("View Terms of Service")
            }
        }
    }
}

@Composable
fun PrivacyToggleItem(
    icon: ImageVector,
    title: String,
    description: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = androidx.compose.foundation.layout.Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.weight(1f)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            Column {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium
                )

                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
        }

        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}