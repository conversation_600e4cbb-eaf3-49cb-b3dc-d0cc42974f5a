import requests
import json

def upload_knowledge_base():
    """Upload the real 57 knowledge base chunks to production"""
    
    # Load the knowledge base chunks
    try:
        with open('domeai_backend/scripts/cleaned_kb_chunks.json', 'r') as f:
            chunks = json.load(f)
        print(f"✅ Loaded {len(chunks)} knowledge base chunks from file")
    except Exception as e:
        print(f"❌ Error loading knowledge base file: {e}")
        return
    
    # Prepare the request
    url = "https://domeai-backend.onrender.com/admin/upload-kb-chunks"
    payload = {"chunks": chunks}
    
    print(f"📤 Uploading {len(chunks)} chunks to production...")
    print("⏳ This may take several minutes as each chunk needs an OpenAI embedding...")
    
    try:
        # Upload with extended timeout (10 minutes)
        response = requests.post(url, json=payload, timeout=600)
        
        if response.status_code == 200:
            result = response.json()
            print("🎉 Upload completed successfully!")
            print(f"✅ Status: {result.get('status')}")
            print(f"📊 Total processed: {result.get('total_chunks_processed')}")
            print(f"✅ Successful: {result.get('successful')}")
            print(f"❌ Failed: {result.get('failed')}")
            print(f"💾 Final count in DB: {result.get('final_count_in_db')}")
            
            if result.get('details'):
                print("\n📋 Details:")
                for detail in result.get('details', []):
                    print(f"   {detail}")
                    
            return True
        else:
            print(f"❌ Upload failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Upload request failed: {e}")
        return False

def verify_upload():
    """Verify the knowledge base was uploaded correctly"""
    print("\n🔍 Verifying upload...")
    
    try:
        response = requests.get("https://domeai-backend.onrender.com/admin/check-kb-status", timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Verification completed!")
            print(f"📊 Total chunks: {result.get('total_chunks')}")
            print(f"📋 Breakdown: {result.get('breakdown')}")
            print(f"🔧 Vector operations: {result.get('vector_operations')}")
            
            # Check if we have the expected number of chunks
            total = result.get('total_chunks', 0)
            if total >= 57:
                print(f"🎉 SUCCESS! Found {total} chunks (expected 57+)")
                return True
            else:
                print(f"⚠️ WARNING: Only found {total} chunks (expected 57+)")
                return False
        else:
            print(f"❌ Verification failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Verification request failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 DomeAI Knowledge Base Upload")
    print("=" * 50)
    
    # Step 1: Upload the knowledge base
    upload_success = upload_knowledge_base()
    
    if upload_success:
        # Step 2: Verify the upload
        verify_success = verify_upload()
        
        if verify_success:
            print("\n🎉 COMPLETE! Your 57 knowledge base chunks are now loaded!")
            print("🧪 You can now test a scan to see real RAG pipeline results!")
        else:
            print("\n⚠️ Upload completed but verification shows issues.")
    else:
        print("\n❌ Upload failed. Please check the error messages above.")
