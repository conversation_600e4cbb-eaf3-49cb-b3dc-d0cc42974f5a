package com.domeai.scamdetector.billing

import android.app.Activity
import android.content.Context
import androidx.lifecycle.LiveData
import com.android.billingclient.api.ProductDetails
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for billing operations.
 */
@Singleton
class BillingRepository @Inject constructor(context: Context) {
    
    private val billingManager = BillingManager(context)
    
    /**
     * Get the subscription products.
     */
    fun getSubscriptionProducts(): LiveData<List<ProductDetails>> {
        return billingManager.subscriptionProductsLiveData
    }
    
    /**
     * Get the user's subscription status.
     */
    fun getUserSubscriptionStatus(): StateFlow<SubscriptionStatus> {
        return billingManager.userSubscriptionFlow
    }
    
    /**
     * Launch the purchase flow for a subscription.
     */
    fun launchSubscriptionPurchaseFlow(activity: Activity, productId: String) {
        val productDetails = billingManager.getSubscriptionProductDetails(productId) ?: return
        val offerToken = billingManager.getOfferToken(productDetails) ?: return
        billingManager.launchSubscriptionPurchaseFlow(activity, productDetails, offerToken)
    }
    
    /**
     * Get the subscription product details by ID.
     */
    fun getSubscriptionProductDetails(productId: String): ProductDetails? {
        return billingManager.getSubscriptionProductDetails(productId)
    }
}
