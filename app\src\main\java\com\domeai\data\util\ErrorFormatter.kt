package com.domeai.data.util

import org.json.JSONObject

/**
 * Utility class to format error messages from the backend
 */
object ErrorFormatter {

    /**
     * Format error message from the backend
     *
     * @param errorMessage The raw error message from the backend
     * @return A user-friendly error message
     */
    fun formatErrorMessage(errorMessage: String): String {
        android.util.Log.d("ErrorFormatter", "Formatting error message: $errorMessage")

        return try {
            // Check if the error message is in JSON format
            if (errorMessage.trim().startsWith("{")) {
                val jsonObject = JSONObject(errorMessage)
                android.util.Log.d("ErrorFormatter", "Parsed JSON: $jsonObject")

                // Extract the detail field
                when {
                    // Handle array of validation errors
                    jsonObject.has("detail") && jsonObject.get("detail") is JSONObject -> {
                        val detailObj = jsonObject.getJSONObject("detail")
                        "Invalid input: ${detailObj.toString()}"
                    }

                    // Handle array of validation errors
                    jsonObject.has("detail") && jsonObject.optJSONArray("detail") != null -> {
                        val detailArray = jsonObject.getJSONArray("detail")
                        if (detailArray.length() > 0) {
                            val firstError = detailArray.getJSONObject(0)
                            val msg = firstError.optString("msg", "Unknown error")

                            // Make the message more user-friendly
                            when {
                                msg.contains("value is not a valid email") ->
                                    "Please enter a valid email address"

                                msg.contains("An email address cannot end with a period") ->
                                    "Email address cannot end with a period"

                                else -> "Please check your input and try again"
                            }
                        } else {
                            "Please check your input and try again"
                        }
                    }

                    // Handle simple string detail
                    jsonObject.has("detail") -> {
                        val detail = jsonObject.getString("detail")
                        when {
                            detail.contains("Email already registered") ->
                                "This email is already registered"

                            detail.contains("Incorrect email or password") ->
                                "Incorrect email or password"

                            detail.contains("value is not a valid email") ->
                                "Please enter a valid email address"

                            else -> detail
                        }
                    }

                    // Handle OAuth2 error format
                    jsonObject.has("error") -> {
                        val error = jsonObject.getString("error")
                        when (error) {
                            "invalid_grant" -> "Incorrect email or password"
                            "invalid_request" -> "Invalid login request"
                            "invalid_client" -> "Authentication failed"
                            else -> "Login failed: $error"
                        }
                    }

                    else -> errorMessage
                }
            } else if (errorMessage.contains("401") || errorMessage.contains("Unauthorized")) {
                "Incorrect email or password"
            } else {
                // Not JSON, return as is
                errorMessage
            }
        } catch (e: Exception) {
            // If parsing fails, log the exception and return a user-friendly message
            android.util.Log.e("ErrorFormatter", "Error parsing error message", e)

            if (errorMessage.contains("401") || errorMessage.contains("Unauthorized")) {
                "Incorrect email or password"
            } else {
                "An error occurred. Please try again."
            }
        }
    }
}
