#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test the vector similarity search functionality.

This script:
1. Takes a knowledge base chunk's content
2. Generates an embedding for it
3. Uses find_similar_kb_chunks to find similar chunks
4. Verifies that the original chunk is returned as the most similar

Usage:
    python test_vector_search.py --chunk_id <id>
"""

import argparse
import asyncio
import logging
import sys
from typing import List, Dict, Any

# Add the parent directory to the path so we can import from app
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.ai_services import OpenAIModelService
from app.core.config import settings
from app.crud import crud_kb

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

async def test_vector_search(chunk_id: int) -> Dict[str, Any]:
    """
    Test the vector similarity search functionality.
    
    Args:
        chunk_id: ID of the knowledge base chunk to use for testing
        
    Returns:
        Dictionary with test results
    """
    # Create a database session
    db = SessionLocal()
    
    try:
        # Initialize the OpenAI service
        ai_service = OpenAIModelService(api_key=settings.OPENAI_API_KEY)
        
        # Get the knowledge base chunk
        chunk = crud_kb.get_kb_chunk(db, chunk_id)
        if not chunk:
            raise ValueError(f"Chunk with ID {chunk_id} not found")
        
        logger.info(f"Testing with chunk ID {chunk_id}: {chunk.source}")
        logger.info(f"Content: {chunk.content[:150]}...")
        
        # Generate embedding for the chunk's content
        logger.info("Generating embedding for the chunk's content...")
        query_embedding = await ai_service.get_text_embedding(text=chunk.content)
        
        # Find similar chunks
        logger.info("Finding similar chunks...")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=query_embedding,
            top_k=3
        )
        
        # Check if the original chunk is in the results
        original_chunk_found = False
        for i, similar_chunk in enumerate(similar_chunks):
            logger.info(f"Similar chunk {i+1}: ID {similar_chunk.id}, Source: {similar_chunk.source}")
            logger.info(f"Similarity score: {getattr(similar_chunk, 'similarity_score', 'N/A')}")
            logger.info(f"Content: {similar_chunk.content[:150]}...")
            
            if similar_chunk.id == chunk_id:
                original_chunk_found = True
                logger.info(f"Original chunk found at position {i+1}")
        
        if not original_chunk_found:
            logger.warning("Original chunk not found in the results!")
        
        return {
            "original_chunk": {
                "id": chunk.id,
                "source": chunk.source,
                "content": chunk.content[:150]
            },
            "similar_chunks": [
                {
                    "id": c.id,
                    "source": c.source,
                    "content": c.content[:150],
                    "similarity_score": getattr(c, 'similarity_score', 'N/A')
                }
                for c in similar_chunks
            ],
            "original_chunk_found": original_chunk_found
        }
    
    finally:
        db.close()

async def main():
    parser = argparse.ArgumentParser(description='Test vector similarity search.')
    parser.add_argument('--chunk_id', type=int, required=True, help='ID of the knowledge base chunk to use for testing')
    args = parser.parse_args()
    
    try:
        results = await test_vector_search(args.chunk_id)
        
        # Print summary
        print(f'\nTest Results:')
        print(f'Original chunk: ID {results["original_chunk"]["id"]}, Source: {results["original_chunk"]["source"]}')
        print(f'Original chunk found in results: {results["original_chunk_found"]}')
        
        print(f'\nSimilar chunks:')
        for i, chunk in enumerate(results["similar_chunks"]):
            print(f'{i+1}. ID {chunk["id"]}, Source: {chunk["source"]}')
            print(f'   Similarity score: {chunk["similarity_score"]}')
            print(f'   Content: {chunk["content"]}...')
    
    except Exception as e:
        logger.error(f'Error: {str(e)}')
        sys.exit(1)

if __name__ == '__main__':
    asyncio.run(main())
