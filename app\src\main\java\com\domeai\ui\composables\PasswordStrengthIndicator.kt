package com.domeai.ui.composables

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * Data class representing password strength criteria
 */
data class PasswordCriteria(
    val hasMinLength: Boolean = false,
    val hasUppercase: Boolean = false,
    val hasLowercase: Boolean = false,
    val hasDigit: Boolean = false,
    val hasSpecialChar: Boolean = false
) {
    val strengthPercentage: Float
        get() {
            val criteria = listOf(hasMinLength, hasUppercase, hasLowercase, hasDigit, hasSpecialChar)
            return criteria.count { it } / criteria.size.toFloat()
        }
        
    val isStrong: Boolean
        get() = hasMinLength && hasUppercase && hasLowercase && hasDigit && hasSpecialChar
}

/**
 * Evaluates the strength of a password based on predefined criteria
 */
fun evaluatePasswordStrength(password: String): PasswordCriteria {
    return PasswordCriteria(
        hasMinLength = password.length >= 8,
        hasUppercase = password.any { it.isUpperCase() },
        hasLowercase = password.any { it.isLowerCase() },
        hasDigit = password.any { it.isDigit() },
        hasSpecialChar = password.any { !it.isLetterOrDigit() }
    )
}

/**
 * A composable that displays password strength criteria and a visual indicator
 */
@Composable
fun PasswordStrengthIndicator(
    password: String,
    modifier: Modifier = Modifier
) {
    val criteria = evaluatePasswordStrength(password)
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        // Strength progress indicator
        StrengthProgressBar(criteria.strengthPercentage)
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // Criteria checklist
        CriteriaChecklist(criteria)
    }
}

@Composable
private fun StrengthProgressBar(strengthPercentage: Float) {
    val strengthColor by animateColorAsState(
        targetValue = when {
            strengthPercentage < 0.3f -> MaterialTheme.colorScheme.error
            strengthPercentage < 0.7f -> MaterialTheme.colorScheme.tertiary
            else -> MaterialTheme.colorScheme.primary
        },
        animationSpec = tween(300),
        label = "StrengthColorAnimation"
    )
    
    val strengthText = when {
        strengthPercentage < 0.3f -> "Weak"
        strengthPercentage < 0.7f -> "Moderate"
        strengthPercentage < 1.0f -> "Strong"
        else -> "Very Strong"
    }
    
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Password Strength",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = strengthText,
                style = MaterialTheme.typography.bodyMedium,
                color = strengthColor,
                fontWeight = FontWeight.Medium
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        LinearProgressIndicator(
            progress = { strengthPercentage },
            modifier = Modifier.fillMaxWidth(),
            color = strengthColor,
            trackColor = MaterialTheme.colorScheme.surfaceVariant,
            strokeCap = StrokeCap.Round
        )
    }
}

@Composable
private fun CriteriaChecklist(criteria: PasswordCriteria) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        CriteriaItem(
            text = "At least 8 characters",
            isValid = criteria.hasMinLength
        )
        
        CriteriaItem(
            text = "At least one uppercase letter",
            isValid = criteria.hasUppercase
        )
        
        CriteriaItem(
            text = "At least one lowercase letter",
            isValid = criteria.hasLowercase
        )
        
        CriteriaItem(
            text = "At least one number",
            isValid = criteria.hasDigit
        )
        
        CriteriaItem(
            text = "At least one special character",
            isValid = criteria.hasSpecialChar
        )
    }
}

@Composable
private fun CriteriaItem(
    text: String,
    isValid: Boolean
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth()
    ) {
        Box(
            modifier = Modifier
                .size(18.dp)
                .clip(CircleShape)
                .background(
                    if (isValid) MaterialTheme.colorScheme.primary
                    else MaterialTheme.colorScheme.surfaceVariant
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = if (isValid) Icons.Default.Check else Icons.Default.Close,
                contentDescription = if (isValid) "Criteria met" else "Criteria not met",
                tint = if (isValid) Color.White else MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(12.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall.copy(fontSize = 12.sp),
            color = if (isValid) 
                MaterialTheme.colorScheme.onSurface 
            else 
                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}
