package com.domeai.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.Toast
import androidx.core.app.NotificationCompat
import com.domeai.R
import com.domeai.presentation.main.MainActivity

/**
 * A simple, reliable floating view service implementation
 */
class SimpleFloatingViewService : Service() {

    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var dismissAreaView: View? = null

    // Track if views are attached to window
    private var isFloatingViewAttached = false
    private var isDismissAreaShowing = false

    // For tracking touch events
    private var initialX = 0
    private var initialY = 0
    private var initialTouchX = 0f
    private var initialTouchY = 0f

    // Screen dimensions
    private var screenWidth = 0
    private var screenHeight = 0

    companion object {
        private const val TAG = "SimpleFloatingService"
        private const val NOTIFICATION_CHANNEL_ID = "floating_view_service_channel"
        private const val NOTIFICATION_ID = 1001

        // Intent actions
        const val ACTION_START = "com.domeai.service.ACTION_START_FLOATING"
        const val ACTION_STOP = "com.domeai.service.ACTION_STOP_FLOATING"

        // Intent to start the service
        fun getStartIntent(context: Context): Intent {
            return Intent(context, SimpleFloatingViewService::class.java).apply {
                action = ACTION_START
            }
        }

        // Intent to stop the service
        fun getStopIntent(context: Context): Intent {
            return Intent(context, SimpleFloatingViewService::class.java).apply {
                action = ACTION_STOP
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Service created")
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "onStartCommand: ${intent?.action}")

        when (intent?.action) {
            ACTION_START -> startFloatingView()
            ACTION_STOP -> stopFloatingView()
            else -> startFloatingView()
        }

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        stopFloatingView()
        Log.d(TAG, "Service destroyed")
    }

    private fun startFloatingView() {
        try {
            // Start as foreground service
            startForeground(NOTIFICATION_ID, createNotification())

            // Check for overlay permission
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
                Log.e(TAG, "No overlay permission")
                stopSelf()
                return
            }

            // Remove any existing views first
            removeAllViews()

            // Get screen dimensions
            val displayMetrics = resources.displayMetrics
            screenWidth = displayMetrics.widthPixels
            screenHeight = displayMetrics.heightPixels

            // Create and add the floating view
            createFloatingView()
            addFloatingViewToWindow()

            // Create dismiss area view (but don't add it yet)
            createDismissAreaView()

            Log.d(TAG, "Floating view started successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting floating view: ${e.message}")
            e.printStackTrace()
            stopSelf()
        }
    }

    private fun createFloatingView() {
        // Inflate the floating view layout
        val inflater = LayoutInflater.from(this)
        floatingView = inflater.inflate(R.layout.floating_view, null)

        // Set up touch listener for dragging
        floatingView?.setOnTouchListener { _, event ->
            handleTouchEvent(event)
        }
    }

    private fun addFloatingViewToWindow() {
        if (floatingView == null) return

        try {
            // Create layout parameters
            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                },
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT
            ).apply {
                gravity = Gravity.TOP or Gravity.END
                x = 0  // With END gravity, x=0 means right edge
                y = 100  // Position near the top
            }

            // Add the view to the window manager
            windowManager?.addView(floatingView, params)
            isFloatingViewAttached = true
            Log.d(TAG, "Floating view added to window")
        } catch (e: Exception) {
            Log.e(TAG, "Error adding floating view: ${e.message}")
            isFloatingViewAttached = false
        }
    }

    private fun createDismissAreaView() {
        // Inflate the dismiss area layout
        val inflater = LayoutInflater.from(this)
        dismissAreaView = inflater.inflate(R.layout.dismiss_area, null)
    }

    private fun showDismissArea() {
        if (dismissAreaView == null || isDismissAreaShowing) return

        try {
            // Create layout parameters
            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                },
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                PixelFormat.TRANSLUCENT
            ).apply {
                gravity = Gravity.BOTTOM
            }

            // Add the view to the window manager
            windowManager?.addView(dismissAreaView, params)
            isDismissAreaShowing = true
            Log.d(TAG, "Dismiss area shown")
        } catch (e: Exception) {
            Log.e(TAG, "Error showing dismiss area: ${e.message}")
            isDismissAreaShowing = false
        }
    }

    private fun hideDismissArea() {
        if (dismissAreaView == null || !isDismissAreaShowing) return

        try {
            windowManager?.removeView(dismissAreaView)
            isDismissAreaShowing = false
            Log.d(TAG, "Dismiss area hidden")
        } catch (e: Exception) {
            Log.e(TAG, "Error hiding dismiss area: ${e.message}")
        }
    }

    private fun handleTouchEvent(event: MotionEvent): Boolean {
        try {
            if (floatingView == null) return false

            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    try {
                        // Get the current position of the view
                        val layoutParams = floatingView?.layoutParams as? WindowManager.LayoutParams
                        if (layoutParams == null) {
                            Log.e(TAG, "Layout params are null in ACTION_DOWN")
                            return false
                        }

                        // Save initial position
                        initialX = layoutParams.x
                        initialY = layoutParams.y
                        initialTouchX = event.rawX
                        initialTouchY = event.rawY

                        return true
                    } catch (e: Exception) {
                        Log.e(TAG, "Error in ACTION_DOWN: ${e.message}")
                        return false
                    }
                }

                MotionEvent.ACTION_MOVE -> {
                    try {
                        // Get the current position of the view
                        val layoutParams = floatingView?.layoutParams as? WindowManager.LayoutParams
                        if (layoutParams == null) {
                            Log.e(TAG, "Layout params are null in ACTION_MOVE")
                            return false
                        }

                        // Calculate new position
                        layoutParams.x = initialX + (event.rawX - initialTouchX).toInt()
                        layoutParams.y = initialY + (event.rawY - initialTouchY).toInt()

                        // Update the view position
                        try {
                            if (isFloatingViewAttached && floatingView != null) {
                                windowManager?.updateViewLayout(floatingView, layoutParams)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error updating view position: ${e.message}")
                        }

                        // Check if we're near the bottom of the screen
                        val dismissThreshold = screenHeight - 300
                        val isNearBottom = event.rawY > dismissThreshold

                        // Show or hide dismiss area based on position
                        if (isNearBottom && !isDismissAreaShowing) {
                            showDismissArea()
                        } else if (!isNearBottom && isDismissAreaShowing) {
                            hideDismissArea()
                        }

                        // Check if we're in the dismiss area
                        if (isDismissAreaShowing) {
                            val inDismissArea = event.rawY > (screenHeight - 200)

                            // Highlight the dismiss area if we're in it
                            try {
                                if (inDismissArea) {
                                    dismissAreaView?.findViewById<ImageView>(R.id.dismiss_icon)?.setColorFilter(
                                        android.graphics.Color.RED, android.graphics.PorterDuff.Mode.SRC_IN
                                    )
                                } else {
                                    dismissAreaView?.findViewById<ImageView>(R.id.dismiss_icon)?.clearColorFilter()
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "Error updating dismiss area: ${e.message}")
                            }
                        }

                        return true
                    } catch (e: Exception) {
                        Log.e(TAG, "Error in ACTION_MOVE: ${e.message}")
                        return false
                    }
                }

                MotionEvent.ACTION_UP -> {
                    try {
                        // Hide dismiss area
                        hideDismissArea()

                        // Check if we're in the dismiss area
                        val inDismissArea = event.rawY > (screenHeight - 200)

                        if (inDismissArea) {
                            // Permanently hide the button
                            stopFloatingView()
                            return true
                        }

                        // Check if it was moved or just clicked
                        val moved = Math.abs(event.rawX - initialTouchX) > 10 ||
                                Math.abs(event.rawY - initialTouchY) > 10

                        if (moved) {
                            // Snap to nearest edge
                            snapToEdge()
                        } else {
                            // Handle click - open the app
                            try {
                                val intent = Intent(this, MainActivity::class.java).apply {
                                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                }
                                startActivity(intent)
                            } catch (e: Exception) {
                                Log.e(TAG, "Error starting activity: ${e.message}")
                            }
                        }

                        return true
                    } catch (e: Exception) {
                        Log.e(TAG, "Error in ACTION_UP: ${e.message}")
                        return false
                    }
                }

                else -> return false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in handleTouchEvent: ${e.message}")
            return false
        }
    }

    private fun snapToEdge() {
        if (floatingView == null) return

        try {
            val layoutParams = floatingView?.layoutParams as? WindowManager.LayoutParams ?: return

            // Get button dimensions
            val buttonWidth = floatingView?.width ?: 100
            val buttonHeight = floatingView?.height ?: 100

            // Calculate distances to edges
            val distanceToLeft = layoutParams.x
            val distanceToRight = screenWidth - (layoutParams.x + buttonWidth)
            val distanceToTop = layoutParams.y
            val distanceToBottom = screenHeight - (layoutParams.y + buttonHeight)

            // Find the closest edge
            val minHorizontalDistance = Math.min(distanceToLeft, distanceToRight)
            val minVerticalDistance = Math.min(distanceToTop, distanceToBottom)

            // Snap to the closest edge
            if (minHorizontalDistance < minVerticalDistance) {
                // Snap horizontally
                if (distanceToLeft < distanceToRight) {
                    // Snap to left
                    layoutParams.x = 0
                } else {
                    // Snap to right
                    layoutParams.x = screenWidth - buttonWidth
                }
            } else {
                // Snap vertically
                if (distanceToTop < distanceToBottom) {
                    // Snap to top
                    layoutParams.y = 0
                } else {
                    // Snap to bottom
                    layoutParams.y = screenHeight - buttonHeight
                }
            }

            // Update the view position
            windowManager?.updateViewLayout(floatingView, layoutParams)
        } catch (e: Exception) {
            Log.e(TAG, "Error snapping to edge: ${e.message}")
        }
    }

    private fun stopFloatingView() {
        removeAllViews()

        // Stop the foreground service
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            if (Build.VERSION.SDK_INT >= 31) { // Android 12+
                stopForeground(Service.STOP_FOREGROUND_REMOVE)
            } else {
                stopForeground(true)
            }
        } else {
            stopForeground(true)
        }

        stopSelf()
        Log.d(TAG, "Floating view stopped")
    }

    private fun removeAllViews() {
        // Remove floating view
        if (isFloatingViewAttached && floatingView != null) {
            try {
                windowManager?.removeView(floatingView)
                isFloatingViewAttached = false
            } catch (e: Exception) {
                Log.e(TAG, "Error removing floating view: ${e.message}")
            }
        }

        // Remove dismiss area
        if (isDismissAreaShowing && dismissAreaView != null) {
            try {
                windowManager?.removeView(dismissAreaView)
                isDismissAreaShowing = false
            } catch (e: Exception) {
                Log.e(TAG, "Error removing dismiss area: ${e.message}")
            }
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Floating View Service"
            val descriptionText = "Shows a floating button for scanning"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(NOTIFICATION_CHANNEL_ID, name, importance).apply {
                description = descriptionText
                setShowBadge(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): android.app.Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setContentTitle("DomeAI Scam Detector")
            .setContentText("Overlay protection is active")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .build()
    }
}
