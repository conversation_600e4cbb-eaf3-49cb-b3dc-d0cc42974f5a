#!/usr/bin/env python
"""
Script to test pgvector functionality only.
"""

import logging
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy import text
from app.core.database import SessionLocal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def test_inner_product():
    """Test inner product similarity search."""
    logger.info("Testing inner product similarity search")

    # Use the existing database connection from the app
    session = SessionLocal()

    try:
        # Check if the pgvector extension is installed
        result = session.execute(text("SELECT * FROM pg_extension WHERE extname = 'vector'"))
        extensions = result.fetchall()
        logger.info(f"Found {len(extensions)} extensions with name 'vector'")

        # Get a sample chunk
        result = session.execute(text("SELECT id, content, source FROM knowledge_base_chunks LIMIT 1"))
        sample = result.fetchone()
        if sample:
            sample_id, sample_content, sample_source = sample
            logger.info(f"Sample chunk: ID={sample_id}, Source={sample_source}")
            logger.info(f"Content: {sample_content[:100]}...")

            # Get the embedding for this chunk
            result = session.execute(text("SELECT embedding FROM knowledge_base_chunks WHERE id = :id"), {"id": sample_id})
            embedding_row = result.fetchone()
            if embedding_row and embedding_row[0]:
                embedding = embedding_row[0]
                logger.info(f"Embedding type: {type(embedding)}")
                logger.info(f"Embedding length: {len(embedding)}")

                # Use the embedding to find similar chunks with inner product
                result = session.execute(text("""
                    SELECT id, content, source, -1 * (embedding <#> :embedding) AS similarity_score
                    FROM knowledge_base_chunks
                    ORDER BY embedding <#> :embedding ASC
                    LIMIT 5
                """), {"embedding": embedding})

                results = result.fetchall()
                logger.info(f"Found {len(results)} similar chunks using inner product")
                for i, (id, content, source, similarity) in enumerate(results):
                    logger.info(f"Similar chunk {i+1}: ID={id}, Source={source}, Similarity={similarity:.4f}")
                    logger.info(f"Content: {content[:100]}...")

                # Use the embedding to find similar chunks with cosine distance
                result = session.execute(text("""
                    SELECT id, content, source, 1 - (embedding <-> :embedding)/2 AS similarity_score
                    FROM knowledge_base_chunks
                    ORDER BY embedding <-> :embedding
                    LIMIT 5
                """), {"embedding": embedding})

                results = result.fetchall()
                logger.info(f"Found {len(results)} similar chunks using cosine distance")
                for i, (id, content, source, similarity) in enumerate(results):
                    logger.info(f"Similar chunk {i+1}: ID={id}, Source={source}, Similarity={similarity:.4f}")
                    logger.info(f"Content: {content[:100]}...")

                # Find chunks with "cashier_check" in the source
                result = session.execute(text("""
                    SELECT id, content, source FROM knowledge_base_chunks
                    WHERE source LIKE '%cashier_check%'
                    LIMIT 3
                """))

                cashier_check_chunks = result.fetchall()
                logger.info(f"Found {len(cashier_check_chunks)} chunks with 'cashier_check' in source")

                if cashier_check_chunks:
                    cashier_id, cashier_content, cashier_source = cashier_check_chunks[0]
                    logger.info(f"Cashier check chunk: ID={cashier_id}, Source={cashier_source}")
                    logger.info(f"Content: {cashier_content[:100]}...")

                    # Get the embedding for the cashier check chunk
                    result = session.execute(text("SELECT embedding FROM knowledge_base_chunks WHERE id = :id"), {"id": cashier_id})
                    cashier_embedding_row = result.fetchone()
                    if cashier_embedding_row and cashier_embedding_row[0]:
                        cashier_embedding = cashier_embedding_row[0]

                        # Use the cashier check embedding to find similar chunks with inner product
                        result = session.execute(text("""
                            SELECT id, content, source, -1 * (embedding <#> :embedding) AS similarity_score
                            FROM knowledge_base_chunks
                            ORDER BY embedding <#> :embedding ASC
                            LIMIT 5
                        """), {"embedding": cashier_embedding})

                        results = result.fetchall()
                        logger.info(f"Found {len(results)} similar chunks to cashier check using inner product")
                        for i, (id, content, source, similarity) in enumerate(results):
                            logger.info(f"Similar chunk {i+1}: ID={id}, Source={source}, Similarity={similarity:.4f}")
                            logger.info(f"Content: {content[:100]}...")

                        # Check if the first result is the cashier check chunk itself
                        if results and results[0][0] == cashier_id:
                            logger.info("Self-query test PASSED: First result is the query chunk itself")
                            similarity = results[0][3]
                            logger.info(f"Self-similarity score: {similarity:.4f}")
                            if abs(similarity - 1.0) < 0.01:
                                logger.info("Self-similarity score is close to 1.0 as expected")
                            else:
                                logger.warning(f"Self-similarity score is {similarity:.4f}, expected close to 1.0")
                        else:
                            logger.error("Self-query test FAILED: First result is not the query chunk itself")
                    else:
                        logger.error("No embedding found for cashier check chunk")
                else:
                    logger.error("No chunks found with 'cashier_check' in source")
            else:
                logger.error("No embedding found for sample chunk")
        else:
            logger.error("No chunks found in the database")

    except Exception as e:
        logger.error(f"Error in test_inner_product: {str(e)}")

    finally:
        session.close()

if __name__ == "__main__":
    test_inner_product()
