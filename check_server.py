"""
Check if the server is running.
"""
import requests
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Server URL
server_url = "http://localhost:8000/"

# Send the request
logger.info(f"Checking if server is running at {server_url}")
try:
    response = requests.get(server_url)
    logger.info(f"Status code: {response.status_code}")
    logger.info(f"Response: {response.text}")
except Exception as e:
    logger.error(f"Error connecting to server: {str(e)}")
