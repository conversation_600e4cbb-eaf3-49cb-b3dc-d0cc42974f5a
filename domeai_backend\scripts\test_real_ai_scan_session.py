#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test the scan session feature with real AI analysis on a multi-image conversational scam scenario.
"""

import logging
import sys
import uuid
import os
import time
import json
import re
import requests
import asyncio
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan_session import ScanSession
from app.models.scan import Scan
from app.crud import crud_scan_session, crud_scan
from app.tasks.scan_tasks import process_scan_task
from app.services.ai_service_factory import get_ai_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Google Drive image URLs
IMAGE_URLS = [
    "https://drive.google.com/file/d/1Rbdq60DXO8Fvvz9XHJnUWkEw29GFXzeC/view?usp=sharing",
    "https://drive.google.com/file/d/1-niQevQraY1PJzu9m79-LM-LwRGUUtxE/view?usp=sharing",
    "https://drive.google.com/file/d/1dQuE-aFYIHKHg6ZV6EoRTQw3QPXTKijX/view?usp=sharing",
    "https://drive.google.com/file/d/1fxdt5H_kfGHO2XaHQSMx7NfNFJoYMDtV/view?usp=sharing"
]

# User context for the first scan
USER_CONTEXT = """They almost got me. My intuition is almost on point. I was so close too. Holy Shit. My intuition is "almost" on point. I finally got one. So I'm on a "dating" app. I matched with a girl in my area. She seemed a little too good to be true. So I of course proceeded with caution. When she asked me personal details, I didn't tell her much. My big mistake was to add her on my personal instagram. Onto the good part. She started getting sexual immediately. I told her I don't send pics until I meet the person. This upset her because she had already sent pics over dm. So I sent her a couple fully clothed pics of me in the mirror. This upset her, she wanted a "limp dick" picture. Red flag, what girl wants to see it limp. 😂😂 After she asked for my number and I turned that down, she turned down my offer to video call. Here's a few of the chats. I warned the people on my Instagram about a potential fake dick pic incoming. Will update if this gets more interesting."""

def download_image_from_gdrive(url: str, output_path: str) -> str:
    """
    Download an image from Google Drive.

    Args:
        url: Google Drive URL
        output_path: Directory to save the image

    Returns:
        Path to the downloaded image
    """
    try:
        # Extract file ID from Google Drive URL
        file_id = re.search(r'\/d\/(.*?)\/view', url)
        if not file_id:
            logger.error(f"Could not extract file ID from URL: {url}")
            return None
        file_id = file_id.group(1)

        # Create direct download link
        download_url = f"https://drive.google.com/uc?export=download&id={file_id}"

        # Create output directory if it doesn't exist
        os.makedirs(output_path, exist_ok=True)

        # Download the file
        local_filename = os.path.join(output_path, f"image_{file_id}.jpg")

        # Download with requests
        response = requests.get(download_url, stream=True)
        if response.status_code != 200:
            logger.error(f"Failed to download image: {response.status_code}")
            # Create a placeholder file instead
            with open(local_filename, 'w') as f:
                f.write(f"Placeholder for {url}")
            return local_filename

        with open(local_filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        logger.info(f"Downloaded image to {local_filename}")
        return local_filename
    except Exception as e:
        logger.error(f"Error downloading image: {str(e)}")
        # Create a placeholder file
        local_filename = os.path.join(output_path, f"placeholder_{int(time.time())}.jpg")
        with open(local_filename, 'w') as f:
            f.write(f"Placeholder for {url}")
        return local_filename

def create_premium_user(db: Session) -> User:
    """
    Create a premium tier test user.

    Args:
        db: Database session

    Returns:
        The created user
    """
    # Create a test user with premium tier
    unique_email = f"test_premium_user_{int(time.time())}@example.com"

    test_user = User(
        email=unique_email,
        hashed_password="hashed_password",
        subscription_tier="premium",
        monthly_scan_allowance=100,
        scans_this_month=0,
        scan_counter_reset_at=datetime.now(timezone.utc)
    )

    # Add the user to the database
    db.add(test_user)
    db.commit()
    db.refresh(test_user)

    logger.info(f"Created premium test user with ID {test_user.id}")
    return test_user

def create_scan_session(db: Session, user_id: int) -> ScanSession:
    """
    Create a scan session.

    Args:
        db: Database session
        user_id: ID of the user who owns the session

    Returns:
        The created scan session
    """
    # Create a scan session
    session = crud_scan_session.create_scan_session(
        db=db, owner_id=user_id, title="Reddit Conversational Scam Test"
    )

    logger.info(f"Created scan session with ID {session.id}")
    return session

async def process_image_with_real_ai(db: Session, scan_id: int, user_tier: str, is_expert_scan: bool) -> None:
    """
    Process an image scan with the real AI service.

    Args:
        db: Database session
        scan_id: ID of the scan to process
        user_tier: User's subscription tier
        is_expert_scan: Whether this is an expert scan
    """
    logger.info(f"Processing scan {scan_id} with real AI service")

    # Get the scan from the database
    scan = db.query(Scan).filter(Scan.id == scan_id).first()
    if not scan:
        logger.error(f"Scan {scan_id} not found")
        return

    # Update scan status to "processing"
    scan.status = "processing"
    db.add(scan)
    db.commit()
    db.refresh(scan)

    # Check if this scan is part of a session
    previous_scans_in_session = []
    if scan.scan_session_id:
        logger.info(f"Scan {scan_id} is part of session {scan.scan_session_id}")

        # Get previous scans in this session
        previous_scans_in_session = crud_scan_session.get_previous_scans_in_session(
            db=db,
            session_id=scan.scan_session_id,
            current_scan_id=scan.id
        )

        # Log information about previous scans
        logger.info(f"Found {len(previous_scans_in_session)} previous scans in this session")

        # Log details of previous scans
        for prev_scan in previous_scans_in_session:
            source = prev_scan.input_content_type

            # Extract text snippet if available
            text_snippet = None
            if prev_scan.analysis_result and "extracted_text" in prev_scan.analysis_result:
                text_snippet = prev_scan.analysis_result["extracted_text"][:100] + "..."
            elif prev_scan.input_text:
                text_snippet = prev_scan.input_text[:100] + "..."
            else:
                text_snippet = "No text available"

            logger.info(f"Previous scan {prev_scan.id} - Source: {source}, Text: {text_snippet}")

    try:
        # Get the appropriate AI service based on user's subscription tier
        ai_service = get_ai_service(user_tier=user_tier)

        # Process the scan with the real AI service
        if scan.input_content_type == "image_path":
            # Get the file path from the raw_input_payload
            file_path = scan.raw_input_payload.get("file_path")
            if not file_path:
                raise ValueError("File path not found in raw_input_payload")

            # Get multimodal analysis
            logger.info(f"Getting multimodal analysis for image at {file_path}")
            multimodal_result = await ai_service.get_multimodal_analysis(
                image_path=file_path
            )

            # Extract text and get embedding
            extracted_text = multimodal_result.get("extracted_text", "")
            logger.info(f"Getting text embedding for extracted text: {extracted_text[:100]}...")
            embedding = await ai_service.get_text_embedding(text=extracted_text)

            # Perform RAG analysis
            logger.info(f"Performing RAG analysis")
            rag_result = await ai_service.perform_scam_analysis_with_rag(
                query_text=extracted_text,
                query_embedding=embedding,
                original_image_description=multimodal_result.get("image_description", ""),
                original_platform_identified=multimodal_result.get("platform_identified", "")
            )

            # Combine results
            analysis_result = {
                **multimodal_result,
                **rag_result
            }

            # Log the KB chunks used in RAG
            if "kb_chunks_used" in rag_result:
                logger.info(f"KB chunks used in RAG:")
                for i, chunk in enumerate(rag_result["kb_chunks_used"]):
                    logger.info(f"  Chunk {i+1}: {chunk.get('source', 'Unknown source')}")

            # Update scan with result
            scan.status = "completed"
            scan.analysis_result = analysis_result
            db.add(scan)
            db.commit()
            db.refresh(scan)

            logger.info(f"Completed processing of scan {scan_id}")
            logger.info(f"Analysis result: Risk Score: {analysis_result.get('risk_score', 'N/A')}, Red Flags: {analysis_result.get('detected_red_flags', [])}")

        else:
            logger.error(f"Unsupported input content type: {scan.input_content_type}")
            scan.status = "failed"
            scan.error_message = f"Unsupported input content type: {scan.input_content_type}"
            db.add(scan)
            db.commit()

    except Exception as e:
        logger.error(f"Error processing scan {scan_id}: {str(e)}")
        scan.status = "failed"
        scan.error_message = str(e)
        db.add(scan)
        db.commit()

async def submit_image_scan(
    db: Session,
    user_id: int,
    image_path: str,
    session_id: uuid.UUID,
    user_provided_context: Optional[str] = None,
    is_session_followup: bool = False
) -> Scan:
    """
    Submit an image scan and process it with the real AI service.

    Args:
        db: Database session
        user_id: ID of the user who owns the scan
        image_path: Path to the image file
        session_id: ID of the scan session
        user_provided_context: Optional user-provided context
        is_session_followup: Whether this is a followup scan in a session

    Returns:
        The created and processed scan
    """
    # Create a scan
    db_scan = Scan(
        owner_id=user_id,
        status="pending",
        input_content_type="image_path",
        user_provided_context=user_provided_context,
        raw_input_payload={"file_path": image_path, "is_session_followup": is_session_followup},
        scan_session_id=session_id
    )

    # Add the scan to the database
    db.add(db_scan)
    db.commit()
    db.refresh(db_scan)

    logger.info(f"Created scan with ID {db_scan.id}")

    # Update session activity
    session = crud_scan_session.get_scan_session(db=db, session_id=session_id, owner_id=user_id)
    if session:
        crud_scan_session.update_scan_session_activity(db=db, db_session=session)
        logger.info(f"Updated session activity timestamp to {session.last_activity_at}")

    # Process the scan with the real AI service
    await process_image_with_real_ai(db, db_scan.id, "premium", False)

    # Refresh the scan to get the updated analysis_result
    db.refresh(db_scan)

    return db_scan

async def test_real_ai_scan_session():
    """Test the scan session feature with real AI analysis on the Reddit conversational scam scenario."""
    logger.info("Testing Scan Session Feature with Real AI Analysis on Reddit Conversational Scam Scenario")

    # Create a database session
    db = SessionLocal()
    try:
        # Create a premium tier test user
        test_user = create_premium_user(db)

        # Download the test images
        image_paths = []
        for i, url in enumerate(IMAGE_URLS):
            image_path = download_image_from_gdrive(url, "temp_images")
            image_paths.append(image_path)

        # Create a scan session explicitly (Step 1)
        session = create_scan_session(db, test_user.id)
        session_id = session.id

        # Track scan credits before
        scans_before = test_user.scans_this_month
        logger.info(f"Scans before: {scans_before}")

        # Submit Image 1 (Step 2)
        logger.info("Submitting Image 1")
        scan1 = await submit_image_scan(
            db,
            test_user.id,
            image_paths[0],
            session_id=session_id,
            user_provided_context=USER_CONTEXT
        )

        # Update user scan count for first scan
        test_user.scans_this_month += 1
        db.commit()
        db.refresh(test_user)

        # Verify scan credit consumption
        logger.info(f"Scans after Image 1: {test_user.scans_this_month}")
        logger.info(f"Scan credit consumed: {test_user.scans_this_month - scans_before}")

        # Submit Image 2 (Step 3)
        logger.info("Submitting Image 2")
        scan2 = await submit_image_scan(
            db,
            test_user.id,
            image_paths[1],
            session_id=session_id,
            is_session_followup=True
        )

        # Verify scan credit consumption (should not increase for premium user)
        db.refresh(test_user)
        logger.info(f"Scans after Image 2: {test_user.scans_this_month}")
        logger.info(f"Scan credit consumed for second scan: {test_user.scans_this_month - (scans_before + 1)}")

        # Submit Image 3 (Step 4)
        logger.info("Submitting Image 3")
        scan3 = await submit_image_scan(
            db,
            test_user.id,
            image_paths[2],
            session_id=session_id,
            is_session_followup=True
        )

        # Verify scan credit consumption (should not increase for premium user)
        db.refresh(test_user)
        logger.info(f"Scans after Image 3: {test_user.scans_this_month}")
        logger.info(f"Scan credit consumed for third scan: {test_user.scans_this_month - (scans_before + 1)}")

        # Submit Image 4 (Step 5)
        logger.info("Submitting Image 4")
        scan4 = await submit_image_scan(
            db,
            test_user.id,
            image_paths[3],
            session_id=session_id,
            is_session_followup=True
        )

        # Verify scan credit consumption (should not increase for premium user)
        db.refresh(test_user)
        logger.info(f"Scans after Image 4: {test_user.scans_this_month}")
        logger.info(f"Total scan credits consumed: {test_user.scans_this_month - scans_before}")

        # Retrieve the final session details
        final_session = crud_scan_session.get_scan_session(
            db=db, session_id=session_id, owner_id=test_user.id
        )

        logger.info(f"Final session details:")
        logger.info(f"Session ID: {final_session.id}")
        logger.info(f"Session title: {final_session.title}")
        logger.info(f"Session created at: {final_session.created_at}")
        logger.info(f"Session last activity at: {final_session.last_activity_at}")

        # Verify all scans are associated with this session
        session_scans = db.query(Scan).filter(Scan.scan_session_id == session_id).all()
        logger.info(f"Number of scans in session: {len(session_scans)}")
        logger.info(f"Scan IDs in session: {[scan.id for scan in session_scans]}")

        # Print detailed analysis results for each scan
        for i, scan in enumerate(session_scans):
            logger.info(f"\nDetailed Analysis for Scan {i+1} (ID: {scan.id}):")
            if scan.analysis_result:
                logger.info(f"Risk Score: {scan.analysis_result.get('risk_score', 'N/A')}")
                logger.info(f"Explanation: {scan.analysis_result.get('explanation', 'N/A')}")
                logger.info(f"Red Flags: {scan.analysis_result.get('detected_red_flags', [])}")
                logger.info(f"Recommendations: {scan.analysis_result.get('recommendations', 'N/A')}")
                logger.info(f"Confidence Level: {scan.analysis_result.get('confidence_level', 'N/A')}")
                logger.info(f"Model Used: {scan.analysis_result.get('model_used', 'N/A')}")
            else:
                logger.info(f"No analysis result available")

        # Clean up
        for scan in session_scans:
            db.delete(scan)
        db.delete(final_session)
        db.delete(test_user)
        db.commit()
        logger.info("Test data deleted")

        # Clean up downloaded images
        for image_path in image_paths:
            if os.path.exists(image_path):
                os.remove(image_path)
                logger.info(f"Deleted temporary image: {image_path}")

    finally:
        db.close()

def main():
    """Run the test."""
    asyncio.run(test_real_ai_scan_session())

if __name__ == "__main__":
    main()
