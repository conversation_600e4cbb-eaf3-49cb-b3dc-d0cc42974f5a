package com.domeai.presentation.settings

import androidx.lifecycle.viewModelScope
import com.domeai.data.repository.AuthRepository
import com.domeai.domain.validation.Validator
import com.domeai.presentation.common.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * UI State for Change Password Screen
 */
data class ChangePasswordUiState(
    val currentPassword: String = "",
    val newPassword: String = "",
    val confirmNewPassword: String = "",
    val currentPasswordError: String? = null,
    val newPasswordError: String? = null,
    val confirmNewPasswordError: String? = null,
    val isLoading: Boolean = false,
    val isPasswordChanged: Boolean = false
) : com.domeai.presentation.common.UiState

/**
 * UI Events for Change Password Screen
 */
sealed class ChangePasswordUiEvent : com.domeai.presentation.common.UiEvent {
    data object NavigateBack : ChangePasswordUiEvent()
    data class ShowSnackbar(val message: String) : ChangePasswordUiEvent()
}

/**
 * UI Actions for Change Password Screen
 */
sealed class ChangePasswordUiAction : com.domeai.presentation.common.UiAction {
    data object NavigateBack : ChangePasswordUiAction()
    data class UpdateCurrentPassword(val password: String) : ChangePasswordUiAction()
    data class UpdateNewPassword(val password: String) : ChangePasswordUiAction()
    data class UpdateConfirmNewPassword(val password: String) : ChangePasswordUiAction()
    data object ChangePassword : ChangePasswordUiAction()
}

/**
 * ViewModel for Change Password Screen
 */
@HiltViewModel
class ChangePasswordViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val validator: Validator
) : BaseViewModel<ChangePasswordUiState, ChangePasswordUiEvent, ChangePasswordUiAction>() {

    override fun createInitialState(): ChangePasswordUiState = ChangePasswordUiState()

    override fun handleAction(action: ChangePasswordUiAction) {
        when (action) {
            is ChangePasswordUiAction.NavigateBack -> navigateBack()
            is ChangePasswordUiAction.UpdateCurrentPassword -> updateCurrentPassword(action.password)
            is ChangePasswordUiAction.UpdateNewPassword -> updateNewPassword(action.password)
            is ChangePasswordUiAction.UpdateConfirmNewPassword -> updateConfirmNewPassword(action.password)
            is ChangePasswordUiAction.ChangePassword -> changePassword()
        }
    }

    private fun updateCurrentPassword(password: String) {
        updateState { it.copy(currentPassword = password, currentPasswordError = null) }
    }

    private fun updateNewPassword(password: String) {
        updateState { it.copy(newPassword = password, newPasswordError = null) }
    }

    private fun updateConfirmNewPassword(password: String) {
        updateState { it.copy(confirmNewPassword = password, confirmNewPasswordError = null) }
    }

    private fun navigateBack() {
        sendEvent(ChangePasswordUiEvent.NavigateBack)
    }

    private fun changePassword() {
        val currentState = uiState.value

        // Validate inputs
        val currentPasswordError = validator.validatePassword(currentState.currentPassword)
        val newPasswordError = validator.validatePassword(currentState.newPassword)
        val confirmNewPasswordError = if (currentState.newPassword != currentState.confirmNewPassword) {
            "Passwords do not match"
        } else null

        // Update state with validation errors
        updateState {
            it.copy(
                currentPasswordError = currentPasswordError,
                newPasswordError = newPasswordError,
                confirmNewPasswordError = confirmNewPasswordError
            )
        }

        // If there are validation errors, don't proceed
        if (currentPasswordError != null || newPasswordError != null || confirmNewPasswordError != null) {
            return
        }

        // Show loading state
        updateState { it.copy(isLoading = true) }

        // Attempt to change password
        viewModelScope.launch {
            try {
                // In a real app, this would call the repository to change the password
                // For now, we'll simulate a successful password change
                // authRepository.changePassword(currentState.currentPassword, currentState.newPassword)

                // Simulate network delay
                kotlinx.coroutines.delay(1000)

                // Update state to indicate success
                updateState { it.copy(
                    isLoading = false,
                    isPasswordChanged = true,
                    currentPassword = "",
                    newPassword = "",
                    confirmNewPassword = ""
                ) }

                // Show success message
                sendEvent(ChangePasswordUiEvent.ShowSnackbar("Password updated successfully"))

                // Navigate back after a short delay
                kotlinx.coroutines.delay(500)
                navigateBack()

            } catch (e: Exception) {
                // Handle error
                updateState { it.copy(isLoading = false) }
                sendEvent(ChangePasswordUiEvent.ShowSnackbar("Failed to update password: ${e.message}"))
            }
        }
    }
}
