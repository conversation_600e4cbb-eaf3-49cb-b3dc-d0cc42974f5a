#!/usr/bin/env python
"""
Scrip<PERSON> to add new knowledge base chunks to the database.

Usage:
    python add_kb_chunks.py --chunks_file chunks.json

The chunks_file should be a JSON file containing a list of dictionaries, where each dictionary
has 'content' and 'source' keys.

Example chunks.json:
[
    {
        "content": "This is the content of the first chunk.",
        "source": "scam_knowledge_base/example_chunk_1.txt"
    },
    {
        "content": "This is the content of the second chunk.",
        "source": "scam_knowledge_base/example_chunk_2.txt"
    }
]

Alternatively, you can use the script programmatically by importing the add_kb_chunks function.
"""

import argparse
import asyncio
import json
import logging
import sys
from typing import Dict, List, Any, Optional

# Add the parent directory to the path so we can import from app
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.ai_services import OpenAIModelService
from app.core.config import settings
from app.crud import crud_kb

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

async def add_kb_chunks(chunks: List[Dict[str, str]], db: Optional[Session] = None) -> Dict[str, Any]:
    """
    Add knowledge base chunks to the database.
    
    Args:
        chunks: List of dictionaries, where each dictionary has 'content' and 'source' keys.
        db: Database session. If None, a new session will be created.
        
    Returns:
        Dictionary with results, including successful and failed chunks.
    """
    # Create a database session if not provided
    close_db = False
    if db is None:
        db = SessionLocal()
        close_db = True
    
    try:
        # Initialize the OpenAI service
        ai_service = OpenAIModelService(api_key=settings.OPENAI_API_KEY)
        
        logger.info(f'Adding {len(chunks)} knowledge base chunks...')
        
        results = {
            'successful': [],
            'failed': [],
            'total': len(chunks)
        }
        
        for i, chunk_data in enumerate(chunks):
            try:
                content = chunk_data.get('content', '').strip()
                source = chunk_data.get('source', '')
                
                if not content:
                    logger.warning(f'Skipping chunk {i+1}/{len(chunks)}: Empty content')
                    results['failed'].append({
                        'index': i,
                        'source': source,
                        'error': 'Empty content'
                    })
                    continue
                
                logger.info(f'Processing chunk {i+1}/{len(chunks)}: {source}')
                
                # Generate embedding for the chunk
                logger.info(f'Generating embedding for chunk {i+1}...')
                try:
                    embedding = await ai_service.get_text_embedding(text=content)
                except Exception as e:
                    logger.error(f'Error generating embedding for chunk {i+1}: {str(e)}')
                    results['failed'].append({
                        'index': i,
                        'source': source,
                        'error': f'Embedding generation failed: {str(e)}'
                    })
                    continue
                
                # Create the knowledge base chunk
                logger.info(f'Creating knowledge base chunk {i+1}...')
                try:
                    chunk = crud_kb.create_kb_chunk(
                        db=db,
                        content=content,
                        embedding=embedding,
                        source=source
                    )
                    logger.info(f'Created knowledge base chunk with ID {chunk.id}')
                    results['successful'].append({
                        'index': i,
                        'source': source,
                        'id': chunk.id
                    })
                except Exception as e:
                    logger.error(f'Error creating knowledge base chunk {i+1}: {str(e)}')
                    results['failed'].append({
                        'index': i,
                        'source': source,
                        'error': f'Database insertion failed: {str(e)}'
                    })
                    continue
            except Exception as e:
                logger.error(f'Unexpected error processing chunk {i+1}: {str(e)}')
                results['failed'].append({
                    'index': i,
                    'source': chunk_data.get('source', 'unknown'),
                    'error': f'Unexpected error: {str(e)}'
                })
        
        logger.info(f'Knowledge base population completed: {len(results["successful"])} successful, {len(results["failed"])} failed')
        return results
    
    finally:
        if close_db:
            db.close()

async def main():
    parser = argparse.ArgumentParser(description='Add knowledge base chunks to the database.')
    parser.add_argument('--chunks_file', type=str, required=True, help='Path to JSON file containing chunks')
    args = parser.parse_args()
    
    try:
        with open(args.chunks_file, 'r') as f:
            chunks = json.load(f)
        
        if not isinstance(chunks, list):
            logger.error('Chunks file must contain a JSON array')
            sys.exit(1)
        
        results = await add_kb_chunks(chunks)
        
        # Print summary
        print(f'\nSummary:')
        print(f'Total chunks: {results["total"]}')
        print(f'Successful: {len(results["successful"])}')
        print(f'Failed: {len(results["failed"])}')
        
        if results['failed']:
            print('\nFailed chunks:')
            for failed in results['failed']:
                print(f'  - {failed["source"]}: {failed["error"]}')
    
    except FileNotFoundError:
        logger.error(f'Chunks file not found: {args.chunks_file}')
        sys.exit(1)
    except json.JSONDecodeError:
        logger.error(f'Invalid JSON in chunks file: {args.chunks_file}')
        sys.exit(1)
    except Exception as e:
        logger.error(f'Unexpected error: {str(e)}')
        sys.exit(1)

if __name__ == '__main__':
    asyncio.run(main())
