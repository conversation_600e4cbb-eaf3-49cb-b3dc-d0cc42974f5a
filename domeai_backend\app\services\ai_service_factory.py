"""
AI Service Factory module.

This module provides a factory for creating AI service instances based on the user's subscription tier.
"""

from typing import Optional

from app.core.config import settings
from app.services.ai_services import OpenAIModelService


def get_ai_service(user_tier: str, api_key: Optional[str] = None):
    """
    Get an AI service instance based on the user's subscription tier.

    Args:
        user_tier: The user's subscription tier ("free", "premium", or "expert").
        api_key: Optional API key to use. If not provided, the key from settings will be used.

    Returns:
        An AI service instance configured for the user's tier.
    """
    # Use the provided API key or fall back to the one in settings
    openai_api_key = api_key or settings.OPENAI_API_KEY

    # HOSTINGER HORIZONS CONTEST: Force all users to premium tier for demo
    if settings.HOSTINGER_HORIZONS_CONTEST_MODE and user_tier in ["free", "basic"]:
        user_tier = "premium"

    if user_tier == "free":
        # Basic tier configuration
        return OpenAIModelService(
            api_key=openai_api_key,
            analysis_model_name=settings.OPENAI_BASIC_ANALYSIS_MODEL_NAME,
            embedding_model_name=settings.OPENAI_BASIC_EMBEDDING_MODEL_NAME,
            embedding_dimensions=settings.OPENAI_BASIC_EMBEDDING_DIMENSIONS,
            tier="basic"
        )
    elif user_tier == "premium":
        # Premium tier configuration
        return OpenAIModelService(
            api_key=openai_api_key,
            analysis_model_name=settings.OPENAI_PREMIUM_ANALYSIS_MODEL_NAME,
            embedding_model_name=settings.OPENAI_PREMIUM_EMBEDDING_MODEL_NAME,
            embedding_dimensions=settings.OPENAI_PREMIUM_EMBEDDING_DIMENSIONS,
            tier="premium"
        )
    elif user_tier == "expert":
        # Expert tier configuration using o4-mini for analysis
        return OpenAIModelService(
            api_key=openai_api_key,
            analysis_model_name=settings.OPENAI_EXPERT_ANALYSIS_MODEL_NAME,  # o4-mini
            multimodal_model_name=settings.OPENAI_EXPERT_MULTIMODAL_MODEL_NAME,  # gpt-4.1 for OCR/initial context
            embedding_model_name=settings.OPENAI_EXPERT_EMBEDDING_MODEL_NAME,  # text-embedding-3-large
            embedding_dimensions=settings.OPENAI_EXPERT_EMBEDDING_DIMENSIONS,  # 3072
            tier="expert"
        )
    else:
        # Default to basic tier if the tier is not recognized
        return OpenAIModelService(
            api_key=openai_api_key,
            analysis_model_name=settings.OPENAI_BASIC_ANALYSIS_MODEL_NAME,
            embedding_model_name=settings.OPENAI_BASIC_EMBEDDING_MODEL_NAME,
            embedding_dimensions=settings.OPENAI_BASIC_EMBEDDING_DIMENSIONS,
            tier="basic"
        )
