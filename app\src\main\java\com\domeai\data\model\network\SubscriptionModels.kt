package com.domeai.data.model.network

import com.google.gson.annotations.SerializedName

/**
 * Request model for changing subscription plan
 */
data class ChangePlanRequest(
    @SerializedName("email") val email: String,
    @SerializedName("new_plan") val newPlan: String // Values: "basic", "premium", or "expert"
)

/**
 * Response model for changing subscription plan
 */
data class ChangePlanResponse(
    @SerializedName("status") val status: String,
    @SerializedName("message") val message: String,
    @SerializedName("subscription_tier") val subscriptionTier: String,
    @SerializedName("monthly_scan_allowance") val monthlyScanAllowance: Int,
    @SerializedName("expert_scan_allowance") val expertScanAllowance: Int
)

/**
 * Response model for subscription details
 */
data class SubscriptionDetailsResponse(
    @SerializedName("subscription_tier") val subscriptionTier: String,
    @SerializedName("monthly_scan_allowance") val monthlyScanAllowance: Int,
    @SerializedName("scans_this_month") val scansThisMonth: Int,
    @SerializedName("expert_scan_allowance") val expertScanAllowance: Int,
    @SerializedName("expert_scans_this_month") val expertScansThisMonth: Int,
    @SerializedName("subscription_expiry_date") val subscriptionExpiryDate: String? = null,
    @SerializedName("auto_renew_status") val autoRenewStatus: Boolean? = false
)
