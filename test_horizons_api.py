import requests
import json

# Test the DomeAI backend API to verify it's working
API_BASE_URL = "https://domeai-backend.onrender.com"

def test_login():
    """Test login to get auth token"""
    url = f"{API_BASE_URL}/api/v1/auth/login"
    data = {
        "username": "<EMAIL>",
        "password": "@#48CrVGt3"
    }
    
    print("Testing login...")
    response = requests.post(url, data=data)
    print(f"Login response status: {response.status_code}")
    print(f"Login response: {response.text}")
    
    if response.status_code == 200:
        token = response.json().get("access_token")
        print(f"Login successful! Token: {token[:20]}...")
        return token
    else:
        print("Login failed!")
        return None

def test_scan_submission(token):
    """Test scan submission with correct payload structure"""
    url = f"{API_BASE_URL}/api/v1/scans/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    # Test the CORRECT payload structure (what backend expects)
    # Backend expects scan_in field containing the scan data
    payload = {
        "scan_in": {
            "input_text": "Have I fucked up massively?",
            "input_content_type": "text"
        },
        "use_expert_scan": False
    }

    print("\nTesting scan submission with correct payload...")
    print(f"Payload: {json.dumps(payload, indent=2)}")

    response = requests.post(url, json=payload, headers=headers)
    print(f"Scan submission status: {response.status_code}")
    print(f"Scan submission response: {response.text}")

    if response.status_code == 202:
        scan_data = response.json()
        scan_id = scan_data.get("id")
        print(f"Scan submitted successfully! Scan ID: {scan_id}")
        return scan_id
    else:
        print("Scan submission failed!")
        return None

def test_scan_status(token, scan_id):
    """Test checking scan status with polling"""
    url = f"{API_BASE_URL}/api/v1/scans/{scan_id}"
    headers = {
        "Authorization": f"Bearer {token}"
    }

    print(f"\nPolling scan status for ID: {scan_id}")

    import time
    max_attempts = 10
    for attempt in range(max_attempts):
        response = requests.get(url, headers=headers)
        print(f"Attempt {attempt + 1}: Status {response.status_code}")

        if response.status_code == 200:
            scan_data = response.json()
            status = scan_data.get("status")
            print(f"Current scan status: {status}")

            if status == "completed":
                print("✅ Scan completed successfully!")
                analysis_result = scan_data.get("analysis_result")
                if analysis_result:
                    print(f"Risk Score: {analysis_result.get('risk_score')}")
                    print(f"Explanation: {analysis_result.get('explanation', '')[:100]}...")
                    print(f"Red Flags: {analysis_result.get('detected_red_flags', [])}")
                return scan_data
            elif status == "failed":
                print("❌ Scan failed!")
                error_msg = scan_data.get("error_message")
                print(f"Error: {error_msg}")
                return scan_data
            elif status in ["pending", "processing"]:
                print(f"⏳ Scan still {status}, waiting 3 seconds...")
                time.sleep(3)
            else:
                print(f"Unknown status: {status}")
                return scan_data
        else:
            print(f"Failed to get scan status: {response.text}")
            return None

    print("⚠️ Polling timeout - scan may still be processing")
    return None

if __name__ == "__main__":
    print("=== DomeAI Backend API Test ===")
    
    # Test login
    token = test_login()
    if not token:
        exit(1)
    
    # Test scan submission
    scan_id = test_scan_submission(token)
    if not scan_id:
        exit(1)
    
    # Test scan status
    scan_data = test_scan_status(token, scan_id)
    
    print("\n=== Test Complete ===")
    if scan_data:
        print("✅ Backend API is working correctly!")
        print("The issue is likely in the Horizons web app payload structure.")
    else:
        print("❌ Backend API has issues.")
