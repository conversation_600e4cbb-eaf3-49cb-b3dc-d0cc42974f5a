"""
<PERSON>ript to test the fixed webhook endpoint.
"""
import base64
import json
import requests
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def create_test_notification() -> Dict[str, Any]:
    """
    Create a test notification payload.
    
    Returns:
        A dictionary containing a test notification
    """
    # Create a test notification
    developer_notification = {
        "version": "1.0",
        "packageName": "com.domeai.scamdetector",
        "eventTimeMillis": "1621234567890",
        "testNotification": {
            "version": "1.0"
        }
    }
    
    # Encode the notification as base64
    encoded_data = base64.b64encode(json.dumps(developer_notification).encode("utf-8")).decode("utf-8")
    
    # Create the Pub/Sub message
    pubsub_message = {
        "message": {
            "data": encoded_data,
            "messageId": "test-message-id",
            "publishTime": "2023-05-20T10:00:00.000Z"
        },
        "subscription": "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
    }
    
    return pubsub_message

def create_subscription_purchased_notification() -> Dict[str, Any]:
    """
    Create a subscription purchased notification payload.
    
    Returns:
        A dictionary containing a subscription purchased notification
    """
    # Create a subscription purchased notification
    developer_notification = {
        "version": "1.0",
        "packageName": "com.domeai.scamdetector",
        "eventTimeMillis": "1621234567890",
        "subscriptionNotification": {
            "version": "1.0",
            "notificationType": 4,  # SUBSCRIPTION_PURCHASED
            "purchaseToken": "test-purchase-token",
            "subscriptionId": "domeai_premium_monthly"
        }
    }
    
    # Encode the notification as base64
    encoded_data = base64.b64encode(json.dumps(developer_notification).encode("utf-8")).decode("utf-8")
    
    # Create the Pub/Sub message
    pubsub_message = {
        "message": {
            "data": encoded_data,
            "messageId": "test-message-id",
            "publishTime": "2023-05-20T10:00:00.000Z"
        },
        "subscription": "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
    }
    
    return pubsub_message

def test_webhook():
    """
    Test the webhook endpoint with a test notification and a subscription purchased notification.
    """
    # API endpoint
    url = "http://localhost:8000/api/v1/webhooks/googleplay/rtdn"
    
    # Test notification
    logger.info("Sending test notification...")
    test_payload = create_test_notification()
    
    try:
        response = requests.post(url, json=test_payload)
        logger.info(f"Status code: {response.status_code}")
        logger.info(f"Response: {response.text}")
    except Exception as e:
        logger.error(f"Error sending test notification: {str(e)}")
    
    # Subscription notification
    logger.info("\nSending subscription purchased notification...")
    subscription_payload = create_subscription_purchased_notification()
    
    try:
        response = requests.post(url, json=subscription_payload)
        logger.info(f"Status code: {response.status_code}")
        logger.info(f"Response: {response.text}")
    except Exception as e:
        logger.error(f"Error sending subscription notification: {str(e)}")

if __name__ == "__main__":
    logger.info("Testing the fixed webhook endpoint...")
    test_webhook()
