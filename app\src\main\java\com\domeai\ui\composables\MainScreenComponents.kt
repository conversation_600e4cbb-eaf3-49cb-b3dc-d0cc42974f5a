package com.domeai.ui.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Shield
import androidx.compose.material.icons.filled.Cancel
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.domeai.data.model.RiskLevel
import com.domeai.data.model.ScanResult
import com.domeai.data.model.ScanSourceType
import com.domeai.presentation.main.MainTab
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Bottom navigation bar for the main screen
 */
@Composable
fun MainBottomNavigation(
    selectedTab: MainTab,
    onTabSelected: (MainTab) -> Unit
) {
    NavigationBar {
        NavigationBarItem(
            icon = { Icon(Icons.Default.Home, contentDescription = "Home") },
            label = { Text("Home") },
            selected = selectedTab == MainTab.HOME,
            onClick = { onTabSelected(MainTab.HOME) }
        )

        NavigationBarItem(
            icon = { Icon(Icons.Default.Search, contentDescription = "Scan") },
            label = { Text("Scan") },
            selected = selectedTab == MainTab.SCAN,
            onClick = { onTabSelected(MainTab.SCAN) }
        )

        NavigationBarItem(
            icon = { Icon(Icons.Default.History, contentDescription = "History") },
            label = { Text("History") },
            selected = selectedTab == MainTab.HISTORY,
            onClick = { onTabSelected(MainTab.HISTORY) }
        )

        NavigationBarItem(
            icon = { Icon(Icons.Default.Settings, contentDescription = "Settings") },
            label = { Text("Settings") },
            selected = selectedTab == MainTab.SETTINGS,
            onClick = { onTabSelected(MainTab.SETTINGS) }
        )
    }
}

/**
 * Overlay toggle card
 */
@Composable
fun OverlayToggleCard(
    isEnabled: Boolean,
    hasPermission: Boolean,
    onToggleOverlay: (Boolean) -> Unit,
    onRequestPermission: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (isEnabled && hasPermission) Icons.Default.Shield else Icons.Default.Cancel,
                    contentDescription = "Overlay Status",
                    tint = if (isEnabled && hasPermission) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                    modifier = Modifier.size(24.dp)
                )

                Spacer(modifier = Modifier.width(16.dp))

                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Overlay Protection",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = if (isEnabled && hasPermission) {
                            "Active - Scanning for scams in real-time"
                        } else if (!hasPermission) {
                            "Permission required to enable overlay"
                        } else {
                            "Disabled - Enable to protect against scams"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }

                Switch(
                    checked = isEnabled,
                    onCheckedChange = onToggleOverlay,
                    enabled = hasPermission
                )
            }

            if (!hasPermission) {
                Spacer(modifier = Modifier.height(16.dp))

                PrimaryButton(
                    text = "Grant Permission",
                    onClick = onRequestPermission
                )
            }
        }
    }
}

/**
 * Scan history item
 */
@Composable
fun ScanHistoryItem(
    scan: ScanResult,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val dateFormat = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
    val formattedDate = dateFormat.format(Date(scan.timestamp))

    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RiskLevelIndicator(riskLevel = scan.riskLevel)

            Spacer(modifier = Modifier.width(16.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = when (scan.sourceType) {
                        ScanSourceType.OVERLAY_SCREENSHOT -> "Screenshot Scan"
                        ScanSourceType.MANUAL_TEXT -> "Text Scan"
                        ScanSourceType.MANUAL_IMAGE -> "Image Scan"
                    },
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = scan.sourceContent ?: "No content preview available",
                    style = MaterialTheme.typography.bodySmall,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = formattedDate,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
            }

            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "${scan.riskScore}%",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = getRiskColor(scan.riskLevel)
                )

                Text(
                    text = scan.riskLevel.name.replace("_", " "),
                    style = MaterialTheme.typography.bodySmall,
                    color = getRiskColor(scan.riskLevel)
                )
            }
        }
    }
}

/**
 * Risk level indicator
 */
@Composable
fun RiskLevelIndicator(
    riskLevel: RiskLevel,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(40.dp)
            .clip(CircleShape)
            .background(getRiskColor(riskLevel).copy(alpha = 0.1f)),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = when (riskLevel) {
                RiskLevel.SAFE -> Icons.Default.Shield
                RiskLevel.LOW_RISK -> Icons.Default.Shield
                RiskLevel.MEDIUM_RISK -> Icons.Default.Warning
                RiskLevel.HIGH_RISK -> Icons.Default.Warning
            },
            contentDescription = "Risk Level",
            tint = getRiskColor(riskLevel),
            modifier = Modifier.size(24.dp)
        )
    }
}

/**
 * Empty state for scan history
 */
@Composable
fun EmptyScanHistory(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.History,
            contentDescription = "No Scans",
            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f),
            modifier = Modifier.size(64.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "No Scan History",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "Your scan history will appear here once you start scanning for scams",
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}

/**
 * Quick action button
 */
@Composable
fun QuickActionButton(
    text: String,
    icon: ImageVector,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = text,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            Text(
                text = text,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * Helper function to get color based on risk level
 */
@Composable
fun getRiskColor(riskLevel: RiskLevel): Color {
    return when (riskLevel) {
        RiskLevel.SAFE -> MaterialTheme.colorScheme.primary
        RiskLevel.LOW_RISK -> Color(0xFF4CAF50) // Green
        RiskLevel.MEDIUM_RISK -> Color(0xFFFFC107) // Amber
        RiskLevel.HIGH_RISK -> Color(0xFFF44336) // Red
    }
}
