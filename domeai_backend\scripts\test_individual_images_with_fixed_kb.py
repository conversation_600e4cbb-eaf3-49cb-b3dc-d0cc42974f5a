#!/usr/bin/env python
"""
<PERSON>ript to test individual images with the fixed knowledge base retrieval.
"""

import asyncio
import logging
import sys
import os
import time
from typing import List, Dict, Any, Optional

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan import Scan
from app.services.ai_service_factory import get_ai_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Test query for cashier's check scam
TEST_QUERY = "I received a cashier's check for more than the amount I was selling my item for. The buyer asked me to wire back the difference. Is this legitimate?"

async def test_kb_retrieval_with_fixed_implementation():
    """Test knowledge base retrieval with the fixed implementation."""
    logger.info("Testing knowledge base retrieval with the fixed implementation")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create an AI service
        ai_service = get_ai_service(user_tier="premium")
        
        # Generate an embedding for the test query
        logger.info(f"Generating embedding for test query: {TEST_QUERY}")
        embedding = await ai_service.get_text_embedding(text=TEST_QUERY)
        logger.info(f"Generated embedding with {len(embedding)} dimensions")
        
        # Test knowledge base retrieval
        from app.crud import crud_kb
        logger.info("Testing knowledge base retrieval")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=embedding,
            top_k=3
        )
        
        logger.info(f"Found {len(similar_chunks)} similar knowledge base chunks")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Similar chunk {i+1}: ID={chunk.id}, Source={chunk.source}")
            if hasattr(chunk, 'similarity_score'):
                logger.info(f"Similarity score: {chunk.similarity_score:.4f}")
            logger.info(f"Content: {chunk.content[:100]}...")
        
        # Perform RAG analysis
        logger.info("Performing RAG analysis")
        rag_result = await ai_service.perform_scam_analysis_with_rag(
            query_text=TEST_QUERY,
            query_embedding=embedding,
            db=db
        )
        
        logger.info("RAG analysis result:")
        logger.info(f"Risk score: {rag_result.risk_score}")
        logger.info(f"Explanation: {rag_result.explanation[:200]}...")
        logger.info(f"Detected red flags: {rag_result.detected_red_flags}")
        logger.info(f"Recommendations: {rag_result.recommendations[:200]}...")
        logger.info(f"Confidence level: {rag_result.confidence_level}")
        logger.info(f"Model used: {rag_result.model_used}")
        
    finally:
        db.close()

def main():
    """Run the test."""
    asyncio.run(test_kb_retrieval_with_fixed_implementation())

if __name__ == "__main__":
    main()
