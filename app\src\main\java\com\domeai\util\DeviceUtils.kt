package com.domeai.util

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings

/**
 * Utility class for device-specific operations
 */
object DeviceUtils {

    /**
     * Check if the device is a Xiaomi device
     */
    fun isXiaomiDevice(): Bo<PERSON>an {
        return Build.MANUFACTURER.equals("Xiaomi", ignoreCase = true) ||
                Build.BRAND.equals("Xiaomi", ignoreCase = true) ||
                Build.BRAND.equals("Redmi", ignoreCase = true) ||
                Build.BRAND.equals("POCO", ignoreCase = true)
    }

    /**
     * Check if the device is running MIUI
     */
    fun isMiuiDevice(): <PERSON><PERSON>an {
        return try {
            val prop = System.getProperty("ro.miui.ui.version.name")
            prop != null && prop.isNotEmpty()
        } catch (e: Exception) {
            android.util.Log.e("DeviceUtils", "Error checking MIUI: ${e.message}")
            false // Return false to be safe
        }
    }

    /**
     * Get intent to open battery optimization settings for Xiaomi devices
     */
    fun getXiaomiBatterySettingsIntent(packageName: String): Intent? {
        return try {
            // Try to open <PERSON>mi's security center
            Intent().apply {
                component = android.content.ComponentName(
                    "com.miui.securitycenter",
                    "com.miui.permcenter.autostart.AutoStartManagementActivity"
                )
            }
        } catch (e: Exception) {
            // Fallback to standard battery optimization settings
            Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
        }
    }

    /**
     * Get intent to request overlay permission
     */
    fun getOverlayPermissionIntent(context: Context): Intent {
        return Intent(
            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
            Uri.parse("package:${context.packageName}")
        )
    }
}
