package com.domeai.presentation.scan

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.domeai.data.model.RiskLevel
import com.domeai.data.model.ScanResult
import com.domeai.data.model.ScanType
import com.domeai.data.repository.ScanRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject

/**
 * Filter options for scan history
 */
data class ScanHistoryFilter(
    val searchQuery: String = "",
    val riskLevel: RiskLevel? = null,
    val scanType: ScanType? = null,
    val startDate: Date? = null,
    val endDate: Date? = null,
    val favoritesOnly: Boolean = false
)

/**
 * UI state for scan history screen
 */
data class ScanHistoryUiState(
    val scans: List<ScanResult> = emptyList(),
    val filter: ScanHistoryFilter = ScanHistoryFilter(),
    val isLoading: Boolean = false,
    val error: String? = null
)

/**
 * Actions that can be performed on the scan history screen
 */
sealed class ScanHistoryAction {
    data class Search(val query: String) : ScanHistoryAction()
    data class FilterByRiskLevel(val riskLevel: RiskLevel?) : ScanHistoryAction()
    data class FilterByScanType(val scanType: ScanType?) : ScanHistoryAction()
    data class FilterByDateRange(val startDate: Date?, val endDate: Date?) : ScanHistoryAction()
    data class ToggleFavoriteFilter(val favoritesOnly: Boolean) : ScanHistoryAction()
    data class ToggleFavorite(val scanId: String) : ScanHistoryAction()
    data class DeleteScan(val scanId: String) : ScanHistoryAction()
    object ClearFilters : ScanHistoryAction()
    object Refresh : ScanHistoryAction()
}

/**
 * ViewModel for the scan history screen
 */
@OptIn(ExperimentalCoroutinesApi::class)
@HiltViewModel
class ScanHistoryViewModel @Inject constructor(
    private val scanRepository: ScanRepository
) : ViewModel() {

    // Current filter state
    private val _filter = MutableStateFlow(ScanHistoryFilter())

    // Loading state
    private val _isLoading = MutableStateFlow(false)

    // Error state
    private val _error = MutableStateFlow<String?>(null)

    // Filtered scans based on current filter
    private val _filteredScans = _filter.flatMapLatest { filter ->
        when {
            filter.favoritesOnly -> scanRepository.getFavoriteScans()
            filter.searchQuery.isNotBlank() -> scanRepository.searchScans(filter.searchQuery)
            filter.riskLevel != null -> scanRepository.filterByRiskLevel(filter.riskLevel)
            filter.scanType != null -> scanRepository.filterByType(filter.scanType)
            filter.startDate != null || filter.endDate != null ->
                scanRepository.filterByDateRange(filter.startDate, filter.endDate)
            else -> scanRepository.getAllScans()
        }
    }

    // Combined UI state
    val uiState: StateFlow<ScanHistoryUiState> = combine(
        _filteredScans,
        _filter,
        _isLoading,
        _error
    ) { scans, filter, isLoading, error ->
        ScanHistoryUiState(
            scans = scans,
            filter = filter,
            isLoading = isLoading,
            error = error
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = ScanHistoryUiState(isLoading = true)
    )

    /**
     * Handle actions from the UI
     */
    fun handleAction(action: ScanHistoryAction) {
        when (action) {
            is ScanHistoryAction.Search -> {
                _filter.value = _filter.value.copy(searchQuery = action.query)
            }
            is ScanHistoryAction.FilterByRiskLevel -> {
                _filter.value = _filter.value.copy(riskLevel = action.riskLevel)
            }
            is ScanHistoryAction.FilterByScanType -> {
                _filter.value = _filter.value.copy(scanType = action.scanType)
            }
            is ScanHistoryAction.FilterByDateRange -> {
                _filter.value = _filter.value.copy(
                    startDate = action.startDate,
                    endDate = action.endDate
                )
            }
            is ScanHistoryAction.ToggleFavoriteFilter -> {
                _filter.value = _filter.value.copy(favoritesOnly = action.favoritesOnly)
            }
            is ScanHistoryAction.ToggleFavorite -> {
                toggleFavorite(action.scanId)
            }
            is ScanHistoryAction.DeleteScan -> {
                deleteScan(action.scanId)
            }
            is ScanHistoryAction.ClearFilters -> {
                _filter.value = ScanHistoryFilter()
            }
            is ScanHistoryAction.Refresh -> {
                // In a real app, this would refresh data from the server
                // For now, we'll just clear any error
                _error.value = null
            }
        }
    }

    /**
     * Toggle favorite status of a scan
     */
    private fun toggleFavorite(scanId: String) {
        viewModelScope.launch {
            try {
                scanRepository.toggleFavorite(scanId)
            } catch (e: Exception) {
                _error.value = "Failed to update favorite status: ${e.message}"
            }
        }
    }

    /**
     * Delete a scan
     */
    private fun deleteScan(scanId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                scanRepository.deleteScan(scanId)
            } catch (e: Exception) {
                _error.value = "Failed to delete scan: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
}
