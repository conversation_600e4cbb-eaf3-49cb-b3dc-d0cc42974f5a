"""
Test script for Google Play RTDN webhook.

This script sends test notifications to the Google Play RTDN webhook endpoint.
"""
import base64
import json
import logging
import requests
import argparse
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Default webhook URL
DEFAULT_WEBHOOK_URL = "http://localhost:8000/api/v1/webhooks/googleplay/rtdn"

# Test notification types
TEST_NOTIFICATION = "test"
SUBSCRIPTION_PURCHASED = "subscription_purchased"
SUBSCRIPTION_RENEWED = "subscription_renewed"
SUBSCRIPTION_CANCELED = "subscription_canceled"
SUBSCRIPTION_EXPIRED = "subscription_expired"
SUBSCRIPTION_REVOKED = "subscription_revoked"
ONE_TIME_PRODUCT_PURCHASED = "one_time_product_purchased"
VOIDED_PURCHASE_REFUNDED = "voided_purchase_refunded"

# Test data
TEST_PACKAGE_NAME = "com.domeai.scamdetector"
TEST_PURCHASE_TOKEN = "test_purchase_token_123456789"
TEST_SUBSCRIPTION_ID = "domeai_placeholder_premium_monthly"
TEST_ORDER_ID = "GPA.1234-5678-9012-3456"


def create_test_notification() -> Dict[str, Any]:
    """
    Create a test notification payload.
    
    Returns:
        A dictionary containing a test notification
    """
    # Create a test notification
    developer_notification = {
        "version": "1.0",
        "packageName": TEST_PACKAGE_NAME,
        "eventTimeMillis": "1621234567890",
        "testNotification": {
            "version": "1.0"
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_subscription_purchased_notification() -> Dict[str, Any]:
    """
    Create a subscription purchased notification payload.
    
    Returns:
        A dictionary containing a subscription purchased notification
    """
    # Create a subscription purchased notification
    developer_notification = {
        "version": "1.0",
        "packageName": TEST_PACKAGE_NAME,
        "eventTimeMillis": "1621234567890",
        "subscriptionNotification": {
            "version": "1.0",
            "notificationType": 4,  # SUBSCRIPTION_PURCHASED
            "purchaseToken": TEST_PURCHASE_TOKEN,
            "subscriptionId": TEST_SUBSCRIPTION_ID
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_subscription_renewed_notification() -> Dict[str, Any]:
    """
    Create a subscription renewed notification payload.
    
    Returns:
        A dictionary containing a subscription renewed notification
    """
    # Create a subscription renewed notification
    developer_notification = {
        "version": "1.0",
        "packageName": TEST_PACKAGE_NAME,
        "eventTimeMillis": "1621234567890",
        "subscriptionNotification": {
            "version": "1.0",
            "notificationType": 2,  # SUBSCRIPTION_RENEWED
            "purchaseToken": TEST_PURCHASE_TOKEN,
            "subscriptionId": TEST_SUBSCRIPTION_ID
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_subscription_canceled_notification() -> Dict[str, Any]:
    """
    Create a subscription canceled notification payload.
    
    Returns:
        A dictionary containing a subscription canceled notification
    """
    # Create a subscription canceled notification
    developer_notification = {
        "version": "1.0",
        "packageName": TEST_PACKAGE_NAME,
        "eventTimeMillis": "1621234567890",
        "subscriptionNotification": {
            "version": "1.0",
            "notificationType": 3,  # SUBSCRIPTION_CANCELED
            "purchaseToken": TEST_PURCHASE_TOKEN,
            "subscriptionId": TEST_SUBSCRIPTION_ID
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_subscription_expired_notification() -> Dict[str, Any]:
    """
    Create a subscription expired notification payload.
    
    Returns:
        A dictionary containing a subscription expired notification
    """
    # Create a subscription expired notification
    developer_notification = {
        "version": "1.0",
        "packageName": TEST_PACKAGE_NAME,
        "eventTimeMillis": "1621234567890",
        "subscriptionNotification": {
            "version": "1.0",
            "notificationType": 13,  # SUBSCRIPTION_EXPIRED
            "purchaseToken": TEST_PURCHASE_TOKEN,
            "subscriptionId": TEST_SUBSCRIPTION_ID
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_subscription_revoked_notification() -> Dict[str, Any]:
    """
    Create a subscription revoked notification payload.
    
    Returns:
        A dictionary containing a subscription revoked notification
    """
    # Create a subscription revoked notification
    developer_notification = {
        "version": "1.0",
        "packageName": TEST_PACKAGE_NAME,
        "eventTimeMillis": "1621234567890",
        "subscriptionNotification": {
            "version": "1.0",
            "notificationType": 12,  # SUBSCRIPTION_REVOKED
            "purchaseToken": TEST_PURCHASE_TOKEN,
            "subscriptionId": TEST_SUBSCRIPTION_ID
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_one_time_product_purchased_notification() -> Dict[str, Any]:
    """
    Create a one-time product purchased notification payload.
    
    Returns:
        A dictionary containing a one-time product purchased notification
    """
    # Create a one-time product purchased notification
    developer_notification = {
        "version": "1.0",
        "packageName": TEST_PACKAGE_NAME,
        "eventTimeMillis": "1621234567890",
        "oneTimeProductNotification": {
            "version": "1.0",
            "notificationType": 1,  # ONE_TIME_PRODUCT_PURCHASED
            "purchaseToken": TEST_PURCHASE_TOKEN,
            "sku": "domeai_placeholder_scan_credits_10"
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_voided_purchase_refunded_notification() -> Dict[str, Any]:
    """
    Create a voided purchase refunded notification payload.
    
    Returns:
        A dictionary containing a voided purchase refunded notification
    """
    # Create a voided purchase refunded notification
    developer_notification = {
        "version": "1.0",
        "packageName": TEST_PACKAGE_NAME,
        "eventTimeMillis": "1621234567890",
        "voidedPurchaseNotification": {
            "version": "1.0",
            "notificationType": 1,  # PURCHASE_REFUNDED
            "purchaseToken": TEST_PURCHASE_TOKEN,
            "orderId": TEST_ORDER_ID
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_pubsub_message(developer_notification: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a Pub/Sub message from a developer notification.
    
    Args:
        developer_notification: The developer notification to encode
        
    Returns:
        A dictionary containing a Pub/Sub message
    """
    # Encode the notification as base64
    encoded_data = base64.b64encode(json.dumps(developer_notification).encode("utf-8")).decode("utf-8")
    
    # Create the Pub/Sub message
    pubsub_message = {
        "message": {
            "data": encoded_data,
            "messageId": "test-message-id",
            "publishTime": "2023-05-20T10:00:00.000Z"
        },
        "subscription": "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
    }
    
    return pubsub_message


def send_notification(notification_type: str, webhook_url: str) -> Optional[Dict[str, Any]]:
    """
    Send a notification to the webhook endpoint.
    
    Args:
        notification_type: Type of notification to send
        webhook_url: URL of the webhook endpoint
        
    Returns:
        Response from the webhook endpoint
    """
    logger.info(f"Sending {notification_type} notification to {webhook_url}")
    
    # Create the notification payload
    if notification_type == TEST_NOTIFICATION:
        payload = create_test_notification()
    elif notification_type == SUBSCRIPTION_PURCHASED:
        payload = create_subscription_purchased_notification()
    elif notification_type == SUBSCRIPTION_RENEWED:
        payload = create_subscription_renewed_notification()
    elif notification_type == SUBSCRIPTION_CANCELED:
        payload = create_subscription_canceled_notification()
    elif notification_type == SUBSCRIPTION_EXPIRED:
        payload = create_subscription_expired_notification()
    elif notification_type == SUBSCRIPTION_REVOKED:
        payload = create_subscription_revoked_notification()
    elif notification_type == ONE_TIME_PRODUCT_PURCHASED:
        payload = create_one_time_product_purchased_notification()
    elif notification_type == VOIDED_PURCHASE_REFUNDED:
        payload = create_voided_purchase_refunded_notification()
    else:
        logger.error(f"Unknown notification type: {notification_type}")
        return None
    
    # Print the decoded notification
    encoded_data = payload["message"]["data"]
    decoded_data = json.loads(base64.b64decode(encoded_data).decode("utf-8"))
    logger.info(f"Decoded notification: {json.dumps(decoded_data, indent=2)}")
    
    # Send the request
    try:
        response = requests.post(
            webhook_url,
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        logger.info(f"Status code: {response.status_code}")
        logger.info(f"Response: {response.text}")
        return response.json() if response.status_code == 200 else None
    except Exception as e:
        logger.error(f"Error sending notification: {str(e)}")
        return None


def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description="Test Google Play RTDN webhook")
    parser.add_argument(
        "--type",
        choices=[
            TEST_NOTIFICATION,
            SUBSCRIPTION_PURCHASED,
            SUBSCRIPTION_RENEWED,
            SUBSCRIPTION_CANCELED,
            SUBSCRIPTION_EXPIRED,
            SUBSCRIPTION_REVOKED,
            ONE_TIME_PRODUCT_PURCHASED,
            VOIDED_PURCHASE_REFUNDED
        ],
        default=TEST_NOTIFICATION,
        help="Type of notification to send"
    )
    parser.add_argument(
        "--url",
        default=DEFAULT_WEBHOOK_URL,
        help="URL of the webhook endpoint"
    )
    args = parser.parse_args()
    
    send_notification(args.type, args.url)


if __name__ == "__main__":
    main()
