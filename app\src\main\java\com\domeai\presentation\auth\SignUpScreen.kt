package com.domeai.presentation.auth

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.isImeVisible


import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.relocation.BringIntoViewRequester
import androidx.compose.foundation.relocation.bringIntoViewRequester
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.derivedStateOf
import androidx.compose.ui.focus.onFocusChanged

import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.domeai.ui.composables.EmailField
import com.domeai.ui.composables.LegalAgreementCheckbox
import com.domeai.ui.composables.PasswordField
import com.domeai.ui.composables.PasswordStrengthIndicator
import com.domeai.ui.composables.PrimaryButton
import com.domeai.ui.composables.TextActionButton
import com.domeai.ui.composables.VerticalSpacer

@OptIn(ExperimentalFoundationApi::class, ExperimentalLayoutApi::class)
@Composable
fun SignUpScreen(
    onNavigateToLogin: () -> Unit,
    onNavigateToDataConsent: () -> Unit,
    onNavigateToMain: () -> Unit = { /* Default empty implementation */ },
    onNavigateToTermsOfService: () -> Unit = {},
    onNavigateToPrivacyPolicy: () -> Unit = {},
    viewModel: SignUpViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    val focusManager = LocalFocusManager.current

    // Handle UI events
    LaunchedEffect(key1 = true) {
        viewModel.uiState.collect { state ->
            if (state.errorMessage != null) {
                snackbarHostState.showSnackbar(state.errorMessage)
            }
        }
    }

    LaunchedEffect(key1 = viewModel) {
        viewModel.uiEvent.collect { event ->
            when (event) {
                is SignUpUiEvent.NavigateToLogin -> onNavigateToLogin()
                is SignUpUiEvent.NavigateToDataConsent -> onNavigateToDataConsent()
                is SignUpUiEvent.NavigateToMain -> onNavigateToMain()
                is SignUpUiEvent.NavigateToTermsOfService -> onNavigateToTermsOfService()
                is SignUpUiEvent.NavigateToPrivacyPolicy -> onNavigateToPrivacyPolicy()
            }
        }
    }

    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
        modifier = Modifier.imePadding()
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(40.dp))

            Text(
                text = "Create Account",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )

            VerticalSpacer(height = 8)

            Text(
                text = "Join DomeAI to protect yourself from online scams",
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )

            VerticalSpacer(height = 40)

            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                val scrollState = rememberScrollState()
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                        .verticalScroll(scrollState)
                ) {
                    Text(
                        text = "Sign Up",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )

                    VerticalSpacer(height = 24)

                    EmailField(
                        email = uiState.email,
                        onEmailChange = { viewModel.sendAction(SignUpUiAction.UpdateEmail(it)) },
                        errorMessage = uiState.emailError,
                        enabled = !uiState.isLoading,
                        imeAction = ImeAction.Next
                    )

                    VerticalSpacer()

                    // Track keyboard visibility using a simpler approach
                    val imeVisible = WindowInsets.isImeVisible

                    // Password input section with BringIntoViewRequester
                    var isPasswordFocused by remember { mutableStateOf(false) }
                    val passwordRequester = remember { BringIntoViewRequester() }

                    // Create a dedicated PasswordInputSection composable
                    PasswordInputSection(
                        password = uiState.password,
                        onPasswordChange = { viewModel.sendAction(SignUpUiAction.UpdatePassword(it)) },
                        errorMessage = uiState.passwordError,
                        enabled = !uiState.isLoading,
                        onFocusChanged = { isFocused ->
                            isPasswordFocused = isFocused
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .bringIntoViewRequester(passwordRequester)
                    )

                    // Primary LaunchedEffect to handle scrolling when password field is focused
                    // This is the most important one for initial focus
                    LaunchedEffect(isPasswordFocused) {
                        if (isPasswordFocused) {
                            // Immediate scroll attempt
                            passwordRequester.bringIntoView()

                            // Then a series of aggressive scroll attempts with short intervals
                            // This ensures we catch the exact moment when the keyboard starts appearing
                            for (delay in listOf(50L, 100L, 150L, 200L, 250L, 300L)) {
                                kotlinx.coroutines.delay(delay)
                                passwordRequester.bringIntoView()
                            }

                            // Then more attempts with longer intervals to catch when keyboard is fully visible
                            for (delay in listOf(400L, 500L, 600L, 700L, 800L)) {
                                kotlinx.coroutines.delay(delay)
                                if (isPasswordFocused) {
                                    passwordRequester.bringIntoView()
                                }
                            }
                        }
                    }

                    // Secondary LaunchedEffect specifically for keyboard visibility changes
                    LaunchedEffect(imeVisible) {
                        if (imeVisible && isPasswordFocused) {
                            // When keyboard appears, make extra scroll attempts
                            // These are critical for the initial focus case
                            passwordRequester.bringIntoView()
                            kotlinx.coroutines.delay(100)
                            passwordRequester.bringIntoView()
                            kotlinx.coroutines.delay(200)
                            passwordRequester.bringIntoView()
                            kotlinx.coroutines.delay(300)
                            passwordRequester.bringIntoView()
                        }
                    }

                    // Tertiary LaunchedEffect that combines both triggers for maximum reliability
                    LaunchedEffect(isPasswordFocused, imeVisible) {
                        if (isPasswordFocused && imeVisible) {
                            // This catches the case when both are true simultaneously
                            kotlinx.coroutines.delay(150)
                            passwordRequester.bringIntoView()
                            kotlinx.coroutines.delay(350)
                            passwordRequester.bringIntoView()
                            kotlinx.coroutines.delay(550)
                            passwordRequester.bringIntoView()
                        }
                    }

                    VerticalSpacer()

                    // Confirm Password field with BringIntoViewRequester
                    val confirmPasswordRequester = remember { BringIntoViewRequester() }
                    var isConfirmPasswordFocused by remember { mutableStateOf(false) }

                    // This column contains confirm password, checkbox, and sign up button
                    // to ensure they all scroll into view together
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .bringIntoViewRequester(confirmPasswordRequester)
                    ) {
                        PasswordField(
                            password = uiState.confirmPassword,
                            onPasswordChange = { viewModel.sendAction(SignUpUiAction.UpdateConfirmPassword(it)) },
                            label = "Confirm Password",
                            errorMessage = uiState.confirmPasswordError,
                            enabled = !uiState.isLoading,
                            imeAction = ImeAction.Done,
                            onImeAction = {
                                viewModel.sendAction(SignUpUiAction.SignUp)
                                // Dismiss keyboard when Done is pressed
                                focusManager.clearFocus()
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .onFocusChanged { focusState ->
                                    isConfirmPasswordFocused = focusState.isFocused
                                }
                        )

                        VerticalSpacer()

                        // Legal Agreement Checkbox
                        LegalAgreementCheckbox(
                            checked = uiState.acceptedTerms,
                            onCheckedChange = { viewModel.sendAction(SignUpUiAction.UpdateTermsAccepted(it)) },
                            onTermsClicked = { viewModel.sendAction(SignUpUiAction.NavigateToTermsOfService) },
                            onPrivacyPolicyClicked = { viewModel.sendAction(SignUpUiAction.NavigateToPrivacyPolicy) },
                            errorMessage = uiState.termsError
                        )

                        VerticalSpacer(height = 24)

                        PrimaryButton(
                            text = "Sign Up",
                            onClick = {
                                viewModel.sendAction(SignUpUiAction.SignUp)
                                // Dismiss keyboard when Sign Up is pressed
                                focusManager.clearFocus()
                            },
                            isLoading = uiState.isLoading,
                            enabled = uiState.isFormValid
                        )

                        VerticalSpacer(height = 16)

                        TextActionButton(
                            text = "Already have an account? Login",
                            onClick = { viewModel.sendAction(SignUpUiAction.NavigateToLogin) },
                            modifier = Modifier.align(Alignment.CenterHorizontally)
                        )
                    }

                    // LaunchedEffect to handle scrolling when confirm password field is focused
                    LaunchedEffect(isConfirmPasswordFocused, imeVisible) {
                        if (isConfirmPasswordFocused && imeVisible) {
                            // Single scroll with a small delay to ensure keyboard is shown
                            delay(100)
                            confirmPasswordRequester.bringIntoView()
                        }
                    }
                }
            }
        }
    }
}

/**
 * A composable that wraps the Password TextField and its PasswordStrengthIndicator
 * to ensure they are both visible when the Password field is focused.
 */
@Composable
private fun PasswordInputSection(
    password: String,
    onPasswordChange: (String) -> Unit,
    errorMessage: String? = null,
    enabled: Boolean = true,
    onFocusChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // Password field
        PasswordField(
            password = password,
            onPasswordChange = onPasswordChange,
            errorMessage = errorMessage,
            enabled = enabled,
            imeAction = ImeAction.Next,
            modifier = Modifier
                .fillMaxWidth()
                .onFocusChanged { focusState ->
                    onFocusChanged(focusState.isFocused)
                }
        )

        // Always show password strength indicator, even when empty
        // This ensures the space is reserved and visible when scrolling on initial focus
        PasswordStrengthIndicator(
            password = password,
            modifier = Modifier.padding(vertical = 8.dp)
        )
    }
}
