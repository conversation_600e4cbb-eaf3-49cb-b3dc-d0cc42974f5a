-- Fix the knowledge_base_chunks table to use proper vector type
-- First, check if we have data
SELECT COUNT(*) as total_chunks FROM knowledge_base_chunks;

-- Add a new column with the correct vector type
ALTER TABLE knowledge_base_chunks ADD COLUMN embedding_vector vector(1536);

-- Copy data from the old column to the new one (if any data exists)
UPDATE knowledge_base_chunks 
SET embedding_vector = embedding::vector(1536) 
WHERE embedding IS NOT NULL;

-- Drop the old column
ALTER TABLE knowledge_base_chunks DROP COLUMN embedding;

-- Rename the new column to the original name
ALTER TABLE knowledge_base_chunks RENAME COLUMN embedding_vector TO embedding;

-- Recreate the index with the correct vector type
DROP INDEX IF EXISTS knowledge_base_chunks_embedding_idx;
CREATE INDEX knowledge_base_chunks_embedding_idx ON knowledge_base_chunks USING ivfflat (embedding vector_cosine_ops);

-- Verify the fix
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_name = 'knowledge_base_chunks' AND column_name = 'embedding';
