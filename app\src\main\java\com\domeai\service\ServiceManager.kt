package com.domeai.service

import android.content.Context
import android.content.Intent
import android.os.Build
import com.domeai.data.repository.OverlayRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manager for handling services in the app
 */
@Singleton
class ServiceManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val overlayRepository: OverlayRepository
) {
    private val managerScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    // Flag to track if we've initialized the service monitoring
    private var isMonitoringInitialized = false

    init {
        // We'll initialize monitoring when the user logs in
        // This prevents the overlay from showing on the login screen
        android.util.Log.d("ServiceManager", "ServiceManager initialized, but not monitoring yet")
    }

    /**
     * Start monitoring the overlay service state
     * This should be called after the user logs in
     */
    fun startMonitoring() {
        if (isMonitoringInitialized) return

        android.util.Log.d("ServiceManager", "Starting overlay service monitoring")
        isMonitoringInitialized = true

        // Monitor overlay service state and start/stop service accordingly
        managerScope.launch {
            try {
                overlayRepository.getOverlayServiceState().collectLatest { state ->
                    try {
                        android.util.Log.d("ServiceManager", "Overlay state changed: enabled=${state.isEnabled}, hasPermission=${state.hasPermission}")
                        if (state.isEnabled && state.hasPermission) {
                            startOverlayService()
                        } else {
                            stopOverlayService()
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("ServiceManager", "Error handling state change: ${e.message}")
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("ServiceManager", "Error collecting overlay state: ${e.message}")
            }
        }
    }

    /**
     * Start the overlay service - DISABLED
     */
    fun startOverlayService() {
        android.util.Log.d("ServiceManager", "Overlay functionality is disabled")
        android.widget.Toast.makeText(
            context,
            "Overlay functionality has been disabled",
            android.widget.Toast.LENGTH_SHORT
        ).show()

        // Make sure all services are stopped
        stopOverlayService()
    }

    /**
     * Stop the overlay service - Simplified
     */
    fun stopOverlayService() {
        try {
            android.util.Log.d("ServiceManager", "Stopping all overlay services")

            // Stop all services silently
            context.stopService(Intent(context, OverlayService::class.java))
            context.stopService(Intent(context, FloatingViewService::class.java))
            context.stopService(Intent(context, SimpleFloatingViewService::class.java))
            context.stopService(Intent(context, BasicOverlayService::class.java))
            context.stopService(Intent(context, StandardOverlayService::class.java))
            context.stopService(Intent(context, SimpleOverlayService::class.java))

            android.util.Log.d("ServiceManager", "All overlay services stopped")
        } catch (e: Exception) {
            android.util.Log.e("ServiceManager", "Error stopping overlay services: ${e.message}")
        }
    }
}
