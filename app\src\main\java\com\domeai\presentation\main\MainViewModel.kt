package com.domeai.presentation.main

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.widget.Toast
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.qualifiers.ApplicationContext
import com.domeai.data.model.ScanResult
import com.domeai.data.repository.AuthRepository
import com.domeai.data.repository.OverlayRepository
import com.domeai.presentation.common.BaseViewModel
import com.domeai.service.ServiceManager
import com.domeai.util.DeviceUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import javax.inject.Inject

/**
 * UI State for Main Screen
 */
data class MainUiState(
    val isOverlayEnabled: Boolean = false,
    val hasOverlayPermission: Boolean = false,
    val isLoading: Boolean = false,
    val recentScans: List<ScanResult> = emptyList(),
    val selectedTab: MainTab = MainTab.HOME,
    val showScanHowItWorksPopup: Boolean = false,
    val hasSeenScanHowItWorksPopup: Boolean = false,
    val showCustomerSupportDialog: Boolean = false,
    val showWelcomeMessage: Boolean = true // Controls which welcome message to show
) : com.domeai.presentation.common.UiState

/**
 * UI Events for Main Screen
 */
sealed class MainUiEvent : com.domeai.presentation.common.UiEvent {
    data object NavigateToManualScan : MainUiEvent()
    data object NavigateToSettings : MainUiEvent()
    data object NavigateToAccountSettings : MainUiEvent()
    data object NavigateToScanHistory : MainUiEvent()
    data object RequestOverlayPermission : MainUiEvent()
    data class NavigateToScanDetail(val scanId: String) : MainUiEvent()
    data object NavigateToLogin : MainUiEvent()
    data object NavigateToUserProfileMenu : MainUiEvent()
    data object NavigateToSubscriptionDetails : MainUiEvent()
}

/**
 * UI Actions for Main Screen
 */
sealed class MainUiAction : com.domeai.presentation.common.UiAction {
    data class ToggleOverlay(val enabled: Boolean) : MainUiAction()
    data object RequestOverlayPermission : MainUiAction()
    data class UpdatePermissionState(val hasPermission: Boolean) : MainUiAction()
    data object RefreshScans : MainUiAction()
    data class SelectTab(val tab: MainTab) : MainUiAction()
    data class OpenScanDetail(val scanId: String) : MainUiAction()
    data object StartManualScan : MainUiAction()
    data object OpenSettings : MainUiAction()
    data object OpenAccountSettings : MainUiAction()
    data object OpenScanHistory : MainUiAction()
    data object OpenUserProfileMenu : MainUiAction()
    data object OpenSubscriptionDetails : MainUiAction()
    data object ShowScanHowItWorksPopup : MainUiAction()
    data object DismissScanHowItWorksPopup : MainUiAction()
    data object MarkScanHowItWorksPopupSeen : MainUiAction()
    data object ShowCustomerSupportDialog : MainUiAction()
    data object DismissCustomerSupportDialog : MainUiAction()
    data object Logout : MainUiAction()
    data object StartNewChat : MainUiAction() // New action for starting a new chat
}

/**
 * Tabs for Main Screen
 */
enum class MainTab {
    HOME,
    SCAN,
    HISTORY,
    SETTINGS
}

/**
 * ViewModel for Main Screen
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val overlayRepository: OverlayRepository,
    private val authRepository: AuthRepository,
    val serviceManager: ServiceManager,
    @ApplicationContext private val context: Context
) : BaseViewModel<MainUiState, MainUiEvent, MainUiAction>() {

    // Check if this is a Xiaomi device
    private val isXiaomiDevice by lazy {
        try {
            DeviceUtils.isXiaomiDevice()
        } catch (e: Exception) {
            android.util.Log.e("MainViewModel", "Error checking device type: ${e.message}")
            false
        }
    }

    override fun createInitialState(): MainUiState = MainUiState(isLoading = true)

    init {
        loadOverlayState()
        loadRecentScans()
        loadHowItWorksPopupState()
        loadAndToggleWelcomeMessageState()
    }

    override fun handleAction(action: MainUiAction) {
        when (action) {
            is MainUiAction.ToggleOverlay -> toggleOverlay(action.enabled)
            is MainUiAction.RequestOverlayPermission -> requestOverlayPermission()
            is MainUiAction.UpdatePermissionState -> updatePermissionState(action.hasPermission)
            is MainUiAction.RefreshScans -> loadRecentScans()
            is MainUiAction.SelectTab -> selectTab(action.tab)
            is MainUiAction.OpenScanDetail -> openScanDetail(action.scanId)
            is MainUiAction.StartManualScan -> startManualScan()
            is MainUiAction.OpenSettings -> openSettings()
            is MainUiAction.OpenAccountSettings -> openAccountSettings()
            is MainUiAction.OpenScanHistory -> openScanHistory()
            is MainUiAction.OpenUserProfileMenu -> openUserProfileMenu()
            is MainUiAction.OpenSubscriptionDetails -> openSubscriptionDetails()
            is MainUiAction.ShowScanHowItWorksPopup -> showScanHowItWorksPopup()
            is MainUiAction.DismissScanHowItWorksPopup -> dismissScanHowItWorksPopup()
            is MainUiAction.MarkScanHowItWorksPopupSeen -> markScanHowItWorksPopupSeen()
            is MainUiAction.ShowCustomerSupportDialog -> showCustomerSupportDialog()
            is MainUiAction.DismissCustomerSupportDialog -> dismissCustomerSupportDialog()
            is MainUiAction.Logout -> logout()
            is MainUiAction.StartNewChat -> startNewChat()
        }
    }

    /**
     * Start a new chat by using a more direct approach
     * This avoids the visual glitch of switching tabs
     */
    private fun startNewChat() {
        // Instead of switching tabs, we'll use a special flag to trigger a reset
        // in the ChatScanScreen composable
        viewModelScope.launch {
            // Set a temporary flag in SharedPreferences that ChatScanScreen can observe
            val sharedPrefs = context.getSharedPreferences("dome_ai_prefs", Context.MODE_PRIVATE)

            // Use the current timestamp as a unique trigger value
            val resetTrigger = System.currentTimeMillis()
            sharedPrefs.edit().putLong("chat_reset_trigger", resetTrigger).apply()

            // Log the action for debugging
            android.util.Log.d("MainViewModel", "Chat reset triggered with value: $resetTrigger")
        }
    }

    private fun loadOverlayState() {
        viewModelScope.launch {
            try {
                overlayRepository.getOverlayServiceState().collectLatest { state ->
                    updateState {
                        it.copy(
                            isOverlayEnabled = state.isEnabled,
                            hasOverlayPermission = state.hasPermission,
                            isLoading = false
                        )
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("MainViewModel", "Error loading overlay state: ${e.message}")
                updateState { it.copy(isLoading = false) }
            }
        }
    }

    private fun loadRecentScans() {
        viewModelScope.launch {
            try {
                overlayRepository.getRecentScans().collectLatest { scans ->
                    updateState { it.copy(recentScans = scans, isLoading = false) }
                }
            } catch (e: Exception) {
                android.util.Log.e("MainViewModel", "Error loading recent scans: ${e.message}")
                updateState { it.copy(isLoading = false) }
            }
        }
    }

    private fun toggleOverlay(enabled: Boolean) {
        viewModelScope.launch {
            try {
                android.util.Log.d("MainViewModel", "Toggle overlay requested: $enabled (DISABLED)")

                // Make sure all services are stopped
                serviceManager.stopOverlayService()

                // Update the UI state to show overlay is disabled
                updateState { it.copy(isOverlayEnabled = false) }

                // Show a toast to inform the user
                Toast.makeText(
                    context,
                    "Overlay functionality has been disabled in this version",
                    Toast.LENGTH_SHORT
                ).show()

            } catch (e: Exception) {
                android.util.Log.e("MainViewModel", "Error in toggleOverlay: ${e.message}")
            }
        }
    }

    private fun requestOverlayPermission() {
        // Don't request permission, just show a toast
        Toast.makeText(
            context,
            "Overlay functionality has been disabled in this version",
            Toast.LENGTH_SHORT
        ).show()
    }

    private fun updatePermissionState(hasPermission: Boolean) {
        viewModelScope.launch {
            try {
                android.util.Log.d("MainViewModel", "Updating permission state: $hasPermission")

                // Make sure the overlay is disabled
                overlayRepository.setOverlayServiceEnabled(false)

                // Update the UI state to reflect the permission status
                updateState {
                    it.copy(
                        hasOverlayPermission = hasPermission,
                        isOverlayEnabled = false
                    )
                }

                // Make sure any existing services are stopped
                serviceManager.stopOverlayService()

                android.util.Log.d("MainViewModel", "Permission state updated, overlay disabled")
            } catch (e: Exception) {
                android.util.Log.e("MainViewModel", "Error updating permission state: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    private fun selectTab(tab: MainTab) {
        updateState { it.copy(selectedTab = tab) }

        // If the Scan tab is selected, refresh the user's subscription plan
        if (tab == MainTab.SCAN) {
            refreshScanTabUserPlan()
        }
    }

    /**
     * Refresh the user's subscription plan in the Scan tab
     * Uses shared preferences to trigger a refresh in the ChatScanViewModel
     */
    private fun refreshScanTabUserPlan() {
        try {
            // Use shared preferences to trigger a refresh in the ChatScanViewModel
            val sharedPrefs = context.getSharedPreferences("dome_ai_prefs", Context.MODE_PRIVATE)

            // Use the current timestamp as a unique trigger value
            val refreshTrigger = System.currentTimeMillis()
            sharedPrefs.edit().putLong("plan_refresh_trigger", refreshTrigger).apply()

            android.util.Log.d("MainViewModel", "Plan refresh triggered with value: $refreshTrigger")
        } catch (e: Exception) {
            android.util.Log.e("MainViewModel", "Error triggering plan refresh: ${e.message}")
        }
    }

    private fun openScanDetail(scanId: String) {
        sendEvent(MainUiEvent.NavigateToScanDetail(scanId))
    }

    private fun loadHowItWorksPopupState() {
        try {
            // Load the preference from SharedPreferences
            val sharedPrefs = context.getSharedPreferences("dome_ai_prefs", Context.MODE_PRIVATE)
            val hasSeenPopup = sharedPrefs.getBoolean("has_seen_how_it_works", false)

            updateState {
                it.copy(hasSeenScanHowItWorksPopup = hasSeenPopup)
            }
        } catch (e: Exception) {
            android.util.Log.e("MainViewModel", "Error loading popup state: ${e.message}")
        }
    }

    private fun startManualScan() {
        // Check if the user has seen the "How It Works" popup
        if (!uiState.value.hasSeenScanHowItWorksPopup) {
            // Show the popup first
            showScanHowItWorksPopup()
        } else {
            // Navigate directly to the scan tab
            selectTab(MainTab.SCAN)
        }
    }

    private fun showScanHowItWorksPopup() {
        updateState { it.copy(showScanHowItWorksPopup = true) }
    }

    private fun dismissScanHowItWorksPopup() {
        updateState { it.copy(showScanHowItWorksPopup = false) }
    }

    private fun markScanHowItWorksPopupSeen() {
        viewModelScope.launch {
            try {
                // Save the preference to SharedPreferences
                val sharedPrefs = context.getSharedPreferences("dome_ai_prefs", Context.MODE_PRIVATE)
                sharedPrefs.edit().putBoolean("has_seen_how_it_works", true).apply()

                updateState {
                    it.copy(
                        hasSeenScanHowItWorksPopup = true,
                        showScanHowItWorksPopup = false
                    )
                }

                // Navigate to the scan tab
                selectTab(MainTab.SCAN)
            } catch (e: Exception) {
                android.util.Log.e("MainViewModel", "Error marking popup as seen: ${e.message}")
            }
        }
    }

    private fun openSettings() {
        sendEvent(MainUiEvent.NavigateToSettings)
    }

    private fun openAccountSettings() {
        sendEvent(MainUiEvent.NavigateToAccountSettings)
    }

    private fun openScanHistory() {
        sendEvent(MainUiEvent.NavigateToScanHistory)
    }

    private fun openUserProfileMenu() {
        sendEvent(MainUiEvent.NavigateToUserProfileMenu)
    }

    private fun openSubscriptionDetails() {
        sendEvent(MainUiEvent.NavigateToSubscriptionDetails)
    }

    private fun showCustomerSupportDialog() {
        updateState { it.copy(showCustomerSupportDialog = true) }
    }

    private fun dismissCustomerSupportDialog() {
        updateState { it.copy(showCustomerSupportDialog = false) }
    }

    private fun loadAndToggleWelcomeMessageState() {
        try {
            // Load the previous state from SharedPreferences
            val sharedPrefs = context.getSharedPreferences("dome_ai_prefs", Context.MODE_PRIVATE)
            val previousState = sharedPrefs.getBoolean("show_welcome_message", true)

            // Toggle the state for this session
            val newState = !previousState

            // Save the new state for next time - use commit() instead of apply() for immediate write
            sharedPrefs.edit().putBoolean("show_welcome_message", newState).commit()

            // Log the change for debugging
            android.util.Log.d("MainViewModel", "Welcome message toggled from $previousState to $newState")

            // Update the UI state
            updateState { it.copy(showWelcomeMessage = newState) }

            // Force a small delay to ensure the state is properly updated
            viewModelScope.launch {
                kotlinx.coroutines.delay(100)
                // Double-check that the state was updated correctly
                val currentState = sharedPrefs.getBoolean("show_welcome_message", true)
                if (currentState != newState) {
                    android.util.Log.e("MainViewModel", "Welcome message state not saved correctly!")
                    // Try again with a different key
                    sharedPrefs.edit().putBoolean("welcome_message_state", newState).commit()
                    updateState { it.copy(showWelcomeMessage = newState) }
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("MainViewModel", "Error toggling welcome message: ${e.message}")
            // Fallback to a default state
            updateState { it.copy(showWelcomeMessage = true) }
        }
    }

    private fun logout() {
        viewModelScope.launch {
            authRepository.logout()
            sendEvent(MainUiEvent.NavigateToLogin)
        }
    }
}
