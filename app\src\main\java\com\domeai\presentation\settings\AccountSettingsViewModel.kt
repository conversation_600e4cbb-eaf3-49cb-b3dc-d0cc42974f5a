package com.domeai.presentation.settings

import androidx.lifecycle.viewModelScope
import com.domeai.data.repository.AuthRepository
import com.domeai.data.repository.UserRepository
import com.domeai.presentation.common.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * UI State for Account Settings Screen
 */
data class AccountSettingsUiState(
    val name: String = "<PERSON>e", // Example data
    val email: String = "<EMAIL>", // Example data
    val phone: String = "", // Example data
    val profilePictureUri: String = "", // URI of the profile picture
    val isEditingProfile: Boolean = false,
    val subscriptionType: String = "Free Plan", // Example data
    val subscriptionExpiryDate: String = "", // Example data
    val emailNotificationsEnabled: Boolean = true,
    val pushNotificationsEnabled: Boolean = true,
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val showImagePicker: Boolean = false // Whether to show the image picker
) : com.domeai.presentation.common.UiState

/**
 * UI Events for Account Settings Screen
 */
sealed class AccountSettingsUiEvent : com.domeai.presentation.common.UiEvent {
    data class ShowSnackbar(val message: String) : AccountSettingsUiEvent()
    data object NavigateBack : AccountSettingsUiEvent()
    data object NavigateToChangePassword : AccountSettingsUiEvent()
    data object NavigateToSubscriptionDetails : AccountSettingsUiEvent()
    data object NavigateToPrivacySecurity : AccountSettingsUiEvent()
    data object OpenImagePicker : AccountSettingsUiEvent()
}

/**
 * UI Actions for Account Settings Screen
 */
sealed class AccountSettingsUiAction : com.domeai.presentation.common.UiAction {
    data object NavigateBack : AccountSettingsUiAction()
    data object EditProfile : AccountSettingsUiAction()
    data class UpdateName(val name: String) : AccountSettingsUiAction()
    data class UpdatePhone(val phone: String) : AccountSettingsUiAction()
    data object SaveProfileChanges : AccountSettingsUiAction()
    data object NavigateToChangePassword : AccountSettingsUiAction()
    data object NavigateToPrivacySecurity : AccountSettingsUiAction()
    data object NavigateToSubscriptionDetails : AccountSettingsUiAction()
    data class ToggleEmailNotifications(val enabled: Boolean) : AccountSettingsUiAction()
    data class TogglePushNotifications(val enabled: Boolean) : AccountSettingsUiAction()
    data object DeleteAccount : AccountSettingsUiAction()
    data object OpenImagePicker : AccountSettingsUiAction()
    data class UpdateProfilePicture(val uri: String) : AccountSettingsUiAction()
}

/**
 * ViewModel for Account Settings Screen
 */
@HiltViewModel
class AccountSettingsViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val userRepository: UserRepository
) : BaseViewModel<AccountSettingsUiState, AccountSettingsUiEvent, AccountSettingsUiAction>() {

    override fun createInitialState(): AccountSettingsUiState = AccountSettingsUiState()

    init {
        loadUserProfile()
    }

    private fun loadUserProfile() {
        viewModelScope.launch {
            updateState { it.copy(isLoading = true) }

            try {
                // In a real app, this would fetch user data from the repository
                // For now, we'll use example data
                val userData = userRepository.getUserProfile()

                updateState {
                    it.copy(
                        name = userData.name,
                        email = userData.email,
                        phone = userData.phone,
                        subscriptionType = userData.subscriptionType,
                        subscriptionExpiryDate = userData.subscriptionExpiryDate,
                        emailNotificationsEnabled = userData.emailNotificationsEnabled,
                        pushNotificationsEnabled = userData.pushNotificationsEnabled,
                        isLoading = false
                    )
                }
            } catch (e: Exception) {
                updateState {
                    it.copy(
                        errorMessage = "Failed to load profile: ${e.message}",
                        isLoading = false
                    )
                }
                sendEvent(AccountSettingsUiEvent.ShowSnackbar("Failed to load profile"))
            }
        }
    }

    override fun handleAction(action: AccountSettingsUiAction) {
        when (action) {
            is AccountSettingsUiAction.NavigateBack -> navigateBack()
            is AccountSettingsUiAction.EditProfile -> editProfile()
            is AccountSettingsUiAction.UpdateName -> updateName(action.name)
            is AccountSettingsUiAction.UpdatePhone -> updatePhone(action.phone)
            is AccountSettingsUiAction.SaveProfileChanges -> saveProfileChanges()
            is AccountSettingsUiAction.NavigateToChangePassword -> navigateToChangePassword()
            is AccountSettingsUiAction.NavigateToPrivacySecurity -> navigateToPrivacySecurity()
            is AccountSettingsUiAction.NavigateToSubscriptionDetails -> navigateToSubscriptionDetails()
            is AccountSettingsUiAction.ToggleEmailNotifications -> toggleEmailNotifications(action.enabled)
            is AccountSettingsUiAction.TogglePushNotifications -> togglePushNotifications(action.enabled)
            is AccountSettingsUiAction.DeleteAccount -> deleteAccount()
            is AccountSettingsUiAction.OpenImagePicker -> openImagePicker()
            is AccountSettingsUiAction.UpdateProfilePicture -> updateProfilePicture(action.uri)
        }
    }

    private fun navigateBack() {
        sendEvent(AccountSettingsUiEvent.NavigateBack)
    }

    private fun editProfile() {
        updateState { it.copy(isEditingProfile = true) }
    }

    private fun updateName(name: String) {
        updateState { it.copy(name = name) }
    }

    private fun updatePhone(phone: String) {
        updateState { it.copy(phone = phone) }
    }

    private fun saveProfileChanges() {
        viewModelScope.launch {
            updateState { it.copy(isLoading = true) }

            try {
                // In a real app, this would save the changes to the repository
                val currentState = uiState.value
                userRepository.updateUserProfile(
                    name = currentState.name,
                    phone = currentState.phone
                )

                updateState {
                    it.copy(
                        isEditingProfile = false,
                        isLoading = false
                    )
                }

                sendEvent(AccountSettingsUiEvent.ShowSnackbar("Profile updated successfully"))
            } catch (e: Exception) {
                updateState {
                    it.copy(
                        errorMessage = "Failed to save changes: ${e.message}",
                        isLoading = false
                    )
                }

                sendEvent(AccountSettingsUiEvent.ShowSnackbar("Failed to save changes"))
            }
        }
    }

    private fun navigateToChangePassword() {
        sendEvent(AccountSettingsUiEvent.NavigateToChangePassword)
    }

    private fun navigateToPrivacySecurity() {
        sendEvent(AccountSettingsUiEvent.NavigateToPrivacySecurity)
    }

    private fun navigateToSubscriptionDetails() {
        sendEvent(AccountSettingsUiEvent.NavigateToSubscriptionDetails)
    }

    private fun toggleEmailNotifications(enabled: Boolean) {
        viewModelScope.launch {
            updateState { it.copy(isLoading = true) }

            try {
                // In a real app, this would update the notification settings in the repository
                userRepository.updateEmailNotifications(enabled)

                updateState {
                    it.copy(
                        emailNotificationsEnabled = enabled,
                        isLoading = false
                    )
                }

                sendEvent(AccountSettingsUiEvent.ShowSnackbar(
                    if (enabled) "Email notifications enabled" else "Email notifications disabled"
                ))
            } catch (e: Exception) {
                updateState {
                    it.copy(
                        errorMessage = "Failed to update notification settings: ${e.message}",
                        isLoading = false
                    )
                }

                sendEvent(AccountSettingsUiEvent.ShowSnackbar("Failed to update notification settings"))
            }
        }
    }

    private fun togglePushNotifications(enabled: Boolean) {
        viewModelScope.launch {
            updateState { it.copy(isLoading = true) }

            try {
                // In a real app, this would update the notification settings in the repository
                userRepository.updatePushNotifications(enabled)

                updateState {
                    it.copy(
                        pushNotificationsEnabled = enabled,
                        isLoading = false
                    )
                }

                sendEvent(AccountSettingsUiEvent.ShowSnackbar(
                    if (enabled) "Push notifications enabled" else "Push notifications disabled"
                ))
            } catch (e: Exception) {
                updateState {
                    it.copy(
                        errorMessage = "Failed to update notification settings: ${e.message}",
                        isLoading = false
                    )
                }

                sendEvent(AccountSettingsUiEvent.ShowSnackbar("Failed to update notification settings"))
            }
        }
    }

    private fun deleteAccount() {
        viewModelScope.launch {
            updateState { it.copy(isLoading = true) }

            try {
                // In a real app, this would delete the user's account
                // For now, we'll just show a snackbar

                updateState { it.copy(isLoading = false) }

                sendEvent(AccountSettingsUiEvent.ShowSnackbar("Account deletion is not implemented in this demo"))
            } catch (e: Exception) {
                updateState {
                    it.copy(
                        errorMessage = "Failed to delete account: ${e.message}",
                        isLoading = false
                    )
                }

                sendEvent(AccountSettingsUiEvent.ShowSnackbar("Failed to delete account"))
            }
        }
    }

    private fun openImagePicker() {
        // Trigger the image picker
        sendEvent(AccountSettingsUiEvent.OpenImagePicker)
    }

    private fun updateProfilePicture(uri: String) {
        viewModelScope.launch {
            updateState { it.copy(isLoading = true) }

            try {
                // In a real app, this would upload the image to a server
                // For now, we'll just update the local state

                // Update the user profile with the new image URI
                userRepository.updateProfilePicture(uri)

                // Update the UI state
                updateState {
                    it.copy(
                        profilePictureUri = uri,
                        isLoading = false
                    )
                }

                sendEvent(AccountSettingsUiEvent.ShowSnackbar("Profile picture updated successfully"))
            } catch (e: Exception) {
                updateState {
                    it.copy(
                        errorMessage = "Failed to update profile picture: ${e.message}",
                        isLoading = false
                    )
                }

                sendEvent(AccountSettingsUiEvent.ShowSnackbar("Failed to update profile picture"))
            }
        }
    }
}
