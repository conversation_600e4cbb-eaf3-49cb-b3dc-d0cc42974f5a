#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test the scan session feature with a multi-image conversational scam scenario.
"""

import asyncio
import logging
import sys
import uuid
import os
import requests
import time
from datetime import datetime, timezone
from typing import List, Optional

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan_session import ScanSession
from app.models.scan import Scan
from app.crud import crud_scan_session, crud_scan
from app.schemas.scan_session import ScanSessionCreate
from app.schemas.scan import ScanCreate

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Instead of using Google Drive URLs, let's use placeholder images from the local filesystem
# We'll create these placeholder images in the script
IMAGE_NAMES = [
    "image_1.jpg",
    "image_2.jpg",
    "image_3.jpg",
    "image_4.jpg"
]

# User context for the first scan
USER_CONTEXT = """They almost got me. My intuition is almost on point. I was so close too. Holy Shit. My intuition is "almost" on point. I finally got one. So I'm on a "dating" app. I matched with a girl in my area. She seemed a little too good to be true. So I of course proceeded with caution. When she asked me personal details, I didn't tell her much. My big mistake was to add her on my personal instagram. Onto the good part. She started getting sexual immediately. I told her I don't send pics until I meet the person. This upset her because she had already sent pics over dm. So I sent her a couple fully clothed pics of me in the mirror. This upset her, she wanted a "limp dick" picture. Red flag, what girl wants to see it limp. 😂😂 After she asked for my number and I turned that down, she turned down my offer to video call. Here's a few of the chats. I warned the people on my Instagram about a potential fake dick pic incoming. Will update if this gets more interesting."""

def create_placeholder_image(image_name: str, output_path: str) -> str:
    """
    Create a placeholder image.

    Args:
        image_name: Name of the image
        output_path: Directory to save the image

    Returns:
        Path to the created image
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)

    # Create a placeholder image
    local_filename = os.path.join(output_path, image_name)

    # Create a simple text file as a placeholder
    with open(local_filename, 'w') as f:
        f.write(f"This is a placeholder for {image_name}")

    logger.info(f"Created placeholder image at {local_filename}")
    return local_filename

def create_premium_user(db: Session) -> User:
    """
    Create a premium tier test user.

    Args:
        db: Database session

    Returns:
        The created user
    """
    # Create a test user with premium tier
    # Use a unique email with a timestamp to avoid conflicts
    unique_email = f"test_premium_user_{int(time.time())}@example.com"

    test_user = User(
        email=unique_email,
        hashed_password="hashed_password",
        subscription_tier="premium",
        monthly_scan_allowance=100,
        scans_this_month=0,
        scan_counter_reset_at=datetime.now(timezone.utc)
    )

    # Add the user to the database
    db.add(test_user)
    db.commit()
    db.refresh(test_user)

    logger.info(f"Created premium test user with ID {test_user.id}")
    return test_user

def create_scan_session(db: Session, user_id: int) -> ScanSession:
    """
    Create a scan session.

    Args:
        db: Database session
        user_id: ID of the user who owns the session

    Returns:
        The created scan session
    """
    # Create a scan session
    session = crud_scan_session.create_scan_session(
        db=db, owner_id=user_id, title="Conversational Scam Test"
    )

    logger.info(f"Created scan session with ID {session.id}")
    return session

def submit_image_to_session(
    db: Session,
    user_id: int,
    image_path: str,
    session_id: Optional[uuid.UUID] = None,
    user_provided_context: Optional[str] = None
) -> Scan:
    """
    Submit an image to a scan session.

    Args:
        db: Database session
        user_id: ID of the user who owns the scan
        image_path: Path to the image file
        session_id: Optional ID of the scan session
        user_provided_context: Optional user-provided context

    Returns:
        The created scan
    """
    # Create a scan directly without using ScanCreate schema
    db_scan = Scan(
        owner_id=user_id,
        status="pending",
        input_content_type="image_path",
        user_provided_context=user_provided_context,
        raw_input_payload={"file_path": image_path},
        scan_session_id=session_id
    )

    # Add the scan to the database
    db.add(db_scan)
    db.commit()
    db.refresh(db_scan)

    logger.info(f"Created scan with ID {db_scan.id}")

    # Simulate the Celery task processing
    logger.info(f"Simulating Celery task processing for scan {db_scan.id}")

    # Update scan status to "processing"
    crud_scan.update_scan_status(db=db, db_scan=db_scan, status="processing")

    # Check if this scan is part of a session
    previous_scans_in_session = []
    if db_scan.scan_session_id:
        logger.info(f"Scan {db_scan.id} is part of session {db_scan.scan_session_id}")

        # Get previous scans in this session
        previous_scans_in_session = crud_scan_session.get_previous_scans_in_session(
            db=db,
            session_id=db_scan.scan_session_id,
            current_scan_id=db_scan.id
        )

        # Log information about previous scans
        logger.info(f"Found {len(previous_scans_in_session)} previous scans in this session")

        # Log details of previous scans
        for prev_scan in previous_scans_in_session:
            source = prev_scan.input_content_type

            # Extract text snippet if available
            text_snippet = None
            if prev_scan.analysis_result and "extracted_text" in prev_scan.analysis_result:
                text_snippet = prev_scan.analysis_result["extracted_text"][:100] + "..."
            elif prev_scan.input_text:
                text_snippet = prev_scan.input_text[:100] + "..."

            logger.info(f"Previous scan {prev_scan.id} - Source: {source}, Text: {text_snippet}")

    # Simulate AI analysis result
    analysis_result = {
        "risk_score": 0.85,
        "detected_red_flags": [
            "Requesting intimate photos",
            "Moving quickly to sexual content",
            "Refusing video verification"
        ],
        "explanation": "This appears to be a classic romance scam where the scammer quickly moves to sexual content and requests intimate photos. The refusal to video call is a major red flag.",
        "recommendations": "Do not send any intimate photos. Block and report this account.",
        "confidence_level": "High",
        "model_used": "gpt-4.1_with_RAG"
    }

    # Update scan with result and set status to "completed"
    # Since we're using a dict instead of ScanResultData, update directly
    db_scan.status = "completed"
    db_scan.analysis_result = analysis_result
    db.add(db_scan)
    db.commit()
    db.refresh(db_scan)

    logger.info(f"Completed processing of scan {db_scan.id}")

    return db_scan

def test_scan_session_scenario():
    """Test the scan session feature with a multi-image conversational scam scenario."""
    logger.info("Testing Scan Session Feature with Multi-Image Conversational Scam Scenario")

    # Create a database session
    db = SessionLocal()
    try:
        # Create a premium tier test user
        test_user = create_premium_user(db)

        # Create placeholder images
        image_paths = []
        for image_name in IMAGE_NAMES:
            image_path = create_placeholder_image(image_name, "temp_images")
            image_paths.append(image_path)

        # Create a scan session explicitly (Step 1)
        session = create_scan_session(db, test_user.id)
        session_id = session.id

        # Track scan credits before
        scans_before = test_user.scans_this_month
        logger.info(f"Scans before: {scans_before}")

        # Submit Image 1 (Step 2)
        logger.info("Submitting Image 1")
        scan1 = submit_image_to_session(
            db,
            test_user.id,
            image_paths[0],
            session_id=session_id,
            user_provided_context=USER_CONTEXT
        )

        # Verify scan credit consumption
        db.refresh(test_user)
        logger.info(f"Scans after Image 1: {test_user.scans_this_month}")
        logger.info(f"Scan credit consumed: {test_user.scans_this_month - scans_before}")

        # Submit Image 2 (Step 3)
        logger.info("Submitting Image 2")
        scan2 = submit_image_to_session(
            db,
            test_user.id,
            image_paths[1],
            session_id=session_id
        )

        # Verify scan credit consumption
        db.refresh(test_user)
        logger.info(f"Scans after Image 2: {test_user.scans_this_month}")

        # Submit Image 3 (Step 4)
        logger.info("Submitting Image 3")
        scan3 = submit_image_to_session(
            db,
            test_user.id,
            image_paths[2],
            session_id=session_id
        )

        # Verify scan credit consumption
        db.refresh(test_user)
        logger.info(f"Scans after Image 3: {test_user.scans_this_month}")

        # Submit Image 4 (Step 5)
        logger.info("Submitting Image 4")
        scan4 = submit_image_to_session(
            db,
            test_user.id,
            image_paths[3],
            session_id=session_id
        )

        # Verify scan credit consumption
        db.refresh(test_user)
        logger.info(f"Scans after Image 4: {test_user.scans_this_month}")
        logger.info(f"Total scan credits consumed: {test_user.scans_this_month - scans_before}")

        # Retrieve the final session details
        final_session = crud_scan_session.get_scan_session(
            db=db, session_id=session_id, owner_id=test_user.id
        )

        logger.info(f"Final session details:")
        logger.info(f"Session ID: {final_session.id}")
        logger.info(f"Session title: {final_session.title}")
        logger.info(f"Session created at: {final_session.created_at}")
        logger.info(f"Session last activity at: {final_session.last_activity_at}")

        # Verify all scans are associated with this session
        session_scans = db.query(Scan).filter(Scan.scan_session_id == session_id).all()
        logger.info(f"Number of scans in session: {len(session_scans)}")
        logger.info(f"Scan IDs in session: {[scan.id for scan in session_scans]}")

        # Clean up
        for scan in session_scans:
            db.delete(scan)
        db.delete(final_session)
        db.delete(test_user)
        db.commit()
        logger.info("Test data deleted")

        # Clean up downloaded images
        for image_path in image_paths:
            if os.path.exists(image_path):
                os.remove(image_path)
                logger.info(f"Deleted temporary image: {image_path}")

    finally:
        db.close()

def main():
    """Run the test."""
    test_scan_session_scenario()

if __name__ == "__main__":
    main()
