import asyncio
import psycopg2
import sys
sys.path.insert(0, '/app')
from app.services.ai_services import OpenAIModelService
import os

async def add_chunks():
    chunks = [
        {
            "content": "A particularly insidious crypto scam is 'Pig Butchering' (<PERSON><PERSON>). Scammers build a long-term relationship with the victim, gaining their trust over weeks or months. Eventually, they introduce a lucrative crypto investment opportunity through a fraudulent platform they control.",
            "source": "kb_crypto_pig_butchering_v1"
        },
        {
            "content": "On dating apps, scammers employ love bombing - overwhelming you with intense affection and declarations of love very early in the relationship. They aim to build a strong emotional connection quickly to make you more susceptible to later manipulation.",
            "source": "kb_dating_app_love_bombing_v1"
        }
    ]
    
    ai_service = OpenAIModelService(api_key=os.getenv('OPENAI_API_KEY'))
    conn = psycopg2.connect(host='db', port='5432', database='dome_app_main_db', user='dome_api_user', password='Dome2025ApiSecure!')
    cur = conn.cursor()
    
    try:
        for i, chunk in enumerate(chunks):
            print(f'Processing chunk {i+1}: {chunk["source"]}')
            embedding = await ai_service.get_text_embedding(text=chunk['content'])
            embedding_str = '[' + ','.join(map(str, embedding)) + ']'
            cur.execute('INSERT INTO knowledge_base_chunks (content, embedding, source, created_at) VALUES (%s, %s::vector(1536), %s, now())', (chunk['content'], embedding_str, chunk['source']))
            print(f'Inserted chunk {i+1}')
        
        conn.commit()
        cur.execute('SELECT COUNT(*) FROM knowledge_base_chunks')
        count = cur.fetchone()[0]
        print(f'Total chunks: {count}')
        
    except Exception as e:
        print(f'Error: {e}')
        conn.rollback()
    finally:
        cur.close()
        conn.close()

if __name__ == '__main__':
    asyncio.run(add_chunks())
