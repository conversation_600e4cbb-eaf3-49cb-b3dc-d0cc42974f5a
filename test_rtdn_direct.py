"""
Test RTDN scenarios for Google Play subscriptions using direct HTTP requests.

This script tests the following scenarios:
1. New Premium Subscription Purchase
2. Premium Subscription Cancellation
3. Premium Subscription Expiry
"""
import logging
import json
import base64
import requests
import time
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Test data
TEST_PURCHASE_TOKEN = "test_premium_purchase_token"
TEST_SUBSCRIPTION_ID = "domeai_placeholder_premium_monthly"
TEST_ORDER_ID = "GPA.1234-5678-9012-3456"
WEBHOOK_URL = "http://localhost:8000/api/v1/webhooks/googleplay/rtdn"


def create_subscription_purchased_notification():
    """
    Create a subscription purchased notification payload.
    
    Returns:
        A dictionary containing a subscription purchased notification
    """
    # Create a subscription purchased notification
    developer_notification = {
        "version": "1.0",
        "packageName": "com.domeai.scamdetector",
        "eventTimeMillis": str(int(time.time() * 1000)),
        "subscriptionNotification": {
            "version": "1.0",
            "notificationType": 4,  # SUBSCRIPTION_PURCHASED
            "purchaseToken": TEST_PURCHASE_TOKEN,
            "subscriptionId": TEST_SUBSCRIPTION_ID
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_subscription_canceled_notification():
    """
    Create a subscription canceled notification payload.
    
    Returns:
        A dictionary containing a subscription canceled notification
    """
    # Create a subscription canceled notification
    developer_notification = {
        "version": "1.0",
        "packageName": "com.domeai.scamdetector",
        "eventTimeMillis": str(int(time.time() * 1000)),
        "subscriptionNotification": {
            "version": "1.0",
            "notificationType": 3,  # SUBSCRIPTION_CANCELED
            "purchaseToken": TEST_PURCHASE_TOKEN,
            "subscriptionId": TEST_SUBSCRIPTION_ID
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_subscription_expired_notification():
    """
    Create a subscription expired notification payload.
    
    Returns:
        A dictionary containing a subscription expired notification
    """
    # Create a subscription expired notification
    developer_notification = {
        "version": "1.0",
        "packageName": "com.domeai.scamdetector",
        "eventTimeMillis": str(int(time.time() * 1000)),
        "subscriptionNotification": {
            "version": "1.0",
            "notificationType": 13,  # SUBSCRIPTION_EXPIRED
            "purchaseToken": TEST_PURCHASE_TOKEN,
            "subscriptionId": TEST_SUBSCRIPTION_ID
        }
    }
    
    return create_pubsub_message(developer_notification)


def create_pubsub_message(developer_notification):
    """
    Create a Pub/Sub message from a developer notification.
    
    Args:
        developer_notification: The developer notification to encode
        
    Returns:
        A dictionary containing a Pub/Sub message
    """
    # Encode the notification as base64
    encoded_data = base64.b64encode(json.dumps(developer_notification).encode("utf-8")).decode("utf-8")
    
    # Create the Pub/Sub message
    pubsub_message = {
        "message": {
            "data": encoded_data,
            "messageId": "test-message-id",
            "publishTime": datetime.now(timezone.utc).isoformat()
        },
        "subscription": "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
    }
    
    return pubsub_message


def send_notification(notification_payload):
    """
    Send a notification to the webhook endpoint.
    
    Args:
        notification_payload: The notification payload to send
        
    Returns:
        The response from the webhook endpoint
    """
    try:
        # Decode the notification for logging
        encoded_data = notification_payload["message"]["data"]
        decoded_data = json.loads(base64.b64decode(encoded_data).decode("utf-8"))
        logger.info(f"Sending notification: {json.dumps(decoded_data, indent=2)}")
        
        # Send the request
        response = requests.post(
            WEBHOOK_URL,
            json=notification_payload,
            headers={"Content-Type": "application/json"}
        )
        
        logger.info(f"Status code: {response.status_code}")
        logger.info(f"Response: {response.text}")
        
        return response
        
    except Exception as e:
        logger.error(f"Error sending notification: {str(e)}")
        return None


def test_subscription_purchased():
    """
    Test the subscription purchased scenario.
    """
    logger.info("Testing subscription purchased scenario")
    
    # Send the notification
    notification_payload = create_subscription_purchased_notification()
    response = send_notification(notification_payload)
    
    if not response or response.status_code != 200:
        logger.error("Failed to send notification.")
        return
    
    logger.info("Subscription purchased notification sent successfully.")


def test_subscription_canceled():
    """
    Test the subscription canceled scenario.
    """
    logger.info("Testing subscription canceled scenario")
    
    # Send the notification
    notification_payload = create_subscription_canceled_notification()
    response = send_notification(notification_payload)
    
    if not response or response.status_code != 200:
        logger.error("Failed to send notification.")
        return
    
    logger.info("Subscription canceled notification sent successfully.")


def test_subscription_expired():
    """
    Test the subscription expired scenario.
    """
    logger.info("Testing subscription expired scenario")
    
    # Send the notification
    notification_payload = create_subscription_expired_notification()
    response = send_notification(notification_payload)
    
    if not response or response.status_code != 200:
        logger.error("Failed to send notification.")
        return
    
    logger.info("Subscription expired notification sent successfully.")


if __name__ == "__main__":
    # Test the scenarios
    test_subscription_purchased()
    print("\n" + "-" * 80 + "\n")
    
    # Wait a moment before sending the next notification
    time.sleep(5)
    
    test_subscription_canceled()
    print("\n" + "-" * 80 + "\n")
    
    # Wait a moment before sending the next notification
    time.sleep(5)
    
    test_subscription_expired()
