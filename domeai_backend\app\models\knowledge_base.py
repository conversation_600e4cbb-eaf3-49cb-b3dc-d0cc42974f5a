from datetime import datetime
from typing import Optional, List

from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, func, Index
from sqlalchemy.dialects.postgresql import ARRAY, FLOAT
from sqlalchemy.types import TypeDecorator, UserDefinedType

from app.core.database import Base


class PGVector(UserDefinedType):
    """
    Custom type for PostgreSQL pgvector type.
    """
    def __init__(self, dimensions=None):
        self.dimensions = dimensions

    def get_col_spec(self, **kw):
        if self.dimensions:
            return f"vector({self.dimensions})"
        return "vector"

    def bind_processor(self, dialect):
        def process(value):
            if value is None:
                return None
            return value
        return process

    def result_processor(self, dialect, coltype):
        def process(value):
            if value is None:
                return None
            return value
        return process


class KnowledgeBaseChunk(Base):
    """
    Model for storing knowledge base chunks with their embeddings.

    This model uses PostgreSQL's pgvector extension to store and query embeddings.
    """
    __tablename__ = "knowledge_base_chunks"

    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    embedding = Column(PGVector(1536), nullable=False)  # Store as PostgreSQL vector
    source = Column(String, nullable=True)
    created_at = Column(DateTime, default=func.now())

    # We'll create the vector index in the migration script
    # since it requires pgvector-specific SQL
