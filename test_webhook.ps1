# Test payload
$payload = @{
    message = @{
        data = "eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJjb20uZG9tZWFpLnNjYW1kZXRlY3RvciIsImV2ZW50VGltZU1pbGxpcyI6IjE2MjEyMzQ1Njc4OTAiLCJ0ZXN0Tm90aWZpY2F0aW9uIjp7InZlcnNpb24iOiIxLjAifX0="
        messageId = "test-message-id"
        publishTime = "2023-05-20T10:00:00.000Z"
    }
    subscription = "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
} | ConvertTo-Json

Write-Host "Sending test notification..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/webhooks/googleplay/rtdn" -Method POST -Body $payload -ContentType "application/json"
    Write-Host "Response: $($response | ConvertTo-Json)"
}
catch {
    Write-Host "Error: $_"
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)"
    Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
}

Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
