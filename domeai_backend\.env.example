# API Settings
PROJECT_NAME=DomeAI Scam Detector API
API_V1_STR=/api/v1

# CORS Settings
BACKEND_CORS_ORIGINS=["http://localhost:8000", "http://localhost:3000"]

# Database Settings
DATABASE_URL=*****************************************************/dome_app_main_db
POSTGRES_USER=dome_app_user
# Note: Avoid special characters in passwords to prevent issues with SQL scripts
POSTGRES_PASSWORD=DomeAppPassword123
POSTGRES_DB=dome_app_main_db

# Security Settings
# Generate a strong random key with: openssl rand -hex 32
SECRET_KEY=p!*xdCuNmJ9CdPQEQzR$joBz4m%Hq&6AzpTDZrvY@j#f*6J*NRYwQewWPyV2FFBz
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Celery Settings
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# AI Service Settings
DEFAULT_AI_SERVICE=openai  # Options: openai, anthropic, google

# OpenAI API Settings
OPENAI_API_KEY=your_openai_key_here

# OpenAI model settings for different tiers
# Premium tier (GPT-4.1)
OPENAI_PREMIUM_ANALYSIS_MODEL_NAME=gpt-4.1
OPENAI_PREMIUM_EMBEDDING_MODEL_NAME=text-embedding-3-large
OPENAI_PREMIUM_EMBEDDING_DIMENSIONS=1536

# Basic tier (GPT-4.1 mini)
OPENAI_BASIC_ANALYSIS_MODEL_NAME=gpt-4.1-mini
OPENAI_BASIC_EMBEDDING_MODEL_NAME=text-embedding-3-small
OPENAI_BASIC_EMBEDDING_DIMENSIONS=1536

# Expert tier
OPENAI_EXPERT_ANALYSIS_MODEL_NAME=o4-mini
OPENAI_EXPERT_MULTIMODAL_MODEL_NAME=gpt-4.1
OPENAI_EXPERT_EMBEDDING_MODEL_NAME=text-embedding-3-large
OPENAI_EXPERT_EMBEDDING_DIMENSIONS=3072

# Session limits
SESSION_MAX_INPUTS_PREMIUM=10
SESSION_MAX_INPUTS_EXPERT=15
SESSION_ACTIVITY_WINDOW_HOURS=4

# Anthropic API Settings
ANTHROPIC_API_KEY=your_anthropic_key_here

# Google API Settings
GOOGLE_API_KEY=your_google_key_here
