package com.domeai.ui.theme

import androidx.compose.ui.graphics.Color

// Brand Primary Colors - Sophisticated Modern Blue
val PrimaryBlue = Color(0xFF0A6EBD)  // Deep vibrant blue
val PrimaryDark = Color(0xFF004C8C)  // Darker blue for containers
val PrimaryLight = Color(0xFF5B9BD5)  // Lighter blue for accents

// Brand Secondary Colors - Green Accent
val SecondaryGreen = Color(0xFF2E8B57)  // Sea green
val SecondaryDark = Color(0xFF1B5E20)  // Dark green for containers
val SecondaryLight = Color(0xFF66BB6A)  // Light green for accents

// Alert colors
val AlertRed = Color(0xFFE53935)  // Error red
val AlertYellow = Color(0xFFFDD835)  // Warning yellow
val AlertGreen = Color(0xFF43A047)  // Success green

// Neutral colors
val NeutralDark = Color(0xFF212121)  // Almost black
val NeutralMedium = Color(0xFF757575)  // Medium gray
val NeutralLight = Color(0xFFEEEEEE)  // Light gray
val NeutralWhite = Color(0xFFFFFFFF)  // White

// Dark theme colors
val DarkBackground = Color(0xFF121212)  // Standard dark background
val DarkSurface = Color(0xFF1E1E1E)  // Slightly lighter dark surface
val DarkError = Color(0xFFCF6679)  // Softer error for dark theme
