@echo off
echo Testing Google Play RTDN webhook...

echo.
echo 1. Sending Test Notification
python test_google_play_rtdn.py --type test
timeout /t 2 /nobreak > nul

echo.
echo 2. Sending Subscription Purchased Notification
python test_google_play_rtdn.py --type subscription_purchased
timeout /t 2 /nobreak > nul

echo.
echo 3. Sending Subscription Renewed Notification
python test_google_play_rtdn.py --type subscription_renewed
timeout /t 2 /nobreak > nul

echo.
echo 4. Sending Subscription Canceled Notification
python test_google_play_rtdn.py --type subscription_canceled
timeout /t 2 /nobreak > nul

echo.
echo 5. Sending Subscription Expired Notification
python test_google_play_rtdn.py --type subscription_expired
timeout /t 2 /nobreak > nul

echo.
echo 6. Sending Subscription Revoked Notification
python test_google_play_rtdn.py --type subscription_revoked
timeout /t 2 /nobreak > nul

echo.
echo 7. Sending One-Time Product Purchased Notification
python test_google_play_rtdn.py --type one_time_product_purchased
timeout /t 2 /nobreak > nul

echo.
echo 8. Sending Voided Purchase Refunded Notification
python test_google_play_rtdn.py --type voided_purchase_refunded
timeout /t 2 /nobreak > nul

echo.
echo All tests completed!
