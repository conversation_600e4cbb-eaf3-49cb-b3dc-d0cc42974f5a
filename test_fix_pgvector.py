import requests
import json

base_url = "https://domeai-backend.onrender.com"

def check_kb_status():
    """Check current knowledge base status"""
    print("📊 Checking knowledge base status...")
    try:
        response = requests.get(f"{base_url}/admin/check-kb-status", timeout=30)
        if response.status_code == 200:
            result = response.json()
            print("✅ Status check completed!")
            print(f"Total chunks: {result.get('total_chunks')}")
            print(f"Breakdown: {result.get('breakdown')}")
            print(f"Embedding column: {result.get('embedding_column')}")
            print(f"Vector operations: {result.get('vector_operations')}")
            return result
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return None

def load_real_kb():
    """Load the real 57 knowledge base chunks"""
    print("\n📚 Loading real knowledge base chunks...")
    try:
        response = requests.post(f"{base_url}/admin/load-real-kb", timeout=300)  # 5 minutes timeout
        if response.status_code == 200:
            result = response.json()
            print("✅ Knowledge base loading completed!")
            print(f"Files loaded: {result.get('files_loaded')}")
            print(f"Total processed: {result.get('total_chunks_processed')}")
            print(f"Successful: {result.get('successful')}")
            print(f"Failed: {result.get('failed')}")
            print(f"Final count in DB: {result.get('final_count_in_db')}")
            return result
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return None

if __name__ == "__main__":
    print("🚀 DomeAI Knowledge Base Setup")
    print("=" * 50)

    # Step 1: Check current status
    status = check_kb_status()

    if status and status.get('total_chunks', 0) < 50:
        print(f"\n⚠️ Only {status.get('total_chunks', 0)} chunks found. Loading real knowledge base...")
        load_real_kb()

        # Check status again
        print("\n🔄 Checking status after loading...")
        check_kb_status()
    else:
        print(f"\n✅ Knowledge base looks good with {status.get('total_chunks', 0)} chunks!")

    print("\n🎉 Setup complete! Your 57 knowledge base chunks should now be working.")
