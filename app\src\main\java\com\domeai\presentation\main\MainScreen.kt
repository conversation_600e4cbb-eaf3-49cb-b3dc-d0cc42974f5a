package com.domeai.presentation.main

import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.mutableStateOf
import com.domeai.service.ServiceManager
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Logout
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Shield
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.HorizontalDivider
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.hilt.navigation.compose.hiltViewModel
import com.domeai.presentation.scan.ChatScanScreenJetchat
import com.domeai.ui.composables.EmptyScanHistory
import com.domeai.ui.composables.LoadingIndicator
import com.domeai.ui.composables.MainBottomNavigation
import com.domeai.ui.composables.OverlayButtonCard
import com.domeai.ui.composables.ScanHistoryItem
import com.domeai.ui.composables.VerticalSpacer

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    onNavigateToManualScan: () -> Unit,
    onNavigateToSettings: () -> Unit,
    onNavigateToScanDetail: (String) -> Unit,
    onNavigateToLogin: () -> Unit,
    onNavigateToScanHistory: () -> Unit,
    onNavigateToAccountSettings: () -> Unit = {},
    onNavigateToUserProfileMenu: () -> Unit = {},
    onNavigateToSubscriptionDetails: () -> Unit = {},
    isDarkMode: Boolean = false,
    onToggleTheme: () -> Unit = {},
    viewModel: MainViewModel = hiltViewModel(),
    serviceManager: ServiceManager = androidx.hilt.navigation.compose.hiltViewModel<MainViewModel>().serviceManager
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    val context = LocalContext.current

    // Permission state for snackbar
    val permissionState = remember { mutableStateOf<Boolean?>(null) }

    // Handle permission result
    val permissionResultLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { _ ->
        // Check if permission was granted
        val hasPermission = Settings.canDrawOverlays(context)
        android.util.Log.d("MainScreen", "Permission check after returning: $hasPermission")

        // If permission was granted, update the UI state but keep the toggle off
        if (hasPermission) {
            android.util.Log.d("MainScreen", "Permission granted, but keeping overlay disabled")

            // Make sure any existing services are stopped
            serviceManager.stopOverlayService()

            // Update the UI state to show permission is granted but overlay is off
            viewModel.sendAction(MainUiAction.UpdatePermissionState(true))

            // Show a success message with instructions
            permissionState.value = true
        } else {
            android.util.Log.d("MainScreen", "Permission not granted")
            permissionState.value = false
        }

        // Refresh the state regardless
        viewModel.sendAction(MainUiAction.RefreshScans)
    }

    // Show permission result snackbar
    LaunchedEffect(permissionState.value) {
        if (permissionState.value != null) {
            if (permissionState.value == true) {
                snackbarHostState.showSnackbar("Permission granted! Now you can toggle the Overlay Protection switch to enable the floating button.")
            } else {
                snackbarHostState.showSnackbar("Overlay permission required to enable this feature")
            }
            permissionState.value = null
        }
    }

    // Handle UI events
    LaunchedEffect(key1 = viewModel) {
        viewModel.uiEvent.collect { event ->
            when (event) {
                is MainUiEvent.NavigateToManualScan -> onNavigateToManualScan()
                is MainUiEvent.NavigateToSettings -> onNavigateToSettings()
                is MainUiEvent.NavigateToAccountSettings -> onNavigateToAccountSettings()
                is MainUiEvent.NavigateToScanHistory -> onNavigateToScanHistory()
                is MainUiEvent.NavigateToScanDetail -> onNavigateToScanDetail(event.scanId)
                is MainUiEvent.NavigateToLogin -> onNavigateToLogin()
                is MainUiEvent.NavigateToUserProfileMenu -> onNavigateToUserProfileMenu()
                is MainUiEvent.NavigateToSubscriptionDetails -> onNavigateToSubscriptionDetails()
                is MainUiEvent.RequestOverlayPermission -> {
                    try {
                        val intent = Intent(
                            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                            Uri.parse("package:${context.packageName}")
                        )
                        permissionResultLauncher.launch(intent)
                    } catch (e: Exception) {
                        android.util.Log.e("MainScreen", "Error requesting permission: ${e.message}")
                        permissionState.value = false
                    }
                }
            }
        }
    }

    Scaffold(
        topBar = {
            // Use different TopAppBar based on the selected tab
            when (uiState.selectedTab) {
                MainTab.SCAN -> {
                    // Special TopAppBar for Scan tab with centered logo and specific layout
                    // This includes the New Chat button in the actions
                    ScanTabTopAppBar(
                        onNewChatClick = { viewModel.sendAction(MainUiAction.StartNewChat) },
                        onProfileClick = { viewModel.sendAction(MainUiAction.OpenUserProfileMenu) }
                    )
                }
                else -> {
                    // Standard TopAppBar for Home, History, and Settings tabs
                    // This has the same design but without the New Chat button
                    StandardTabTopAppBar(
                        onProfileClick = { viewModel.sendAction(MainUiAction.OpenUserProfileMenu) }
                    )
                }
            }
        },
        bottomBar = {
            MainBottomNavigation(
                selectedTab = uiState.selectedTab,
                onTabSelected = { viewModel.sendAction(MainUiAction.SelectTab(it)) }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                // Use a more subtle background that works with both light and dark mode
                .background(MaterialTheme.colorScheme.background)
        ) {
            if (uiState.isLoading) {
                LoadingIndicator(modifier = Modifier.align(Alignment.Center))
            } else {
                when (uiState.selectedTab) {
                    MainTab.HOME -> HomeTab(
                        isOverlayEnabled = uiState.isOverlayEnabled,
                        hasOverlayPermission = uiState.hasOverlayPermission,
                        recentScans = uiState.recentScans.take(3),
                        onToggleOverlay = { viewModel.sendAction(MainUiAction.ToggleOverlay(it)) },
                        onRequestPermission = { viewModel.sendAction(MainUiAction.RequestOverlayPermission) },
                        onScanClick = { viewModel.sendAction(MainUiAction.OpenScanDetail(it)) },
                        onViewAllClick = { viewModel.sendAction(MainUiAction.OpenScanHistory) },
                        onManualScanClick = { viewModel.sendAction(MainUiAction.StartManualScan) }
                    )

                    MainTab.SCAN -> ChatScanScreenJetchat(
                        onScanDetail = { viewModel.sendAction(MainUiAction.OpenScanDetail(it)) }
                    )

                    MainTab.HISTORY -> HistoryTab(
                        scans = uiState.recentScans,
                        onScanClick = { viewModel.sendAction(MainUiAction.OpenScanDetail(it)) }
                    )

                    MainTab.SETTINGS -> SettingsTab(
                        onSettingsClick = { viewModel.sendAction(MainUiAction.Logout) },
                        onAccountSettingsClick = { viewModel.sendAction(MainUiAction.OpenAccountSettings) },
                        isDarkMode = isDarkMode,
                        onToggleTheme = onToggleTheme
                    )
                }

                // Show the "How It Works" popup if needed
                if (uiState.showScanHowItWorksPopup) {
                    com.domeai.ui.composables.HowItWorksDialog(
                        onDismiss = { viewModel.sendAction(MainUiAction.DismissScanHowItWorksPopup) },
                        onGetStarted = { viewModel.sendAction(MainUiAction.MarkScanHowItWorksPopupSeen) }
                    )
                }

                // Show the Customer Support dialog if needed
                if (uiState.showCustomerSupportDialog) {
                    com.domeai.ui.composables.CustomerSupportDialog(
                        onDismiss = { viewModel.sendAction(MainUiAction.DismissCustomerSupportDialog) }
                    )
                }

                // Show Customer Support button on HOME and SETTINGS tabs
                if (uiState.selectedTab == MainTab.HOME || uiState.selectedTab == MainTab.SETTINGS) {
                    com.domeai.ui.composables.CustomerSupportButton(
                        onClick = { viewModel.sendAction(MainUiAction.ShowCustomerSupportDialog) },
                        modifier = Modifier.align(Alignment.BottomEnd)
                    )
                }
            }
        }
    }
}

@Composable
fun HomeTab(
    isOverlayEnabled: Boolean,
    hasOverlayPermission: Boolean,
    recentScans: List<com.domeai.data.model.ScanResult>,
    onToggleOverlay: (Boolean) -> Unit,
    onRequestPermission: () -> Unit,
    onScanClick: (String) -> Unit,
    onViewAllClick: () -> Unit,
    onManualScanClick: () -> Unit
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        item {
            // Dynamic welcome message
            WelcomeMessage()

            VerticalSpacer(height = 24)
        }

        item {
            Text(
                text = "Recent Scans",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )

            VerticalSpacer(height = 16)
        }

        if (recentScans.isEmpty()) {
            item {
                EmptyScanHistory()
            }
        } else {
            items(recentScans) { scan ->
                ScanHistoryItem(
                    scan = scan,
                    onClick = { onScanClick(scan.id) }
                )

                VerticalSpacer()
            }

            item {
                // Always show the View All button
                com.domeai.ui.composables.TextActionButton(
                    text = "View All Scans",
                    onClick = onViewAllClick,
                    modifier = Modifier.fillMaxWidth()
                )

                VerticalSpacer(height = 24)
            }
        }

        item {
            // New futuristic scan button
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(80.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .clickable(onClick = onManualScanClick),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primary
                ),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 8.dp
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "Scan",
                        tint = MaterialTheme.colorScheme.onPrimary,
                        modifier = Modifier.size(32.dp)
                    )

                    Spacer(modifier = Modifier.width(16.dp))

                    Text(
                        text = "Scan with Dome AI",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                }
            }

            VerticalSpacer(height = 24)

            // Overlay Protection section (moved from top to bottom)
            OverlayButtonCard(
                hasPermission = hasOverlayPermission,
                onEnableOverlay = { onToggleOverlay(true) },
                onDisableOverlay = { onToggleOverlay(false) },
                onRequestPermission = onRequestPermission
            )

            // Add extra space at the bottom
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

@Composable
fun WelcomeMessage(
    viewModel: MainViewModel = androidx.hilt.navigation.compose.hiltViewModel()
) {
    // Get the welcome message state from the ViewModel
    val uiState by viewModel.uiState.collectAsState()
    val showWelcomeMessage = uiState.showWelcomeMessage

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Icon
            Icon(
                imageVector = if (showWelcomeMessage) Icons.Default.Person else Icons.Default.Shield,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onPrimaryContainer,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            // Message text
            Text(
                text = if (showWelcomeMessage) {
                    "Welcome back, John! Stay vigilant."
                } else {
                    "I've flagged 37 scams so far, and counting!"
                },
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
        }
    }
}

@Composable
fun HistoryTab(
    scans: List<com.domeai.data.model.ScanResult>,
    onScanClick: (String) -> Unit
) {
    // Use the enhanced ScanHistoryScreen but adapt it to be a tab
    com.domeai.presentation.scan.ScanHistoryContent(
        onScanClick = onScanClick,
        showBackButton = false // Don't show back button in tab view
    )
}

/**
 * Common TopAppBar layout used by both standard tabs and scan tab
 * with shared layout elements for consistent appearance
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CommonTopAppBarLayout(
    content: @Composable RowScope.() -> Unit
) {
    TopAppBar(
        title = {
            // Use a Row with proper spacing for title and centered logo
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Left: App name with larger font size
                Text(
                    text = "Dome AI",
                    fontSize = MaterialTheme.typography.titleLarge.fontSize, // Explicitly use titleLarge fontSize (22.sp)
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(start = 4.dp)
                )

                // Push the logo to the center with weight
                Spacer(modifier = Modifier.weight(1f))

                // Center: Shield logo with precise sizing
                Box(
                    modifier = Modifier
                        .size(32.dp) // Exact size as requested
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Shield,
                        contentDescription = "Dome AI Logo",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(24.dp)
                    )
                }

                // Push the actions to the right with weight
                Spacer(modifier = Modifier.weight(1f))

                // Empty spacer to balance the title
                // This creates visual space for the actions to align properly
                Spacer(modifier = Modifier.width(48.dp))
            }
        },
        actions = content
    )
}

/**
 * Standard TopAppBar for Home, History, and Settings tabs
 * with centered logo and profile icon
 */
@Composable
fun StandardTabTopAppBar(
    onProfileClick: () -> Unit
) {
    CommonTopAppBarLayout {
        // Profile button - only action for standard tabs
        IconButton(
            onClick = onProfileClick,
            modifier = Modifier
                .padding(end = 4.dp)
                .size(48.dp) // Larger touch target (48.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(40.dp)  // Larger size for better visibility
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f))
                    .border(1.5.dp, MaterialTheme.colorScheme.primary, CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.AccountCircle,
                    contentDescription = "User Profile",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(30.dp) // Larger icon (30.dp)
                )
            }
        }
    }
}

/**
 * Dedicated TopAppBar for the Scan tab with specific layout requirements:
 * - Larger "Dome AI" title text
 * - Perfectly centered shield logo
 * - Larger action icons (New Chat and Profile)
 */
@Composable
fun ScanTabTopAppBar(
    onNewChatClick: () -> Unit,
    onProfileClick: () -> Unit
) {
    CommonTopAppBarLayout {
        // New Chat button - positioned FIRST (to the left of profile)
        IconButton(
            onClick = onNewChatClick,
            modifier = Modifier.size(48.dp) // Larger touch target (48.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = "New Chat",
                modifier = Modifier.size(30.dp) // Larger icon (30.dp)
            )
        }

        // Profile button - always shown as the LAST (rightmost) action
        IconButton(
            onClick = onProfileClick,
            modifier = Modifier
                .padding(end = 4.dp)
                .size(48.dp) // Larger touch target (48.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(40.dp)  // Larger size for better visibility
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f))
                    .border(1.5.dp, MaterialTheme.colorScheme.primary, CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.AccountCircle,
                    contentDescription = "User Profile",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(30.dp) // Larger icon (30.dp)
                )
            }
        }
    }
}

@Composable
fun SettingsTab(
    onSettingsClick: () -> Unit,
    onAccountSettingsClick: () -> Unit,
    isDarkMode: Boolean = false,
    onToggleTheme: () -> Unit = {},
    viewModel: MainViewModel = androidx.hilt.navigation.compose.hiltViewModel()
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        Text(
            text = "Settings",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 24.dp)
        )

        // Subscription Section (Added at the top as requested)
        com.domeai.ui.composables.SubscriptionCard(
            subscriptionType = "Premium Plan", // This would come from user data
            expiryDate = "Dec 31, 2023", // This would come from user data
            onManageSubscription = { viewModel.sendAction(MainUiAction.OpenSubscriptionDetails) }
        )

        VerticalSpacer(height = 16)

        // Theme Settings
        com.domeai.ui.composables.SettingsCard(
            title = "Appearance",
            description = "Customize the app's appearance",
            content = {
                com.domeai.ui.composables.SettingsSwitch(
                    title = "Dark Mode",
                    description = "Enable dark mode for better readability in low light",
                    checked = isDarkMode,
                    onCheckedChange = { onToggleTheme() }
                )
            }
        )

        VerticalSpacer(height = 16)

        // Account Settings Button (new design)
        com.domeai.ui.composables.AccountSettingsButton(
            onClick = onAccountSettingsClick
        )

        VerticalSpacer(height = 16)

        // Logout Button (new design)
        com.domeai.ui.composables.LogoutButton(
            onClick = onSettingsClick
        )

        VerticalSpacer(height = 16)

        // About Section (new design)
        com.domeai.ui.composables.AboutCard(
            appVersion = "2.0",
            appCopyright = "© 2023 DomeAI"
        )

        // Add extra space at the bottom for better scrolling
        Spacer(modifier = Modifier.height(40.dp))
    }
}
