"""
Webhook endpoints for external services.

This module contains endpoints for receiving webhooks from external services,
such as Google Play for Real-Time Developer Notifications (RTDNs).
"""
import base64
import json
import logging
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.schemas.google_play import PubSubMessageData, DeveloperNotification
from app.services import google_play_service

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/googleplay/rtdn", status_code=status.HTTP_200_OK)
async def google_play_rtdn(
    payload: PubSubMessageData,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Receive and process Google Play Real-Time Developer Notifications (RTDNs).
    
    This endpoint receives notifications from Google Cloud Pub/Sub when events
    occur in Google Play, such as subscription purchases, renewals, or cancellations.
    
    Args:
        payload: The Pub/Sub message payload
        db: Database session
        
    Returns:
        A success response to acknowledge receipt of the notification
    """
    logger.info(f"Received Google Play RTDN from subscription: {payload.subscription}")
    
    try:
        # Extract and decode the base64-encoded data
        encoded_data = payload.message.data
        decoded_data = base64.b64decode(encoded_data).decode("utf-8")
        
        # Parse the decoded data as JSON
        notification_data = json.loads(decoded_data)
        
        # Convert to DeveloperNotification model
        notification = DeveloperNotification(**notification_data)
        
        # Log the notification details
        logger.info(f"RTDN Version: {notification.version}")
        logger.info(f"Package Name: {notification.packageName}")
        logger.info(f"Event Time: {notification.eventTimeMillis}")
        
        if notification.subscriptionNotification:
            logger.info(f"Subscription Notification Type: {notification.subscriptionNotification.notificationType}")
            logger.info(f"Subscription ID: {notification.subscriptionNotification.subscriptionId}")
        elif notification.testNotification:
            logger.info("Test Notification")
        elif notification.oneTimeProductNotification:
            logger.info(f"One-Time Product Notification Type: {notification.oneTimeProductNotification.notificationType}")
        elif notification.voidedPurchaseNotification:
            logger.info(f"Voided Purchase Notification Type: {notification.voidedPurchaseNotification.notificationType}")
        
        # Process the notification (just log for now, actual processing will be implemented later)
        await google_play_service.process_rtdn(db, notification)
        
        # Return a success response to acknowledge receipt
        return {"status": "success", "message": "RTDN received and processed"}
    
    except Exception as e:
        logger.error(f"Error processing Google Play RTDN: {str(e)}", exc_info=True)
        # Still return a success response to prevent Pub/Sub from retrying
        # In a production environment, you might want to handle certain errors differently
        return {"status": "error", "message": str(e)}
