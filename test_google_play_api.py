"""
Test script for Google Play API calls.

This script tests the Google Play API call with graceful error handling.
"""
import asyncio
import logging
import json
import sys
import os
from typing import Dict, Any, Optional

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.google_play_service import get_subscription_data, _get_google_play_access_token

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Test data
TEST_SUBSCRIPTION_ID = "domeai_placeholder_premium_monthly"
TEST_PURCHASE_TOKEN = "test_purchase_token_123456789"
REAL_SUBSCRIPTION_ID = "domeai_premium_monthly"  # Replace with a real subscription ID if available
REAL_PURCHASE_TOKEN = "real_purchase_token"  # Replace with a real purchase token if available


async def test_get_access_token():
    """
    Test getting an access token from Google Play.
    """
    logger.info("Testing _get_google_play_access_token()")
    
    try:
        access_token = await _get_google_play_access_token()
        
        if access_token:
            logger.info("Successfully got access token")
            # Only show the first few characters for security
            logger.info(f"Access token: {access_token[:10]}...")
        else:
            logger.error("Failed to get access token")
    
    except Exception as e:
        logger.error(f"Error getting access token: {str(e)}")


async def test_get_subscription_data_with_mock():
    """
    Test getting subscription data with mock data.
    """
    logger.info("Testing get_subscription_data() with mock data")
    
    try:
        subscription_data = await get_subscription_data(
            TEST_SUBSCRIPTION_ID,
            TEST_PURCHASE_TOKEN,
            use_mock=True
        )
        
        if subscription_data:
            logger.info("Successfully got mock subscription data")
            logger.info(f"Subscription data: {json.dumps(subscription_data, indent=2)}")
        else:
            logger.error("Failed to get mock subscription data")
    
    except Exception as e:
        logger.error(f"Error getting mock subscription data: {str(e)}")


async def test_get_subscription_data_with_real_api():
    """
    Test getting subscription data with real API call.
    """
    logger.info("Testing get_subscription_data() with real API call")
    
    try:
        # First, try with test data (should fail gracefully)
        logger.info("Testing with test data (should fail gracefully)")
        subscription_data = await get_subscription_data(
            TEST_SUBSCRIPTION_ID,
            TEST_PURCHASE_TOKEN,
            use_mock=False
        )
        
        if subscription_data:
            logger.info("Successfully got subscription data with test data (unexpected)")
            logger.info(f"Subscription data: {json.dumps(subscription_data, indent=2)}")
        else:
            logger.info("Failed to get subscription data with test data (expected)")
        
        # Then, try with real data if available
        if REAL_PURCHASE_TOKEN != "real_purchase_token":
            logger.info("Testing with real data")
            subscription_data = await get_subscription_data(
                REAL_SUBSCRIPTION_ID,
                REAL_PURCHASE_TOKEN,
                use_mock=False
            )
            
            if subscription_data:
                logger.info("Successfully got subscription data with real data")
                logger.info(f"Subscription data: {json.dumps(subscription_data, indent=2)}")
            else:
                logger.error("Failed to get subscription data with real data")
    
    except Exception as e:
        logger.error(f"Error getting subscription data: {str(e)}")


async def main():
    """
    Main function.
    """
    # Test getting an access token
    await test_get_access_token()
    
    print("\n" + "-" * 80 + "\n")
    
    # Test getting subscription data with mock data
    await test_get_subscription_data_with_mock()
    
    print("\n" + "-" * 80 + "\n")
    
    # Test getting subscription data with real API call
    await test_get_subscription_data_with_real_api()


if __name__ == "__main__":
    asyncio.run(main())
