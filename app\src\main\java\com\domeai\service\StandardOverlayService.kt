package com.domeai.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.Toast
import androidx.core.app.NotificationCompat
import com.domeai.R
import com.domeai.presentation.main.MainActivity

/**
 * A standard overlay service that follows Android best practices
 */
class StandardOverlayService : Service() {

    private lateinit var windowManager: WindowManager
    private var overlayView: View? = null
    private lateinit var params: WindowManager.LayoutParams

    private var initialX: Int = 0
    private var initialY: Int = 0
    private var initialTouchX: Float = 0f
    private var initialTouchY: Float = 0f

    // Screen dimensions
    private var screenWidth: Int = 0
    private var screenHeight: Int = 0

    private val TAG = "StandardOverlayService"
    private val NOTIFICATION_CHANNEL_ID = "OverlayServiceChannel"
    private val NOTIFICATION_ID = 1003

    companion object {
        // Intent actions
        const val ACTION_START = "com.domeai.service.ACTION_START_STANDARD"
        const val ACTION_STOP = "com.domeai.service.ACTION_STOP_STANDARD"

        // Intent to start the service
        fun getStartIntent(context: Context): Intent {
            return Intent(context, StandardOverlayService::class.java).apply {
                action = ACTION_START
            }
        }

        // Intent to stop the service
        fun getStopIntent(context: Context): Intent {
            return Intent(context, StandardOverlayService::class.java).apply {
                action = ACTION_STOP
            }
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Service created")
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager

        // Get screen dimensions
        val displayMetrics = resources.displayMetrics
        screenWidth = displayMetrics.widthPixels
        screenHeight = displayMetrics.heightPixels

        // Create notification channel for Android O+
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "onStartCommand: ${intent?.action}")

        // Show a toast for debugging
        Toast.makeText(
            this,
            "StandardOverlayService onStartCommand: ${intent?.action}",
            Toast.LENGTH_SHORT
        ).show()

        when (intent?.action) {
            ACTION_START -> startOverlay()
            ACTION_STOP -> stopOverlay()
            else -> {
                Log.d(TAG, "No action specified, defaulting to START")
                startOverlay()
            }
        }

        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        removeOverlayView()
        Log.d(TAG, "Service destroyed")
    }

    private fun startOverlay() {
        try {
            Log.d(TAG, "Starting overlay...")

            // Show a toast for debugging
            android.widget.Toast.makeText(
                this,
                "Starting overlay in StandardOverlayService",
                android.widget.Toast.LENGTH_SHORT
            ).show()

            // Start as foreground service
            startForeground(NOTIFICATION_ID, createNotification())
            Log.d(TAG, "Started as foreground service")

            // Create and add the overlay view
            createOverlayView()

            Log.d(TAG, "Overlay started successfully")

            // Show a toast for debugging
            android.widget.Toast.makeText(
                this,
                "Overlay started successfully. Press Home to see it.",
                android.widget.Toast.LENGTH_LONG
            ).show()
        } catch (e: Exception) {
            Log.e(TAG, "Error starting overlay: ${e.message}")
            e.printStackTrace()

            // Show a toast for debugging
            android.widget.Toast.makeText(
                this,
                "Error starting overlay: ${e.message}",
                android.widget.Toast.LENGTH_LONG
            ).show()

            stopSelf()
        }
    }

    private fun createOverlayView() {
        try {
            Log.d(TAG, "Creating overlay view on thread: ${Thread.currentThread().name}")

            // Remove any existing view first
            removeOverlayView()

            // Inflate the floating view layout
            val inflater = getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            overlayView = inflater.inflate(R.layout.floating_view, null)

            // Check if inflation was successful
            if (overlayView == null) {
                Log.e(TAG, "CRITICAL ERROR: Overlay view is NULL after inflation!")
                Toast.makeText(this, "Error: Failed to create overlay view", Toast.LENGTH_LONG).show()
                stopSelf()
                return
            }

            Log.d(TAG, "Inflated overlayView: $overlayView, Parent: ${overlayView?.parent}, WindowToken: ${overlayView?.windowToken}")

            // Define layout parameters
            val layoutFlag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_PHONE
            }

            // Use fixed size for testing
            params = WindowManager.LayoutParams(
                150, // Fixed width for testing
                150, // Fixed height for testing
                layoutFlag,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT
            ).apply {
                // Use center gravity for testing
                gravity = Gravity.CENTER
                x = 0
                y = 0
            }

            Log.d(TAG, "Params: Width=${params.width}, Height=${params.height}, Type=${params.type}, " +
                    "Flags=${params.flags}, Gravity=${params.gravity}, X=${params.x}, Y=${params.y}")

            // Set up touch listener for dragging
            setupTouchListener()

            // Check manufacturer for special handling
            val manufacturer = Build.MANUFACTURER.lowercase()
            if (manufacturer.contains("xiaomi") || manufacturer.contains("redmi") ||
                manufacturer.contains("poco") || manufacturer.contains("miui")) {
                Log.w(TAG, "Detected Xiaomi/MIUI device: $manufacturer. May need additional permissions.")
                Toast.makeText(this,
                    "Xiaomi device detected. If overlay doesn't appear, check 'Display pop-up windows' permission in Settings > Apps > Your App > Permissions",
                    Toast.LENGTH_LONG).show()
            }

            // Add the view to the window manager with robust error handling
            Log.d(TAG, "Attempting to add overlay view...")

            try {
                // Double-check: Only add if not already added
                if (overlayView?.windowToken == null && overlayView?.parent == null) {
                    // Ensure we're on the main thread
                    if (Thread.currentThread().name != "main") {
                        Log.w(TAG, "Not on main thread! Switching to main thread for addView")
                        android.os.Handler(android.os.Looper.getMainLooper()).post {
                            try {
                                windowManager.addView(overlayView, params)
                                Log.i(TAG, "SUCCESS: windowManager.addView() called on main thread.")
                            } catch (e: Exception) {
                                Log.e(TAG, "Error adding view on main thread: ${e.message}", e)
                                Toast.makeText(this, "Error adding overlay: ${e.message}", Toast.LENGTH_LONG).show()
                            }
                        }
                    } else {
                        windowManager.addView(overlayView, params)
                        Log.i(TAG, "SUCCESS: windowManager.addView() called.")
                    }
                } else {
                    Log.w(TAG, "View already added or has token. Parent: ${overlayView?.parent}, WindowToken: ${overlayView?.windowToken}")
                }
            } catch (e: WindowManager.BadTokenException) {
                // Often related to invalid context or window type issues
                Log.e(TAG, "FAILED: BadTokenException - Is the service context valid? Check LayoutParams Type.", e)
                Toast.makeText(this, "Overlay Error: Bad Token", Toast.LENGTH_LONG).show()
                stopSelf()
            } catch (e: IllegalStateException) {
                // Can happen if view is already added elsewhere or state is inconsistent
                Log.e(TAG, "FAILED: IllegalStateException - View might be already added or WindowManager state issue.", e)
                Toast.makeText(this, "Overlay Error: Illegal State", Toast.LENGTH_LONG).show()
            } catch (e: SecurityException) {
                // Should be caught by canDrawOverlays, but could indicate OEM interference
                Log.e(TAG, "FAILED: SecurityException - Overlay Permission likely missing or blocked by OEM!", e)
                Toast.makeText(this, "Overlay Error: Permission Blocked", Toast.LENGTH_LONG).show()
                stopSelf()
            } catch (e: Exception) {
                // Catch any other unexpected exceptions
                Log.e(TAG, "FAILED: Unexpected Exception during addView: ${e.message}", e)
                Toast.makeText(this, "Overlay Error: ${e.message}", Toast.LENGTH_LONG).show()
                stopSelf()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error creating overlay view: ${e.message}")
            e.printStackTrace()
            Toast.makeText(this, "Error creating overlay: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun setupTouchListener() {
        overlayView?.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // Save initial position
                    initialX = params.x
                    initialY = params.y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    // Calculate new position
                    params.x = initialX + (event.rawX - initialTouchX).toInt()
                    params.y = initialY + (event.rawY - initialTouchY).toInt()

                    // Update the view position
                    windowManager.updateViewLayout(overlayView, params)
                    true
                }
                MotionEvent.ACTION_UP -> {
                    val moved = Math.abs(event.rawX - initialTouchX) > 10 ||
                            Math.abs(event.rawY - initialTouchY) > 10

                    if (moved) {
                        // Check if we're at the bottom of the screen
                        if (event.rawY > screenHeight - 200) {
                            // User dragged to bottom, remove the overlay
                            stopOverlay()
                            return@setOnTouchListener true
                        }

                        // Snap to nearest edge
                        snapToEdge()
                    } else {
                        // Handle click - open the app
                        val intent = Intent(this, MainActivity::class.java).apply {
                            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        }
                        startActivity(intent)
                    }
                    true
                }
                else -> false
            }
        }
    }

    private fun snapToEdge() {
        try {
            // Get button dimensions
            val buttonWidth = overlayView?.width ?: 100
            val buttonHeight = overlayView?.height ?: 100

            // Calculate distances to edges
            val distanceToLeft = params.x
            val distanceToRight = screenWidth - (params.x + buttonWidth)
            val distanceToTop = params.y
            val distanceToBottom = screenHeight - (params.y + buttonHeight)

            // Find the closest edge
            val minHorizontalDistance = Math.min(distanceToLeft, distanceToRight)
            val minVerticalDistance = Math.min(distanceToTop, distanceToBottom)

            // Snap to the closest edge
            if (minHorizontalDistance < minVerticalDistance) {
                // Snap horizontally
                if (distanceToLeft < distanceToRight) {
                    // Snap to left
                    params.x = 0
                } else {
                    // Snap to right
                    params.x = screenWidth - buttonWidth
                }
            } else {
                // Snap vertically
                if (distanceToTop < distanceToBottom) {
                    // Snap to top
                    params.y = 0
                } else {
                    // Snap to bottom
                    params.y = screenHeight - buttonHeight
                }
            }

            // Update the view position
            windowManager.updateViewLayout(overlayView, params)
            Log.d(TAG, "Snapped to edge: x=${params.x}, y=${params.y}")
        } catch (e: Exception) {
            Log.e(TAG, "Error snapping to edge: ${e.message}")
        }
    }

    private fun removeOverlayView() {
        if (overlayView != null) {
            try {
                windowManager.removeView(overlayView)
                overlayView = null
                Log.d(TAG, "Overlay view removed")
            } catch (e: Exception) {
                Log.e(TAG, "Error removing overlay view: ${e.message}")
            }
        }
    }

    private fun stopOverlay() {
        removeOverlayView()

        // Stop the foreground service
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            if (Build.VERSION.SDK_INT >= 31) { // Android 12+
                stopForeground(STOP_FOREGROUND_REMOVE)
            } else {
                stopForeground(true)
            }
        } else {
            stopForeground(true)
        }

        stopSelf()
        Log.d(TAG, "Overlay stopped")
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                "Overlay Service",
                NotificationManager.IMPORTANCE_LOW // Low importance so it's less intrusive
            )
            val manager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            manager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        // Intent to open your app when notification is tapped
        val notificationIntent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setContentTitle("DomeAI Scam Detector")
            .setContentText("Overlay protection is active")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .build()
    }
}
