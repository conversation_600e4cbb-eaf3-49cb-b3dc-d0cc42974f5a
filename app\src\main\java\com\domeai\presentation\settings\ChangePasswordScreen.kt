package com.domeai.presentation.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.domeai.ui.composables.PasswordField
import com.domeai.ui.composables.PrimaryButton
import com.domeai.ui.composables.VerticalSpacer

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChangePasswordScreen(
    onNavigateBack: () -> Unit,
    viewModel: ChangePasswordViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    
    // Handle UI events
    LaunchedEffect(key1 = viewModel) {
        viewModel.uiEvent.collect { event ->
            when (event) {
                is ChangePasswordUiEvent.NavigateBack -> onNavigateBack()
                is ChangePasswordUiEvent.ShowSnackbar -> {
                    snackbarHostState.showSnackbar(event.message)
                }
            }
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Change Password") },
                navigationIcon = {
                    IconButton(onClick = { viewModel.sendAction(ChangePasswordUiAction.NavigateBack) }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp)
        ) {
            Text(
                text = "Update Your Password",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            
            Text(
                text = "Enter your current password and a new password to update your credentials.",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                modifier = Modifier.padding(top = 8.dp, bottom = 24.dp)
            )
            
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    // Current Password
                    PasswordField(
                        password = uiState.currentPassword,
                        onPasswordChange = { viewModel.sendAction(ChangePasswordUiAction.UpdateCurrentPassword(it)) },
                        label = "Current Password",
                        errorMessage = uiState.currentPasswordError,
                        enabled = !uiState.isLoading,
                        imeAction = ImeAction.Next
                    )
                    
                    VerticalSpacer(height = 16)
                    
                    // New Password
                    PasswordField(
                        password = uiState.newPassword,
                        onPasswordChange = { viewModel.sendAction(ChangePasswordUiAction.UpdateNewPassword(it)) },
                        label = "New Password",
                        errorMessage = uiState.newPasswordError,
                        enabled = !uiState.isLoading,
                        imeAction = ImeAction.Next
                    )
                    
                    VerticalSpacer(height = 16)
                    
                    // Confirm New Password
                    PasswordField(
                        password = uiState.confirmNewPassword,
                        onPasswordChange = { viewModel.sendAction(ChangePasswordUiAction.UpdateConfirmNewPassword(it)) },
                        label = "Confirm New Password",
                        errorMessage = uiState.confirmNewPasswordError,
                        enabled = !uiState.isLoading,
                        imeAction = ImeAction.Done,
                        onImeAction = { viewModel.sendAction(ChangePasswordUiAction.ChangePassword) }
                    )
                    
                    VerticalSpacer(height = 24)
                    
                    // Change Password Button
                    PrimaryButton(
                        text = "Update Password",
                        onClick = { viewModel.sendAction(ChangePasswordUiAction.ChangePassword) },
                        isLoading = uiState.isLoading
                    )
                }
            }
            
            // Password Requirements
            PasswordRequirementsSection()
        }
    }
}

@Composable
fun PasswordRequirementsSection() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 24.dp)
    ) {
        Text(
            text = "Password Requirements",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        
        VerticalSpacer(height = 8)
        
        Text(
            text = "• At least 8 characters long",
            style = MaterialTheme.typography.bodyMedium
        )
        
        Text(
            text = "• Contains at least one uppercase letter",
            style = MaterialTheme.typography.bodyMedium
        )
        
        Text(
            text = "• Contains at least one lowercase letter",
            style = MaterialTheme.typography.bodyMedium
        )
        
        Text(
            text = "• Contains at least one number",
            style = MaterialTheme.typography.bodyMedium
        )
        
        Text(
            text = "• Contains at least one special character",
            style = MaterialTheme.typography.bodyMedium
        )
    }
}
