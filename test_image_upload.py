#!/usr/bin/env python3
"""
Test script to verify image upload functionality is working
"""
import requests
import base64
import json

API_BASE_URL = "https://domeai-backend.onrender.com"

def test_image_upload():
    # Login first
    login_url = f"{API_BASE_URL}/api/v1/auth/login"
    login_data = {
        "username": "<EMAIL>",
        "password": "@#48CrVGt3"
    }
    
    print("Logging in...")
    response = requests.post(login_url, data=login_data)
    if response.status_code != 200:
        print(f"Login failed: {response.status_code}")
        return
    
    token = response.json()["access_token"]
    print("✅ Login successful")
    
    # Create a simple test image (1x1 pixel PNG)
    # This is a minimal valid PNG file in base64
    test_image_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    test_image_bytes = base64.b64decode(test_image_b64)
    
    # Test image upload endpoint
    upload_url = f"{API_BASE_URL}/api/v1/scans/upload-image/"
    headers = {"Authorization": f"Bearer {token}"}
    
    # Prepare multipart form data
    files = {
        'file': ('test_image.png', test_image_bytes, 'image/png')
    }
    data = {
        'user_provided_context': 'This is a test image upload with user context',
        'use_expert_scan': 'false'
    }
    
    print("\nTesting image upload...")
    print(f"URL: {upload_url}")
    print(f"User context: {data['user_provided_context']}")
    
    response = requests.post(upload_url, headers=headers, files=files, data=data)
    
    print(f"Upload status: {response.status_code}")
    print(f"Upload response: {response.text}")
    
    if response.status_code == 202:
        scan_data = response.json()
        scan_id = scan_data.get("id")
        user_context = scan_data.get("user_provided_context")
        
        print(f"✅ Image upload successful!")
        print(f"Scan ID: {scan_id}")
        print(f"User Context: {user_context}")
        
        # Check if user context was preserved
        if user_context == data['user_provided_context']:
            print("✅ User context preserved correctly!")
        else:
            print("❌ User context not preserved!")
            
        return scan_id
    else:
        print("❌ Image upload failed!")
        return None

def test_text_scan():
    # Login first
    login_url = f"{API_BASE_URL}/api/v1/auth/login"
    login_data = {
        "username": "<EMAIL>",
        "password": "@#48CrVGt3"
    }
    
    response = requests.post(login_url, data=login_data)
    token = response.json()["access_token"]
    
    # Test text scan endpoint
    text_url = f"{API_BASE_URL}/api/v1/scans/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "scan_in": {
            "input_text": "Test text scan after image fix",
            "input_content_type": "text"
        },
        "use_expert_scan": False
    }
    
    print("\nTesting text scan...")
    response = requests.post(text_url, json=payload, headers=headers)
    
    print(f"Text scan status: {response.status_code}")
    print(f"Text scan response: {response.text}")
    
    if response.status_code == 202:
        print("✅ Text scan still working!")
        return True
    else:
        print("❌ Text scan broken!")
        return False

if __name__ == "__main__":
    print("=== Testing Image Upload Fix ===")
    
    # Test image upload
    image_scan_id = test_image_upload()
    
    # Test text scan still works
    text_works = test_text_scan()
    
    print("\n=== Test Results ===")
    if image_scan_id and text_works:
        print("✅ Both image and text scans are working!")
        print("🎉 Horizons fix was successful!")
    elif image_scan_id:
        print("✅ Image upload fixed, but text scan broken")
    elif text_works:
        print("✅ Text scan works, but image upload still broken")
    else:
        print("❌ Both image and text scans are broken")
