# Google Play RTDN Webhook Fix

This document explains the issue with the Google Play RTDN webhook endpoint and provides a solution.

## The Issue

The webhook endpoint `/api/v1/webhooks/googleplay/rtdn` is not being registered correctly with the FastAPI application, resulting in a 404 Not Found error when trying to access it.

After examining the codebase, I found that:

1. The webhook endpoint is defined in `app/api/v1/endpoints/webhooks.py` as `/googleplay/rtdn`
2. The webhook router is included in the API router in `app/api/v1/api.py` with the prefix `/webhooks`
3. The API router is included in the main FastAPI app in `app/main.py` with the prefix `/api/v1`

This means the full path to the endpoint should be `/api/v1/webhooks/googleplay/rtdn`, but it's not being registered correctly.

## The Solution

There are two ways to fix this issue:

### Option 1: Fix the Existing Code

1. Make sure the webhook router is properly defined in `app/api/v1/endpoints/webhooks.py`:
   ```python
   router = APIRouter()
   
   @router.post("/googleplay/rtdn", status_code=status.HTTP_200_OK)
   async def google_play_rtdn(payload: PubSubMessageData, db: Session = Depends(get_db)) -> Dict[str, Any]:
       # ... existing code ...
   ```

2. Make sure the webhook router is properly included in the API router in `app/api/v1/api.py`:
   ```python
   api_router.include_router(webhooks.router, prefix="/webhooks", tags=["webhooks"])
   ```

3. Make sure the API router is properly included in the main FastAPI app in `app/main.py`:
   ```python
   app.include_router(api_router, prefix=settings.API_V1_STR)
   ```

4. Restart the API server:
   ```bash
   cd domeai_backend
   docker-compose down
   docker-compose up -d
   ```

### Option 2: Use the Standalone Test Server

I've created a standalone FastAPI app that implements the Google Play RTDN webhook endpoint correctly. You can use this to test the webhook functionality without needing the full backend environment.

1. Run the standalone server:
   ```bash
   python fix_webhook.py
   ```

2. In a separate terminal, test the webhook endpoint:
   ```bash
   python test_fix_webhook.py
   ```

3. Or use the batch file to do both:
   ```bash
   fix_webhook.bat
   ```

## Testing the Webhook

Once the webhook endpoint is working, you can test it with the following curl command:

```bash
curl -X POST http://localhost:8000/api/v1/webhooks/googleplay/rtdn \
  -H "Content-Type: application/json" \
  -d "{\"message\":{\"data\":\"eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJjb20uZG9tZWFpLnNjYW1kZXRlY3RvciIsImV2ZW50VGltZU1pbGxpcyI6IjE2MjEyMzQ1Njc4OTAiLCJ0ZXN0Tm90aWZpY2F0aW9uIjp7InZlcnNpb24iOiIxLjAifX0=\",\"messageId\":\"test-message-id\",\"publishTime\":\"2023-05-20T10:00:00.000Z\"},\"subscription\":\"projects/domeai-project/subscriptions/google-play-rtdn-subscription\"}"
```

Or with PowerShell:

```powershell
$payload = @{
    message = @{
        data = "eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJjb20uZG9tZWFpLnNjYW1kZXRlY3RvciIsImV2ZW50VGltZU1pbGxpcyI6IjE2MjEyMzQ1Njc4OTAiLCJ0ZXN0Tm90aWZpY2F0aW9uIjp7InZlcnNpb24iOiIxLjAifX0="
        messageId = "test-message-id"
        publishTime = "2023-05-20T10:00:00.000Z"
    }
    subscription = "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:8000/api/v1/webhooks/googleplay/rtdn" -Method POST -Body $payload -ContentType "application/json"
```

## Next Steps

Once you've verified that the webhook endpoint is working correctly, you can:

1. Apply the database migration to add the Google Play subscription fields:
   ```bash
   cd domeai_backend
   docker-compose exec api alembic upgrade head
   ```

2. Test the webhook endpoint with real Google Play RTDNs

3. Implement the actual Google Play API integration for verifying purchases

4. Update the user's subscription status based on the RTDNs
