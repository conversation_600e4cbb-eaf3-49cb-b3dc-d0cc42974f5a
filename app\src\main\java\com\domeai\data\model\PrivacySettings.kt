package com.domeai.data.model

/**
 * Data class representing privacy settings
 */
data class PrivacySettings(
    val userId: String,
    val dataSharingEnabled: Boolean = true,
    val analyticsCollectionEnabled: <PERSON>olean = true,
    val personalizationEnabled: <PERSON><PERSON>an = true,
    val biometricAuthEnabled: <PERSON>olean = false,
    val screenLockEnabled: <PERSON>olean = false,
    val twoFactorAuthEnabled: Boolean = false,
    val lastUpdated: Long = System.currentTimeMillis()
)
