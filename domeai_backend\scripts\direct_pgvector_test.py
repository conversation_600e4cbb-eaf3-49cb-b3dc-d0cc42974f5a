#!/usr/bin/env python
"""
Script to directly test pgvector similarity search.
"""

import psycopg2
import logging
import random
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Database connection parameters
DB_PARAMS = {
    "dbname": "dome_app_main_db",
    "user": "dome_app_user",
    "password": "A$ym5*ufttM2m#!zyGhwn",
    "host": "localhost",
    "port": "5432"
}

def test_inner_product():
    """Test inner product similarity search."""
    logger.info("Testing inner product similarity search")
    
    try:
        # Connect to the database
        conn = psycopg2.connect(**DB_PARAMS)
        cursor = conn.cursor()
        
        # Check if the pgvector extension is installed
        cursor.execute("SELECT * FROM pg_extension WHERE extname = 'vector'")
        extensions = cursor.fetchall()
        logger.info(f"Found {len(extensions)} extensions with name 'vector'")
        
        # Get a sample chunk
        cursor.execute("SELECT id, content, source FROM knowledge_base_chunks LIMIT 1")
        sample = cursor.fetchone()
        if sample:
            sample_id, sample_content, sample_source = sample
            logger.info(f"Sample chunk: ID={sample_id}, Source={sample_source}")
            logger.info(f"Content: {sample_content[:100]}...")
            
            # Get the embedding for this chunk
            cursor.execute("SELECT embedding FROM knowledge_base_chunks WHERE id = %s", (sample_id,))
            embedding_row = cursor.fetchone()
            if embedding_row and embedding_row[0]:
                embedding = embedding_row[0]
                logger.info(f"Embedding type: {type(embedding)}")
                logger.info(f"Embedding length: {len(embedding)}")
                
                # Use the embedding to find similar chunks with inner product
                cursor.execute("""
                    SELECT id, content, source, -1 * (embedding <#> %s) AS similarity_score
                    FROM knowledge_base_chunks
                    ORDER BY embedding <#> %s ASC
                    LIMIT 5
                """, (embedding, embedding))
                
                results = cursor.fetchall()
                logger.info(f"Found {len(results)} similar chunks using inner product")
                for i, (id, content, source, similarity) in enumerate(results):
                    logger.info(f"Similar chunk {i+1}: ID={id}, Source={source}, Similarity={similarity:.4f}")
                    logger.info(f"Content: {content[:100]}...")
                
                # Use the embedding to find similar chunks with cosine distance
                cursor.execute("""
                    SELECT id, content, source, 1 - (embedding <-> %s)/2 AS similarity_score
                    FROM knowledge_base_chunks
                    ORDER BY embedding <-> %s
                    LIMIT 5
                """, (embedding, embedding))
                
                results = cursor.fetchall()
                logger.info(f"Found {len(results)} similar chunks using cosine distance")
                for i, (id, content, source, similarity) in enumerate(results):
                    logger.info(f"Similar chunk {i+1}: ID={id}, Source={source}, Similarity={similarity:.4f}")
                    logger.info(f"Content: {content[:100]}...")
                
                # Test with a random embedding
                random_embedding = [random.uniform(-1, 1) for _ in range(1536)]
                # Normalize the random embedding
                norm = np.linalg.norm(random_embedding)
                normalized_embedding = [x / norm for x in random_embedding]
                
                cursor.execute("""
                    SELECT id, content, source, -1 * (embedding <#> %s) AS similarity_score
                    FROM knowledge_base_chunks
                    ORDER BY embedding <#> %s ASC
                    LIMIT 3
                """, (normalized_embedding, normalized_embedding))
                
                results = cursor.fetchall()
                logger.info(f"Found {len(results)} similar chunks using random embedding")
                for i, (id, content, source, similarity) in enumerate(results):
                    logger.info(f"Similar chunk {i+1}: ID={id}, Source={source}, Similarity={similarity:.4f}")
                    logger.info(f"Content: {content[:100]}...")
            else:
                logger.error("No embedding found for sample chunk")
        else:
            logger.error("No chunks found in the database")
        
        # Close the connection
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Error in test_inner_product: {str(e)}")

if __name__ == "__main__":
    test_inner_product()
