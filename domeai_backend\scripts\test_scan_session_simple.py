#!/usr/bin/env python
"""
Simple script to test the scan session functionality.
"""

import logging
import sys
import uuid
import os
import time
from datetime import datetime, timezone

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan_session import ScanSession
from app.models.scan import Scan
from app.crud import crud_scan_session

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# User context for the first scan
USER_CONTEXT = """They almost got me. My intuition is almost on point. I was so close too. Holy Shit. My intuition is "almost" on point. I finally got one. So I'm on a "dating" app. I matched with a girl in my area. She seemed a little too good to be true. So I of course proceeded with caution."""

def test_scan_session_simple():
    """Test the scan session functionality with a simple approach."""
    logger.info("Testing Scan Session Functionality (Simple)")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create a premium tier test user
        unique_email = f"test_premium_user_{int(time.time())}@example.com"
        test_user = User(
            email=unique_email,
            hashed_password="hashed_password",
            subscription_tier="premium",
            monthly_scan_allowance=100,
            scans_this_month=0,
            scan_counter_reset_at=datetime.now(timezone.utc)
        )
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        logger.info(f"Created premium test user with ID {test_user.id}")
        
        # Create a scan session
        session = crud_scan_session.create_scan_session(
            db=db, owner_id=test_user.id, title="Simple Test Session"
        )
        logger.info(f"Created scan session with ID {session.id}")
        
        # Track scan credits before
        scans_before = test_user.scans_this_month
        logger.info(f"Scans before: {scans_before}")
        
        # Create first scan in the session
        logger.info("Creating first scan in session")
        scan1 = Scan(
            owner_id=test_user.id,
            status="completed",
            input_content_type="text",
            input_text="This is test scan 1",
            user_provided_context=USER_CONTEXT,
            scan_session_id=session.id,
            analysis_result={
                "risk_score": 0.85,
                "detected_red_flags": ["Red flag 1", "Red flag 2"],
                "explanation": "This is a test explanation",
                "recommendations": "These are test recommendations",
                "confidence_level": "High"
            }
        )
        db.add(scan1)
        db.commit()
        db.refresh(scan1)
        logger.info(f"Created scan 1 with ID {scan1.id}")
        
        # Update user scan count
        test_user.scans_this_month += 1
        db.commit()
        db.refresh(test_user)
        logger.info(f"Scans after first scan: {test_user.scans_this_month}")
        
        # Create second scan in the session
        logger.info("Creating second scan in session")
        scan2 = Scan(
            owner_id=test_user.id,
            status="completed",
            input_content_type="text",
            input_text="This is test scan 2",
            scan_session_id=session.id,
            analysis_result={
                "risk_score": 0.9,
                "detected_red_flags": ["Red flag 3", "Red flag 4"],
                "explanation": "This is another test explanation",
                "recommendations": "These are more test recommendations",
                "confidence_level": "High"
            }
        )
        db.add(scan2)
        db.commit()
        db.refresh(scan2)
        logger.info(f"Created scan 2 with ID {scan2.id}")
        
        # Check if scan credit was consumed for the second scan
        db.refresh(test_user)
        logger.info(f"Scans after second scan: {test_user.scans_this_month}")
        logger.info(f"Scan credit consumed for second scan: {test_user.scans_this_month - (scans_before + 1)}")
        
        # Get previous scans in the session
        previous_scans = crud_scan_session.get_previous_scans_in_session(
            db=db, session_id=session.id, current_scan_id=scan2.id
        )
        logger.info(f"Found {len(previous_scans)} previous scans in session {session.id}")
        for prev_scan in previous_scans:
            logger.info(f"Previous scan ID: {prev_scan.id}, Text: {prev_scan.input_text}")
        
        # Retrieve the final session details
        final_session = crud_scan_session.get_scan_session(
            db=db, session_id=session.id, owner_id=test_user.id
        )
        logger.info(f"Final session details:")
        logger.info(f"Session ID: {final_session.id}")
        logger.info(f"Session title: {final_session.title}")
        logger.info(f"Session created at: {final_session.created_at}")
        logger.info(f"Session last activity at: {final_session.last_activity_at}")
        
        # Verify all scans are associated with this session
        session_scans = db.query(Scan).filter(Scan.scan_session_id == session.id).all()
        logger.info(f"Number of scans in session: {len(session_scans)}")
        logger.info(f"Scan IDs in session: {[scan.id for scan in session_scans]}")
        
        # Clean up
        for scan in session_scans:
            db.delete(scan)
        db.delete(final_session)
        db.delete(test_user)
        db.commit()
        logger.info("Test data deleted")
        
    finally:
        db.close()

def main():
    """Run the test."""
    test_scan_session_simple()

if __name__ == "__main__":
    main()
