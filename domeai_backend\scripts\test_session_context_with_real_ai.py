#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test session context accumulation with real AI analysis.

This script submits the 4-Image Reddit Conversational Scam Scenario to a single scan session
and reports the accumulated session context, KB chunks retrieved, and final ScanResultData
for each scan.
"""

import asyncio
import logging
import sys
import os
import uuid
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan import Scan
from app.models.scan_session import ScanSession
from app.crud import crud_user, crud_scan, crud_scan_session
from app.services.ai_service_factory import get_ai_service
from app.tasks.scan_tasks import _process_scan_with_ai

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Reddit Conversational Scam Scenario image paths
REDDIT_IMAGES = [
    "data/test_images/reddit_scam_scenario/image1.jpg",
    "data/test_images/reddit_scam_scenario/image2.jpg",
    "data/test_images/reddit_scam_scenario/image3.jpg",
    "data/test_images/reddit_scam_scenario/image4.jpg"
]

async def create_test_user(db: Session, email: str, tier: str = "premium") -> User:
    """Create a test user with the specified subscription tier."""
    # Check if user already exists
    user = crud_user.get_user_by_email(db, email=email)
    if user:
        logger.info(f"User {email} already exists with ID {user.id}")
        # Update subscription tier
        user.subscription_tier = tier
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    # Create new user
    from app.schemas.user import UserCreate

    user_in = UserCreate(
        email=email,
        password="testpassword",
        full_name="Test User"
    )

    user = crud_user.create_user(db=db, obj_in=user_in)

    # Update user properties
    user.subscription_tier = tier
    user.scans_this_month = 0
    user.scan_allowance = 50 if tier == "premium" else 100
    user.expert_scans_this_month = 0
    user.expert_scan_allowance = 0 if tier == "free" else (10 if tier == "premium" else 20)

    db.add(user)
    db.commit()
    db.refresh(user)
    logger.info(f"Created user {email} with ID {user.id} and tier {tier}")
    return user

async def create_scan_in_session(
    db: Session,
    user: User,
    session_id: Optional[uuid.UUID],
    image_path: str,
    user_provided_context: Optional[str] = None
) -> Scan:
    """Create a scan in the specified session."""
    # Create a new session if none provided
    if not session_id:
        session = crud_scan_session.create_scan_session(db=db, owner_id=user.id)
        session_id = session.id
        logger.info(f"Created new session {session_id}")

    # Create scan
    scan = Scan(
        owner_id=user.id,
        status="pending",
        input_content_type="image_path",
        user_provided_context=user_provided_context,
        scan_session_id=session_id,
        raw_input_payload={"file_path": image_path, "is_expert_scan": False}
    )

    db.add(scan)
    db.commit()
    db.refresh(scan)
    logger.info(f"Created scan {scan.id} in session {session_id}")

    return scan

async def process_scan_with_real_ai(
    db: Session,
    scan: Scan,
    user: User,
    previous_scans: List[Scan] = None
) -> Dict[str, Any]:
    """
    Process a scan with the real AI service.

    Args:
        db: Database session
        scan: The scan to process
        user: The user who owns the scan
        previous_scans: List of previous scans in the session

    Returns:
        The analysis result
    """
    logger.info(f"Processing scan {scan.id} with real AI service")

    # Update scan status to "processing"
    scan.status = "processing"
    db.add(scan)
    db.commit()
    db.refresh(scan)

    # Get the AI service based on user's tier
    ai_service = get_ai_service(user_tier=user.subscription_tier)

    # Compile accumulated session context
    accumulated_session_context = None
    if previous_scans:
        context_parts = []

        for i, prev_scan in enumerate(previous_scans):
            # Extract text from previous scan
            prev_text = None
            if prev_scan.analysis_result and "extracted_text" in prev_scan.analysis_result:
                prev_text = prev_scan.analysis_result["extracted_text"]
            elif prev_scan.input_text:
                prev_text = prev_scan.input_text

            if prev_text:
                # Add scan number and text to context
                context_parts.append(f"[Scan {i+1}]: {prev_text}")

                # Add platform information if available
                if prev_scan.analysis_result and "platform_identified" in prev_scan.analysis_result:
                    platform = prev_scan.analysis_result["platform_identified"]
                    if platform:
                        context_parts[-1] += f" (Platform: {platform})"

        # Join all parts into a single context string
        if context_parts:
            accumulated_session_context = "\n---\n".join(context_parts)
            logger.info(f"Compiled accumulated session context ({len(context_parts)} previous scans)")
            logger.info(f"Accumulated session context: {accumulated_session_context[:200]}...")

    # Process the scan with the AI service
    try:
        # Call the _process_scan_with_ai function directly
        analysis_result = await _process_scan_with_ai(scan, ai_service, accumulated_session_context)

        # Update scan with result
        scan.analysis_result = analysis_result.__dict__
        scan.status = "completed"
        db.add(scan)
        db.commit()
        db.refresh(scan)

        logger.info(f"Processed scan {scan.id}")
        logger.info(f"Risk score: {analysis_result.risk_score}")
        logger.info(f"Detected red flags: {analysis_result.detected_red_flags}")

        if analysis_result.overall_session_assessment:
            logger.info(f"Overall session assessment: {analysis_result.overall_session_assessment}")

        return scan.analysis_result

    except Exception as e:
        logger.error(f"Error processing scan {scan.id}: {str(e)}")
        scan.status = "failed"
        scan.error_message = str(e)
        db.add(scan)
        db.commit()
        db.refresh(scan)
        return {"error": str(e)}

async def test_session_context_with_real_ai():
    """Test session context accumulation with real AI analysis."""
    logger.info("Testing session context accumulation with real AI analysis")

    # Create a database session
    db = SessionLocal()
    try:
        # Create a test user with premium tier
        user = await create_test_user(db, email="<EMAIL>", tier="premium")

        # Create a new session
        session = crud_scan_session.create_scan_session(db=db, owner_id=user.id)
        session_id = session.id
        logger.info(f"Created session {session_id}")

        # Process each image in the Reddit Conversational Scam Scenario
        previous_scans = []

        for i, image_path in enumerate(REDDIT_IMAGES):
            logger.info(f"\n{'='*80}\n")
            logger.info(f"Processing image {i+1}: {image_path}")

            # Create and process scan
            scan = await create_scan_in_session(
                db,
                user,
                session_id,
                image_path,
                user_provided_context=f"Reddit Scam Scenario Image {i+1}"
            )

            # Process the scan with real AI
            result = await process_scan_with_real_ai(db, scan, user, previous_scans)

            # Add scan to previous scans for next iteration
            previous_scans.append(scan)

            # Log the results
            logger.info(f"\nScan {i+1} Results:")
            logger.info(f"Scan ID: {scan.id}")
            logger.info(f"Session ID: {session_id}")

            # Log risk score and red flags
            if "risk_score" in result:
                logger.info(f"Risk Score: {result['risk_score']}")

            if "detected_red_flags" in result:
                logger.info(f"Red Flags: {result['detected_red_flags']}")

            # Log explanation (truncated)
            if "explanation" in result:
                explanation = result["explanation"]
                logger.info(f"Explanation: {explanation[:200]}...")

            # Log recommendations (truncated)
            if "recommendations" in result:
                recommendations = result["recommendations"]
                logger.info(f"Recommendations: {recommendations[:200]}...")

            # Log overall session assessment (truncated)
            if "overall_session_assessment" in result:
                assessment = result["overall_session_assessment"]
                if assessment:
                    logger.info(f"Overall Session Assessment: {assessment[:200]}...")

            # Log confidence level
            if "confidence_level" in result:
                logger.info(f"Confidence Level: {result['confidence_level']}")

            # Save detailed results to a JSON file
            output_dir = "output"
            os.makedirs(output_dir, exist_ok=True)

            with open(f"{output_dir}/scan_{scan.id}_result.json", "w") as f:
                json.dump(result, f, indent=2)

            logger.info(f"Saved detailed results to {output_dir}/scan_{scan.id}_result.json")

        # Final summary
        logger.info(f"\n{'='*80}\n")
        logger.info(f"Completed processing {len(REDDIT_IMAGES)} images in session {session_id}")
        logger.info(f"User: {user.email} (ID: {user.id})")
        logger.info(f"Scans: {[scan.id for scan in previous_scans]}")

    finally:
        db.close()

async def main():
    """Run the script."""
    await test_session_context_with_real_ai()

if __name__ == "__main__":
    asyncio.run(main())
