#!/usr/bin/env python
"""
Script to test knowledge base retrieval.
"""

import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy import text
from app.core.database import SessionLocal

def test_kb_retrieval():
    """Test knowledge base retrieval."""
    print("Testing knowledge base retrieval")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create a test vector
        test_vector = "[" + ",".join(["0.1"] * 1536) + "]"
        
        # Use raw SQL with the pgvector operator
        query = text("""
            SELECT id, content, source,
                   1 - (embedding <-> :embedding) AS similarity_score
            FROM knowledge_base_chunks
            ORDER BY embedding <-> :embedding
            LIMIT 3
        """)
        
        # Execute the query with parameters
        result = db.execute(query, {"embedding": test_vector})
        
        # Process the results
        rows = result.fetchall()
        print(f"Found {len(rows)} similar rows")
        for row in rows:
            print(f"Similar row: ID={row.id}, Source={row.source}, Similarity={row.similarity_score}")
            print(f"Content: {row.content[:100]}...")
        
        # Try a query with a specific keyword
        print("\nTesting query with 'cashier check' keyword")
        query = text("""
            SELECT id, content, source
            FROM knowledge_base_chunks
            WHERE source LIKE '%cashier%'
            LIMIT 3
        """)
        
        # Execute the query
        result = db.execute(query)
        
        # Process the results
        rows = result.fetchall()
        print(f"Found {len(rows)} rows with 'cashier' in source")
        for row in rows:
            print(f"Row: ID={row.id}, Source={row.source}")
            print(f"Content: {row.content[:100]}...")
            
            # Get the embedding for this row
            query = text("""
                SELECT embedding
                FROM knowledge_base_chunks
                WHERE id = :id
            """)
            
            # Execute the query
            embedding_result = db.execute(query, {"id": row.id})
            embedding_row = embedding_result.fetchone()
            
            if embedding_row and embedding_row.embedding:
                print(f"Embedding type: {type(embedding_row.embedding)}")
                print(f"Embedding length: {len(embedding_row.embedding)}")
                print(f"First 5 values: {embedding_row.embedding[:5]}")
            else:
                print("No embedding found")
        
    finally:
        db.close()

if __name__ == "__main__":
    test_kb_retrieval()
