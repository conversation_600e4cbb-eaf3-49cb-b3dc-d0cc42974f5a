package com.domeai.data.model

/**
 * Sealed class representing subscription plan information
 */
sealed class SubscriptionPlanInfo(
    val id: String,
    val title: String,
    val priceMonthly: String,
    val priceAnnually: String,
    val annualBillingDetails: String, // e.g., "$XX.XX/year (Save YY%)" or "Billed as $X.XX annually"
    val features: List<String>,
    val scanAllowance: String, // e.g., "10 Quick Scans/month", "100 Advanced Scans/month + Session Handling"
    val primaryAiModelInfo: String // e.g., "Powered by GPT-4.1 mini", "Powered by GPT-4.1"
) {
    object Basic : SubscriptionPlanInfo(
        id = "domeai_basic_free",
        title = "DomeAI Basic",
        priceMonthly = "$0",
        priceAnnually = "$0", // Or "N/A" if no annual concept for free
        annualBillingDetails = "Always Free",
        features = listOf(
            "Basic Scam Detection",
            "Standard Support",
            "Limited Overlay Alerts",
            "7-Day Scan History"
        ),
        scanAllowance = "10 Quick Scans/month",
        primaryAiModelInfo = "Uses GPT-4.1 mini"
    )

    object Premium : SubscriptionPlanInfo(
        id = "domeai_premium",
        title = "DomeAI Premium",
        priceMonthly = "$9.99/month",
        priceAnnually = "$99.90/year",
        annualBillingDetails = "Billed annually (approx. $8.32/month)",
        features = listOf(
            "Advanced Scam Detection",
            "Detailed Scan Reports",
            "Priority Email Support",
            "Real-Time Overlay Alerts",
            "Full Scan History",
            "Early Beta Access"
        ),
        scanAllowance = "100 Advanced Scans/month (with Scan Session handling)",
        primaryAiModelInfo = "Powered by GPT-4.1"
    )

    object Expert : SubscriptionPlanInfo(
        id = "domeai_expert",
        title = "DomeAI Expert",
        priceMonthly = "$24.99/month",
        priceAnnually = "$249.90/year",
        annualBillingDetails = "Billed annually (approx. $20.82/month)",
        features = listOf(
            "All Premium Features",
            "Expert Analysis Mode (for select scans)",
            "Most Detailed Forensic Reports",
            "Highest Priority Support",
            "Exclusive Content (e.g., quarterly scam trends)"
        ),
        scanAllowance = "100 Advanced Scans/month (GPT-4.1) + 20 Expert Scans/month (o3) (both with Scan Session handling)",
        primaryAiModelInfo = "GPT-4.1 & Access to OpenAI o3 Model"
    )
}

/**
 * List of all subscription plans for easy iteration in UI
 */
val allSubscriptionPlans = listOf(
    SubscriptionPlanInfo.Basic,
    SubscriptionPlanInfo.Premium,
    SubscriptionPlanInfo.Expert
)
