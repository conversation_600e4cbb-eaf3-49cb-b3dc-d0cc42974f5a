#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create a test session with multiple scans.
"""

import asyncio
import logging
import sys
import os
import uuid
from typing import List, Dict, Any, Optional

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan import Scan
from app.models.scan_session import ScanSession
from app.crud import crud_user, crud_scan, crud_scan_session
from app.services.ai_service_factory import get_ai_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Test data
TEST_MESSAGES = [
    "Hi there, I'm interested in your product. Can you tell me more about it?",
    "I'd like to buy it, but I'm currently traveling. Can I pay with a cashier's check?",
    "Great! I'll send a check for $2,000 even though the item is only $800. My assistant made a mistake. Can you wire me back the difference?",
    "Please send the money via Western Union to my cousin in Nigeria. It's urgent!"
]

async def create_test_user(db: Session, email: str, tier: str = "premium") -> User:
    """Create a test user with the specified subscription tier."""
    # Check if user already exists
    user = crud_user.get_user_by_email(db, email=email)
    if user:
        logger.info(f"User {email} already exists with ID {user.id}")
        # Update subscription tier
        user.subscription_tier = tier
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    # Create new user
    user_in = {
        "email": email,
        "password": "testpassword",
        "full_name": "Test User",
        "subscription_tier": tier,
        "scans_this_month": 0,
        "scan_allowance": 50 if tier == "premium" else 100,
        "expert_scans_this_month": 0,
        "expert_scan_allowance": 0 if tier == "free" else (10 if tier == "premium" else 20)
    }
    user = crud_user.create_user(db, obj_in=user_in)
    logger.info(f"Created user {email} with ID {user.id} and tier {tier}")
    return user

async def create_scan_in_session(
    db: Session, 
    user: User, 
    session_id: uuid.UUID, 
    message: str
) -> Scan:
    """Create a scan in the specified session."""
    # Create scan
    scan = Scan(
        owner_id=user.id,
        status="completed",
        input_content_type="text",
        input_text=message,
        scan_session_id=session_id,
        raw_input_payload={"is_expert_scan": False}
    )
    
    db.add(scan)
    db.commit()
    db.refresh(scan)
    logger.info(f"Created scan {scan.id} in session {session_id}")
    
    return scan

async def create_test_session_with_scans():
    """Create a test session with multiple scans."""
    logger.info("Creating test session with multiple scans")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create a test user with premium tier
        user = await create_test_user(db, email="<EMAIL>", tier="premium")
        
        # Create a new session
        session = crud_scan_session.create_scan_session(db=db, owner_id=user.id)
        session_id = session.id
        logger.info(f"Created session {session_id}")
        
        # Create scans in the session
        scans = []
        for i, message in enumerate(TEST_MESSAGES):
            logger.info(f"Creating scan {i+1} with message: {message}")
            scan = await create_scan_in_session(db, user, session_id, message)
            
            # Add mock analysis result
            risk_score = 0.2 + (i * 0.2)  # Increasing risk score with each message
            analysis_result = {
                "extracted_text": message,
                "platform_identified": "Test Platform",
                "risk_score": risk_score,
                "detected_red_flags": [
                    f"Red flag {i+1}.1",
                    f"Red flag {i+1}.2"
                ],
                "explanation": f"This is a test explanation for message {i+1}. The risk score is {risk_score}.",
                "recommendations": f"Recommendations for message {i+1}.",
                "confidence_level": "High",
                "model_used": "Test Model",
                "overall_session_assessment": f"Overall assessment for session after message {i+1}. The conversation is becoming {'more' if i > 0 else 'potentially'} suspicious."
            }
            
            scan.analysis_result = analysis_result
            db.add(scan)
            db.commit()
            db.refresh(scan)
            
            scans.append(scan)
            logger.info(f"Added analysis result to scan {scan.id}")
        
        logger.info(f"Created {len(scans)} scans in session {session_id}")
        
        # Return the session ID for reference
        return session_id
    
    finally:
        db.close()

async def main():
    """Run the script."""
    session_id = await create_test_session_with_scans()
    logger.info(f"Test session created with ID: {session_id}")
    logger.info("You can now run test_session_context_simple.py to verify the session context")

if __name__ == "__main__":
    asyncio.run(main())
