package com.domeai.ui.composables

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.domeai.R

/**
 * Displays the DomeAI logo
 */
@Composable
fun DomeAILogo(
    modifier: Modifier = Modifier,
    size: Int = 100
) {
    // Use the appropriate drawable based on device density
    val context = LocalContext.current
    val density = context.resources.displayMetrics.density

    val logoResId = when {
        density >= 3.0 -> R.drawable.logo_no_bg_xxhdpi
        density >= 2.0 -> R.drawable.logo_no_bg_xhdpi
        density >= 1.5 -> R.drawable.logo_no_bg_hdpi
        else -> R.drawable.logo_no_bg_mdpi
    }

    Image(
        painter = painterResource(id = logoResId),
        contentDescription = "DomeAI Logo",
        modifier = modifier.size(size.dp)
    )
}

/**
 * Displays the DomeAI logo centered in a container
 */
@Composable
fun CenteredDomeAILogo(
    modifier: Modifier = Modifier,
    size: Int = 100
) {
    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.Center
    ) {
        DomeAILogo(size = size)
    }
}

/**
 * Displays the DomeAI logo with padding
 */
@Composable
fun PaddedDomeAILogo(
    modifier: Modifier = Modifier,
    size: Int = 100,
    paddingDp: Int = 16
) {
    Box(
        modifier = modifier.padding(paddingDp.dp),
        contentAlignment = Alignment.Center
    ) {
        DomeAILogo(size = size)
    }
}
