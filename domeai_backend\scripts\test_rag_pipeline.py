#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test the RAG pipeline with specific scenarios.
"""

import asyncio
import json
import logging
import sys
from typing import List, Dict, Any, Optional

# Add the parent directory to the path so we can import from app
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.database import SessionLocal
from app.services.ai_services import OpenAIModelService
from app.core.config import settings
from app.crud import crud_kb
from app.schemas.scan import ScanResultData
from enum import Enum, auto

class ContentType(Enum):
    TEXT = "text"
    IMAGE = "image"
    URL = "url"
# Define our own perform_scam_analysis_with_rag function for testing
async def perform_scam_analysis_with_rag(
    content_type,
    text_content,
    image_content,
    source_app,
    source_url,
    text_embedding,
    similar_chunks,
    ai_service
):
    """
    Perform scam analysis with RAG.

    This is a simplified version of the function for testing purposes.
    """
    # Extract content from similar chunks
    knowledge_context = "\n\n".join([
        f"Source: {chunk.source}\n{chunk.content}"
        for chunk in similar_chunks
    ])

    # Construct a prompt for the AI service
    prompt = f"""
    You are an expert scam detector AI. Analyze the following content for potential scam indicators.

    CONTENT TO ANALYZE:
    {text_content}

    SOURCE APP: {source_app}
    SOURCE URL: {source_url}

    RELEVANT KNOWLEDGE BASE INFORMATION:
    {knowledge_context}

    Based on the content and the knowledge base information, provide a comprehensive scam analysis with the following:

    1. RISK SCORE: Provide a risk score between 0.0 and 1.0, where 0.0 is definitely safe and 1.0 is definitely a scam.
    2. EXPLANATION: Explain why you think this is or isn't a scam.
    3. RED FLAGS: List specific red flags or suspicious elements in the content.
    4. RECOMMENDATIONS: Provide recommendations for the user.
    5. CONFIDENCE: Indicate your confidence level in this analysis (Low, Medium, High).

    Format your response as follows:

    RISK SCORE: [score]

    EXPLANATION:
    [your explanation]

    RED FLAGS:
    - [red flag 1]
    - [red flag 2]
    - ...

    RECOMMENDATIONS:
    [your recommendations]

    CONFIDENCE: [confidence level]
    """

    # Call the AI service to analyze the content
    response = await ai_service.client.chat.completions.create(
        model="gpt-4.1",
        messages=[
            {"role": "system", "content": "You are an expert scam detector AI."},
            {"role": "user", "content": prompt}
        ],
        temperature=0.3
    )

    # Extract the response text
    response_text = response.choices[0].message.content

    # Parse the response
    risk_score = 0.0
    explanation = ""
    red_flags = []
    recommendations = ""
    confidence = ""

    # Simple parsing based on expected response structure
    lines = response_text.split('\n')
    in_risk_section = False
    in_explanation_section = False
    in_red_flags_section = False
    in_recommendations_section = False
    in_confidence_section = False

    for line in lines:
        line = line.strip()

        if not line:  # Skip empty lines
            continue

        if line.startswith("RISK SCORE:"):
            in_risk_section = True
            in_explanation_section = False
            in_red_flags_section = False
            in_recommendations_section = False
            in_confidence_section = False

            # Extract risk score
            try:
                risk_score = float(line.split("RISK SCORE:")[1].strip())
            except:
                risk_score = 0.5  # Default if parsing fails

        elif line.startswith("EXPLANATION:"):
            in_risk_section = False
            in_explanation_section = True
            in_red_flags_section = False
            in_recommendations_section = False
            in_confidence_section = False

        elif line.startswith("RED FLAGS:"):
            in_risk_section = False
            in_explanation_section = False
            in_red_flags_section = True
            in_recommendations_section = False
            in_confidence_section = False

        elif line.startswith("RECOMMENDATIONS:"):
            in_risk_section = False
            in_explanation_section = False
            in_red_flags_section = False
            in_recommendations_section = True
            in_confidence_section = False

        elif line.startswith("CONFIDENCE:"):
            in_risk_section = False
            in_explanation_section = False
            in_red_flags_section = False
            in_recommendations_section = False
            in_confidence_section = True

            # Extract confidence
            confidence = line.split("CONFIDENCE:")[1].strip()

        elif in_explanation_section:
            explanation += line + " "

        elif in_red_flags_section and line.startswith("-"):
            red_flags.append(line[1:].strip())

        elif in_recommendations_section:
            recommendations += line + " "

    # Create a ScanResultData object
    from app.schemas.scan import ScanResultData

    result = ScanResultData(
        risk_score=risk_score,
        explanation=explanation.strip(),
        detected_red_flags=red_flags,
        recommendations=recommendations.strip()
    )

    # Add confidence as an attribute
    setattr(result, 'confidence', confidence)

    return result

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

async def test_cashier_check_scam():
    """Test the RAG pipeline with a cashier's check scam scenario."""
    db = SessionLocal()
    try:
        # Initialize the OpenAI service
        ai_service = OpenAIModelService(api_key=settings.OPENAI_API_KEY)

        # Cashier's check scam text
        text_content = """
        Hey there! I'm interested in your item on OfferUp. I'd like to buy it right away. I'll send you a cashier's check for $1,500. That's $500 more than your asking price. Once you deposit it, just send me back the extra $500 via Western Union. I'm out of town for work, so I can't pick it up in person. I'll arrange shipping once you confirm. Let me know if this works for you!
        """

        # Source metadata
        source_app = "OfferUp"
        source_url = "https://offerup.com/item/detail/123456"

        # Generate text embedding
        logger.info("Generating text embedding...")
        text_embedding = await ai_service.get_text_embedding(text=text_content)

        # Find similar KB chunks
        logger.info("Finding similar KB chunks...")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=text_embedding,
            top_k=3
        )

        # Log the similar chunks
        logger.info(f"Found {len(similar_chunks)} similar chunks:")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Chunk {i+1}: {chunk.source}")
            logger.info(f"Similarity score: {getattr(chunk, 'similarity_score', 'N/A')}")
            logger.info(f"Content: {chunk.content[:150]}...")

        # Perform scam analysis with RAG
        logger.info("Performing scam analysis with RAG...")
        result = await perform_scam_analysis_with_rag(
            content_type=ContentType.TEXT,
            text_content=text_content,
            image_content=None,
            source_app=source_app,
            source_url=source_url,
            text_embedding=text_embedding,
            similar_chunks=similar_chunks,
            ai_service=ai_service
        )

        # Log the result
        logger.info("Scam analysis result:")
        logger.info(f"Risk score: {result.risk_score}")
        logger.info(f"Explanation: {result.explanation}")
        logger.info(f"Red flags: {result.detected_red_flags}")
        logger.info(f"Recommendations: {result.recommendations}")
        logger.info(f"Confidence: {getattr(result, 'confidence', 'N/A')}")

        return {
            "similar_chunks": [
                {
                    "source": chunk.source,
                    "similarity_score": getattr(chunk, "similarity_score", None),
                    "content": chunk.content[:150] + "..."
                }
                for chunk in similar_chunks
            ],
            "result": {
                "risk_score": result.risk_score,
                "explanation": result.explanation,
                "red_flags": result.detected_red_flags,
                "recommendations": result.recommendations,
                "confidence": getattr(result, 'confidence', 'N/A')
            }
        }

    finally:
        db.close()

async def test_remote_buyer_scam():
    """Test the RAG pipeline with a remote buyer scam scenario."""
    db = SessionLocal()
    try:
        # Initialize the OpenAI service
        ai_service = OpenAIModelService(api_key=settings.OPENAI_API_KEY)

        # Remote buyer scam text
        text_content = """
        Hi, I saw your listing and I'm very interested in buying it. I'm currently out of the country for work but I want to purchase it right away. I'll pay extra for shipping and handling. I'll send payment via PayPal and arrange a shipping company to pick it up. Please take down the listing and I'll pay an extra $50 for your trouble. Let me know your PayPal email so I can send the money right away.
        """

        # Source metadata
        source_app = "Facebook Marketplace"
        source_url = "https://www.facebook.com/marketplace/item/123456"

        # Generate text embedding
        logger.info("Generating text embedding...")
        text_embedding = await ai_service.get_text_embedding(text=text_content)

        # Find similar KB chunks
        logger.info("Finding similar KB chunks...")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=text_embedding,
            top_k=3
        )

        # Log the similar chunks
        logger.info(f"Found {len(similar_chunks)} similar chunks:")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Chunk {i+1}: {chunk.source}")
            logger.info(f"Similarity score: {getattr(chunk, 'similarity_score', 'N/A')}")
            logger.info(f"Content: {chunk.content[:150]}...")

        # Perform scam analysis with RAG
        logger.info("Performing scam analysis with RAG...")
        result = await perform_scam_analysis_with_rag(
            content_type=ContentType.TEXT,
            text_content=text_content,
            image_content=None,
            source_app=source_app,
            source_url=source_url,
            text_embedding=text_embedding,
            similar_chunks=similar_chunks,
            ai_service=ai_service
        )

        # Log the result
        logger.info("Scam analysis result:")
        logger.info(f"Risk score: {result.risk_score}")
        logger.info(f"Explanation: {result.explanation}")
        logger.info(f"Red flags: {result.detected_red_flags}")
        logger.info(f"Recommendations: {result.recommendations}")
        logger.info(f"Confidence: {getattr(result, 'confidence', 'N/A')}")

        return {
            "similar_chunks": [
                {
                    "source": chunk.source,
                    "similarity_score": getattr(chunk, "similarity_score", None),
                    "content": chunk.content[:150] + "..."
                }
                for chunk in similar_chunks
            ],
            "result": {
                "risk_score": result.risk_score,
                "explanation": result.explanation,
                "red_flags": result.detected_red_flags,
                "recommendations": result.recommendations,
                "confidence": getattr(result, 'confidence', 'N/A')
            }
        }

    finally:
        db.close()

async def test_tech_support_scam():
    """Test the RAG pipeline with a tech support scam scenario."""
    db = SessionLocal()
    try:
        # Initialize the OpenAI service
        ai_service = OpenAIModelService(api_key=settings.OPENAI_API_KEY)

        # Tech support scam text
        text_content = """
        WARNING! Your computer is infected with 3 viruses! Call Microsoft Support Immediately at ************** to prevent data loss. Your personal information is at risk. If you close this window, your computer will be disabled. Our certified Microsoft technicians can remove the viruses remotely for a one-time fee of $299.99. Act now to protect your data!
        """

        # Source metadata
        source_app = "Web Browser"
        source_url = "https://fake-security-alert.com/warning"

        # Generate text embedding
        logger.info("Generating text embedding...")
        text_embedding = await ai_service.get_text_embedding(text=text_content)

        # Find similar KB chunks
        logger.info("Finding similar KB chunks...")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=text_embedding,
            top_k=3
        )

        # Log the similar chunks
        logger.info(f"Found {len(similar_chunks)} similar chunks:")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Chunk {i+1}: {chunk.source}")
            logger.info(f"Similarity score: {getattr(chunk, 'similarity_score', 'N/A')}")
            logger.info(f"Content: {chunk.content[:150]}...")

        # Perform scam analysis with RAG
        logger.info("Performing scam analysis with RAG...")
        result = await perform_scam_analysis_with_rag(
            content_type=ContentType.TEXT,
            text_content=text_content,
            image_content=None,
            source_app=source_app,
            source_url=source_url,
            text_embedding=text_embedding,
            similar_chunks=similar_chunks,
            ai_service=ai_service
        )

        # Log the result
        logger.info("Scam analysis result:")
        logger.info(f"Risk score: {result.risk_score}")
        logger.info(f"Explanation: {result.explanation}")
        logger.info(f"Red flags: {result.detected_red_flags}")
        logger.info(f"Recommendations: {result.recommendations}")
        logger.info(f"Confidence: {getattr(result, 'confidence', 'N/A')}")

        return {
            "similar_chunks": [
                {
                    "source": chunk.source,
                    "similarity_score": getattr(chunk, "similarity_score", None),
                    "content": chunk.content[:150] + "..."
                }
                for chunk in similar_chunks
            ],
            "result": {
                "risk_score": result.risk_score,
                "explanation": result.explanation,
                "red_flags": result.detected_red_flags,
                "recommendations": result.recommendations,
                "confidence": getattr(result, 'confidence', 'N/A')
            }
        }

    finally:
        db.close()

async def main():
    """Run all test scenarios and save the results to a file."""
    results = {}

    logger.info("Testing cashier's check scam scenario...")
    results["cashier_check_scam"] = await test_cashier_check_scam()

    logger.info("\nTesting remote buyer scam scenario...")
    results["remote_buyer_scam"] = await test_remote_buyer_scam()

    logger.info("\nTesting tech support scam scenario...")
    results["tech_support_scam"] = await test_tech_support_scam()

    # Save the results to a file
    with open("rag_test_results.json", "w") as f:
        json.dump(results, f, indent=2)

    logger.info("\nAll tests completed. Results saved to rag_test_results.json")

if __name__ == "__main__":
    asyncio.run(main())
