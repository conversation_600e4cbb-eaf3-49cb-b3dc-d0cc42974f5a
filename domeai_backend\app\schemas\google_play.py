"""
Pydantic schemas for Google Play Real-Time Developer Notifications (RTDNs).
"""
from pydantic import BaseModel, Field
from typing import Optional, Any, Dict, List


class GooglePlaySubscriptionNotification(BaseModel):
    """
    Schema for Google Play subscription notifications.
    
    These are sent when a subscription is purchased, renewed, canceled, etc.
    """
    version: str
    notificationType: int  # Integer representing the type of notification
    purchaseToken: str
    subscriptionId: str  # This is your SKU/Product ID in Google Play


class GooglePlayTestNotification(BaseModel):
    """
    Schema for Google Play test notifications.
    
    These are sent when testing the RTDN setup.
    """
    version: str


class GooglePlayOneTimeProductNotification(BaseModel):
    """
    Schema for Google Play one-time product notifications.
    
    These are sent for non-subscription purchases.
    """
    version: str
    notificationType: int
    purchaseToken: str
    sku: str  # Product ID


class GooglePlayVoidedPurchaseNotification(BaseModel):
    """
    Schema for Google Play voided purchase notifications.
    
    These are sent when a purchase is refunded or voided.
    """
    version: str
    notificationType: int
    purchaseToken: str
    orderId: str


class DeveloperNotification(BaseModel):
    """
    Schema for Google Play Developer Notifications.
    
    This is the main payload of the RTDN after decoding the base64 data.
    """
    version: str
    packageName: str
    eventTimeMillis: str
    subscriptionNotification: Optional[GooglePlaySubscriptionNotification] = None
    testNotification: Optional[GooglePlayTestNotification] = None
    oneTimeProductNotification: Optional[GooglePlayOneTimeProductNotification] = None
    voidedPurchaseNotification: Optional[GooglePlayVoidedPurchaseNotification] = None


class PubSubMessage(BaseModel):
    """
    Schema for Google Cloud Pub/Sub message.
    
    This is the message received from Pub/Sub.
    """
    data: str  # Base64 encoded DeveloperNotification
    messageId: str
    publishTime: str
    attributes: Optional[Dict[str, str]] = None


class PubSubMessageData(BaseModel):
    """
    Schema for Google Cloud Pub/Sub message data.
    
    This is the top-level payload received from Pub/Sub.
    """
    message: PubSubMessage
    subscription: str  # Name of the Pub/Sub subscription
