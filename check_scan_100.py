#!/usr/bin/env python3
"""
Check scan 100 results and scan submission format
"""
import requests
import json

API_BASE_URL = "https://domeai-backend.onrender.com"

def check_scan_100():
    # Login first
    login_url = f"{API_BASE_URL}/api/v1/auth/login"
    login_data = {
        "username": "<EMAIL>",
        "password": "@#48CrVGt3"
    }
    
    print("Logging in...")
    response = requests.post(login_url, data=login_data)
    if response.status_code != 200:
        print(f"Login failed: {response.status_code}")
        return
    
    token = response.json()["access_token"]
    print("✅ Login successful")
    
    # Check scan 100
    scan_url = f"{API_BASE_URL}/api/v1/scans/100"
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\nChecking scan 100...")
    response = requests.get(scan_url, headers=headers)
    
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        try:
            scan_data = response.json()
            print(f"✅ JSON Response received")
            print(f"Status: {scan_data.get('status')}")
            print(f"Scan ID: {scan_data.get('id')} (type: {type(scan_data.get('id'))})")
            print(f"User Context: {scan_data.get('user_provided_context', 'None')[:100]}...")
            
            analysis = scan_data.get('analysis_result')
            if analysis:
                print(f"\n📊 Analysis Result:")
                print(f"  Risk Score: {analysis.get('risk_score')}")
                print(f"  Confidence: {analysis.get('confidence_level')}")
                print(f"  Red Flags Count: {len(analysis.get('detected_red_flags', []))}")
                
            else:
                print("❌ No analysis result found")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            print(f"Raw response: {response.text[:500]}...")
    else:
        print(f"❌ Failed to get scan: {response.status_code}")
        print(f"Response: {response.text}")

def test_scan_submission_format():
    """Test what the scan submission response looks like"""
    login_url = f"{API_BASE_URL}/api/v1/auth/login"
    login_data = {
        "username": "<EMAIL>",
        "password": "@#48CrVGt3"
    }
    
    response = requests.post(login_url, data=login_data)
    token = response.json()["access_token"]
    
    # Test text scan submission to see response format
    text_url = f"{API_BASE_URL}/api/v1/scans/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "scan_in": {
            "input_text": "Test scan ID format check",
            "input_content_type": "text"
        },
        "use_expert_scan": False
    }
    
    print("\n=== Testing Scan Submission Response Format ===")
    response = requests.post(text_url, json=payload, headers=headers)
    
    print(f"Submission Status: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    
    if response.status_code == 202:
        try:
            data = response.json()
            print(f"✅ Scan submitted successfully!")
            print(f"Response keys: {list(data.keys())}")
            print(f"Scan ID: {data.get('id')} (type: {type(data.get('id'))})")
            print(f"Status: {data.get('status')}")
            print(f"Created At: {data.get('created_at')}")
            
            # Show the exact JSON structure
            print(f"\n📋 Full Response Structure:")
            print(json.dumps(data, indent=2, default=str))
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            print(f"Raw response: {response.text}")
    else:
        print(f"❌ Submission failed: {response.status_code}")
        print(f"Response: {response.text}")

if __name__ == "__main__":
    check_scan_100()
    test_scan_submission_format()
