#!/usr/bin/env python
"""
<PERSON>ript to test pgvector similarity search with the fixed implementation.
"""

import asyncio
import logging
import sys
import os
import time
from typing import List, Dict, Any, Optional

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.knowledge_base import KnowledgeBaseChunk
from app.crud import crud_kb
from app.services.ai_service_factory import get_ai_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

async def test_self_query():
    """Test querying with the embedding of a known KB chunk."""
    logger.info("Testing self-query with a known KB chunk")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Find a chunk with "cashier_check" in the source
        cashier_check_chunk = None
        all_chunks = db.query(KnowledgeBaseChunk).all()
        for chunk in all_chunks:
            if chunk.source and "cashier_check" in chunk.source:
                cashier_check_chunk = chunk
                break
        
        if not cashier_check_chunk:
            logger.error("No chunk found with 'cashier_check' in source")
            return
        
        logger.info(f"Found cashier check chunk: ID={cashier_check_chunk.id}, Source={cashier_check_chunk.source}")
        logger.info(f"Content: {cashier_check_chunk.content[:100]}...")
        
        # Get the embedding from the chunk
        embedding = cashier_check_chunk.embedding
        logger.info(f"Embedding dimensions: {len(embedding)}")
        
        # Use the embedding to find similar chunks
        logger.info("Finding similar chunks using the chunk's own embedding")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=embedding,
            top_k=5
        )
        
        logger.info(f"Found {len(similar_chunks)} similar chunks")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Similar chunk {i+1}: ID={chunk.id}, Source={chunk.source}")
            if hasattr(chunk, 'similarity_score'):
                logger.info(f"Similarity score: {chunk.similarity_score:.4f}")
            logger.info(f"Content: {chunk.content[:100]}...")
        
        # The first chunk should be the same as the query chunk with similarity close to 1.0
        if similar_chunks and similar_chunks[0].id == cashier_check_chunk.id:
            logger.info("Self-query test PASSED: First result is the query chunk itself")
            similarity = getattr(similar_chunks[0], 'similarity_score', 0)
            logger.info(f"Self-similarity score: {similarity:.4f}")
            if abs(similarity - 1.0) < 0.01:
                logger.info("Self-similarity score is close to 1.0 as expected")
            else:
                logger.warning(f"Self-similarity score is {similarity:.4f}, expected close to 1.0")
        else:
            logger.error("Self-query test FAILED: First result is not the query chunk itself")
    
    finally:
        db.close()

async def test_with_new_embedding():
    """Test with a new embedding from the AI service."""
    logger.info("Testing with a new embedding from the AI service")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create an AI service
        ai_service = get_ai_service(user_tier="premium")
        
        # Generate a new embedding for a test query
        test_query = "I received a cashier's check for more than the amount I was selling my item for. The buyer asked me to wire back the difference. Is this legitimate?"
        logger.info(f"Generating embedding for test query: {test_query}")
        
        embedding = await ai_service.get_text_embedding(text=test_query)
        logger.info(f"Generated embedding with {len(embedding)} dimensions")
        
        # Use the embedding to find similar chunks
        logger.info("Finding similar chunks using the new embedding")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=embedding,
            top_k=5
        )
        
        logger.info(f"Found {len(similar_chunks)} similar chunks")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Similar chunk {i+1}: ID={chunk.id}, Source={chunk.source}")
            if hasattr(chunk, 'similarity_score'):
                logger.info(f"Similarity score: {chunk.similarity_score:.4f}")
            logger.info(f"Content: {chunk.content[:100]}...")
        
        # Check if any of the top results contain "cashier_check" in the source
        cashier_check_found = False
        for chunk in similar_chunks:
            if chunk.source and "cashier_check" in chunk.source:
                cashier_check_found = True
                logger.info(f"Found relevant chunk with 'cashier_check' in source: ID={chunk.id}, Source={chunk.source}")
                logger.info(f"Similarity score: {getattr(chunk, 'similarity_score', 0):.4f}")
                break
        
        if cashier_check_found:
            logger.info("Test PASSED: Found relevant chunk for the query")
        else:
            logger.warning("Test WARNING: No chunk with 'cashier_check' in source found in top results")
    
    finally:
        db.close()

async def test_explain_analyze():
    """Test EXPLAIN ANALYZE to check if the index is being used."""
    logger.info("Testing EXPLAIN ANALYZE to check if the index is being used")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create an AI service
        ai_service = get_ai_service(user_tier="premium")
        
        # Generate a new embedding for a test query
        test_query = "I received a cashier's check for more than the amount I was selling my item for. The buyer asked me to wire back the difference. Is this legitimate?"
        logger.info(f"Generating embedding for test query: {test_query}")
        
        embedding = await ai_service.get_text_embedding(text=test_query)
        
        # Convert the embedding list to a string representation for SQL
        embedding_str = str(embedding)
        
        # Use raw SQL with EXPLAIN ANALYZE for inner product
        from sqlalchemy import text
        query = text(f"""
            EXPLAIN ANALYZE
            SELECT id, content, source, created_at,
                   -1 * (embedding <#> :embedding) AS similarity_score
            FROM knowledge_base_chunks
            ORDER BY embedding <#> :embedding ASC
            LIMIT 3
        """)
        
        # Execute the query with parameters
        result = db.execute(query, {"embedding": embedding_str})
        
        # Print the query plan
        logger.info("Query plan for inner product:")
        for row in result:
            logger.info(row[0])
        
        # Use raw SQL with EXPLAIN ANALYZE for cosine distance
        query = text(f"""
            EXPLAIN ANALYZE
            SELECT id, content, source, created_at,
                   1 - (embedding <-> :embedding)/2 AS similarity_score
            FROM knowledge_base_chunks
            ORDER BY embedding <-> :embedding
            LIMIT 3
        """)
        
        # Execute the query with parameters
        result = db.execute(query, {"embedding": embedding_str})
        
        # Print the query plan
        logger.info("Query plan for cosine distance:")
        for row in result:
            logger.info(row[0])
    
    finally:
        db.close()

async def main():
    """Run all tests."""
    logger.info("Starting pgvector similarity tests")
    
    await test_self_query()
    logger.info("\n" + "="*80 + "\n")
    
    await test_with_new_embedding()
    logger.info("\n" + "="*80 + "\n")
    
    await test_explain_analyze()
    logger.info("\n" + "="*80 + "\n")
    
    logger.info("All tests completed")

if __name__ == "__main__":
    asyncio.run(main())
