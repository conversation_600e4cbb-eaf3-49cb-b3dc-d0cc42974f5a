"""
Simple FastAPI app to test Google Play RTDN webhook.

This standalone script creates a simple FastAPI app that implements just the
Google Play RTDN webhook endpoint, allowing you to test the webhook functionality
without the full backend environment.

Run with: uvicorn simple_webhook_test:app --reload
Test with: curl -X POST http://localhost:8000/api/v1/webhooks/googleplay/rtdn -H "Content-Type: application/json" -d '{"message":{"data":"eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJjb20uZG9tZWFpLnNjYW1kZXRlY3RvciIsImV2ZW50VGltZU1pbGxpcyI6IjE2MjEyMzQ1Njc4OTAiLCJ0ZXN0Tm90aWZpY2F0aW9uIjp7InZlcnNpb24iOiIxLjAifX0=","messageId":"test-message-id","publishTime":"2023-05-20T10:00:00.000Z"},"subscription":"projects/domeai-project/subscriptions/google-play-rtdn-subscription"}'
"""
import base64
import json
import logging
from typing import Any, Dict, Optional

from fastapi import FastAPI, status
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="Google Play RTDN Webhook Test")

# Define Pydantic models for Google Play notifications
class GooglePlaySubscriptionNotification(BaseModel):
    """Schema for Google Play subscription notifications."""
    version: str
    notificationType: int
    purchaseToken: str
    subscriptionId: str


class GooglePlayTestNotification(BaseModel):
    """Schema for Google Play test notifications."""
    version: str


class GooglePlayOneTimeProductNotification(BaseModel):
    """Schema for Google Play one-time product notifications."""
    version: str
    notificationType: int
    purchaseToken: str
    sku: str


class GooglePlayVoidedPurchaseNotification(BaseModel):
    """Schema for Google Play voided purchase notifications."""
    version: str
    notificationType: int
    purchaseToken: str
    orderId: str


class DeveloperNotification(BaseModel):
    """Schema for Google Play developer notification."""
    version: str
    packageName: str
    eventTimeMillis: str
    subscriptionNotification: Optional[GooglePlaySubscriptionNotification] = None
    testNotification: Optional[GooglePlayTestNotification] = None
    oneTimeProductNotification: Optional[GooglePlayOneTimeProductNotification] = None
    voidedPurchaseNotification: Optional[GooglePlayVoidedPurchaseNotification] = None


class PubSubMessage(BaseModel):
    """Schema for Google Cloud Pub/Sub message."""
    data: str
    messageId: str
    publishTime: str
    attributes: Optional[Dict[str, str]] = None


class PubSubMessageData(BaseModel):
    """Schema for Google Cloud Pub/Sub message data."""
    message: PubSubMessage
    subscription: str


@app.get("/")
def root():
    """Root endpoint that returns a welcome message."""
    return {"message": "Google Play RTDN Webhook Test is running!"}


@app.post("/api/v1/webhooks/googleplay/rtdn", status_code=status.HTTP_200_OK)
async def google_play_rtdn(payload: PubSubMessageData) -> Dict[str, Any]:
    """
    Receive and process Google Play Real-Time Developer Notifications (RTDNs).
    
    This endpoint receives notifications from Google Cloud Pub/Sub when events
    occur in Google Play, such as subscription purchases, renewals, or cancellations.
    
    Args:
        payload: The Pub/Sub message payload
        
    Returns:
        A success response to acknowledge receipt of the notification
    """
    logger.info(f"Received Google Play RTDN from subscription: {payload.subscription}")
    
    try:
        # Extract and decode the base64-encoded data
        encoded_data = payload.message.data
        decoded_data = base64.b64decode(encoded_data).decode("utf-8")
        
        # Parse the decoded data as JSON
        notification_data = json.loads(decoded_data)
        
        # Convert to DeveloperNotification model
        notification = DeveloperNotification(**notification_data)
        
        # Log the notification details
        logger.info(f"RTDN Version: {notification.version}")
        logger.info(f"Package Name: {notification.packageName}")
        logger.info(f"Event Time: {notification.eventTimeMillis}")
        
        if notification.subscriptionNotification:
            logger.info(f"Subscription Notification Type: {notification.subscriptionNotification.notificationType}")
            logger.info(f"Subscription ID: {notification.subscriptionNotification.subscriptionId}")
            
            # Process subscription notification
            notification_type = notification.subscriptionNotification.notificationType
            if notification_type == 1:
                logger.info("Subscription was recovered")
            elif notification_type == 2:
                logger.info("Subscription was renewed")
            elif notification_type == 3:
                logger.info("Subscription was canceled by user")
            elif notification_type == 4:
                logger.info("New subscription purchased")
            elif notification_type == 5:
                logger.info("Subscription was paused")
            elif notification_type == 6:
                logger.info("Subscription pause schedule changed")
            elif notification_type == 7:
                logger.info("Subscription was revoked")
            elif notification_type == 8:
                logger.info("Subscription expired")
            elif notification_type == 9:
                logger.info("Subscription was restarted")
            elif notification_type == 10:
                logger.info("Subscription price change confirmed")
            elif notification_type == 11:
                logger.info("Subscription deferred")
            elif notification_type == 12:
                logger.info("Subscription paused schedule changed")
            elif notification_type == 13:
                logger.info("Subscription was paused automatically")
            else:
                logger.info(f"Unknown subscription notification type: {notification_type}")
                
        elif notification.testNotification:
            logger.info("Test Notification")
        elif notification.oneTimeProductNotification:
            logger.info(f"One-Time Product Notification Type: {notification.oneTimeProductNotification.notificationType}")
        elif notification.voidedPurchaseNotification:
            logger.info(f"Voided Purchase Notification Type: {notification.voidedPurchaseNotification.notificationType}")
        
        # Return a success response to acknowledge receipt
        return {"status": "success", "message": "RTDN received and processed"}
    
    except Exception as e:
        logger.error(f"Error processing Google Play RTDN: {str(e)}", exc_info=True)
        # Still return a success response to prevent Pub/Sub from retrying
        return {"status": "error", "message": str(e)}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
