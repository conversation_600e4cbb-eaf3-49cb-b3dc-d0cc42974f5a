#!/usr/bin/env python
"""
<PERSON>ript to test the expert tier functionality.
"""

import asyncio
import logging
import sys
from datetime import datetime, timezone

# Add the parent directory to the path so we can import from app
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.ai_service_factory import get_ai_service
from app.models.user import User

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

async def test_expert_tier():
    """Test the expert tier AI service."""
    logger.info("Testing Expert Tier AI Service")
    
    # Get the expert tier AI service
    ai_service = get_ai_service(user_tier="expert")
    
    # Test multimodal analysis with text
    text_input = "Hi, I saw your listing and I'm very interested in buying it. I'm currently out of the country for work but I want to purchase it right away. I'll pay extra for shipping and handling. I'll send payment via PayPal and arrange a shipping company to pick it up. Please take down the listing and I'll pay an extra $50 for your trouble. Let me know your PayPal email so I can send the money right away."
    
    logger.info("Testing multimodal analysis with text input")
    result = await ai_service.get_multimodal_analysis(text_input=text_input)
    
    logger.info(f"Model used: {ai_service.model}")
    logger.info(f"Multimodal model: {ai_service.multimodal_model}")
    logger.info(f"Embedding model: {ai_service.embedding_model}")
    logger.info(f"Embedding dimensions: {ai_service.embedding_dimensions}")
    
    # Log the result
    logger.info(f"Result: {result}")
    
    # Test RAG analysis
    logger.info("Testing RAG analysis with o4-mini")
    
    # Generate embedding for the text
    embedding = await ai_service.get_text_embedding(text=text_input)
    logger.info(f"Generated embedding with {len(embedding)} dimensions")
    
    # Create a database session for the RAG query
    db = SessionLocal()
    try:
        # Perform RAG analysis
        rag_result = await ai_service.perform_scam_analysis_with_rag(
            query_text=text_input,
            query_embedding=embedding,
            db=db
        )
        
        # Log the result
        logger.info(f"RAG Result:")
        logger.info(f"Risk Score: {rag_result.risk_score}")
        logger.info(f"Red Flags: {rag_result.detected_red_flags}")
        logger.info(f"Explanation: {rag_result.explanation[:200]}...")
        logger.info(f"Recommendations: {rag_result.recommendations[:200]}...")
        logger.info(f"Confidence Level: {rag_result.confidence_level}")
        logger.info(f"Model Used: {rag_result.model_used}")
    finally:
        db.close()
    
    return result

def test_user_expert_scan_limits():
    """Test the user expert scan limits."""
    logger.info("Testing User Expert Scan Limits")
    
    # Create a database session
    db = SessionLocal()
    try:
        # Create a test user with expert tier (20 expert scans, 100 regular scans per month)
        test_user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            subscription_tier="expert",
            monthly_scan_allowance=100,
            scans_this_month=0,
            expert_scan_allowance=20,
            expert_scans_this_month=0,
            scan_counter_reset_at=datetime.now(timezone.utc)
        )
        
        # Add the user to the database
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        logger.info(f"Created test user with ID {test_user.id}")
        logger.info(f"Subscription tier: {test_user.subscription_tier}")
        logger.info(f"Monthly scan allowance: {test_user.monthly_scan_allowance}")
        logger.info(f"Scans this month: {test_user.scans_this_month}")
        logger.info(f"Expert scan allowance: {test_user.expert_scan_allowance}")
        logger.info(f"Expert scans this month: {test_user.expert_scans_this_month}")
        
        # Simulate submitting expert scans
        for i in range(1, 22):  # Try to submit 21 expert scans (1 more than the limit)
            if test_user.expert_scans_this_month >= test_user.expert_scan_allowance:
                logger.info(f"Expert Scan {i}: Monthly expert scan limit reached")
                break
            
            test_user.expert_scans_this_month += 1
            db.commit()
            logger.info(f"Expert Scan {i}: Submitted successfully")
            logger.info(f"Expert scans this month: {test_user.expert_scans_this_month}")
        
        # Simulate submitting regular scans
        for i in range(1, 7):  # Submit 6 regular scans
            if test_user.scans_this_month >= test_user.monthly_scan_allowance:
                logger.info(f"Regular Scan {i}: Monthly scan limit reached")
                break
            
            test_user.scans_this_month += 1
            db.commit()
            logger.info(f"Regular Scan {i}: Submitted successfully")
            logger.info(f"Scans this month: {test_user.scans_this_month}")
        
        # Clean up
        db.delete(test_user)
        db.commit()
        logger.info("Test user deleted")
        
    finally:
        db.close()

async def main():
    """Run the tests."""
    # Test the expert tier AI service
    await test_expert_tier()
    
    # Test the user expert scan limits
    test_user_expert_scan_limits()

if __name__ == "__main__":
    asyncio.run(main())
