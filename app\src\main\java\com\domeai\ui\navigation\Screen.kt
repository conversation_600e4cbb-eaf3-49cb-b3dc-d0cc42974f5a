package com.domeai.ui.navigation

sealed class Screen(val route: String) {
    object Welcome : Screen("welcome")
    object Login : Screen("login")
    object SignUp : Screen("signup")
    object ForgotPassword : Screen("forgot_password")
    object DataConsent : Screen("data_consent")
    object Onboarding : Screen("onboarding")
    object Main : Screen("main")
    object ManualAnalysis : Screen("manual_analysis")
    object ScanHistory : Screen("scan_history")
    object ScanDetail : Screen("scan_detail/{scanId}") {
        fun createRoute(scanId: String) = "scan_detail/$scanId"
    }
    object ScanAction : Screen("scan_action/{scanId}") {
        fun createRoute(scanId: String) = "scan_action/$scanId"
    }
    object Settings : Screen("settings")
    object AccountSettings : Screen("account_settings")
    object ChangePassword : Screen("change_password")
    object SubscriptionDetails : Screen("subscription_details")
    object PrivacySecurity : Screen("privacy_security")
    object UserProfileMenu : Screen("user_profile_menu")
    object TermsOfService : Screen("terms_of_service")
    object PrivacyPolicy : Screen("privacy_policy")
}
