"""
Service for interacting with Google Play Billing.

This service handles verification of purchases and processing of Real-Time Developer Notifications (RTDNs).
"""
import logging
import json
import os
import time
from datetime import datetime, timezone
from typing import Any, Dict, Optional, Tuple, List, Union
from enum import IntEnum

import httpx
from google.oauth2 import service_account
from google.auth.transport.requests import Request
from sqlalchemy.orm import Session
from sqlalchemy import or_

from app.schemas.google_play import DeveloperNotification
from app.models.user import User
from app.core.config import settings
from app.core.google_play_config import (
    GOOGLE_PLAY_SKU_TO_TIER_MAP,
    TIER_ALLOWANCES,
    SUBSCRIPTION_NOTIFICATION_TYPES,
    get_mock_subscription_data
)
from app.crud.crud_user import (
    get_user_by_provider_subscription_id,
    get_user_by_id,
    get_user_by_google_play_purchase_token,
    get_user_by_email
)

# Configure logging
logger = logging.getLogger(__name__)

# Google Play subscription notification types
class SubscriptionNotificationType(IntEnum):
    SUBSCRIPTION_RECOVERED = 1
    SUBSCRIPTION_RENEWED = 2
    SUBSCRIPTION_CANCELED = 3
    SUBSCRIPTION_PURCHASED = 4
    SUBSCRIPTION_ON_HOLD = 5
    SUBSCRIPTION_IN_GRACE_PERIOD = 6
    SUBSCRIPTION_RESTARTED = 7
    SUBSCRIPTION_PRICE_CHANGE_CONFIRMED = 8
    SUBSCRIPTION_DEFERRED = 9
    SUBSCRIPTION_PAUSED = 10
    SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED = 11
    SUBSCRIPTION_REVOKED = 12
    SUBSCRIPTION_EXPIRED = 13

# Google Play one-time product notification types
class OneTimeProductNotificationType(IntEnum):
    ONE_TIME_PRODUCT_PURCHASED = 1
    ONE_TIME_PRODUCT_CANCELED = 2

# Google Play voided purchase notification types
class VoidedPurchaseNotificationType(IntEnum):
    PURCHASE_REFUNDED = 1


async def process_rtdn(db: Session, notification: DeveloperNotification) -> None:
    """
    Process a Google Play Real-Time Developer Notification.

    Args:
        db: Database session
        notification: The notification to process
    """
    logger.info(f"Processing RTDN version {notification.version}")
    logger.info(f"Package Name: {notification.packageName}")
    logger.info(f"Event Time: {notification.eventTimeMillis}")

    if notification.subscriptionNotification:
        notification_type = notification.subscriptionNotification.notificationType
        notification_type_str = SUBSCRIPTION_NOTIFICATION_TYPES.get(notification_type, "UNKNOWN")
        logger.info(f"Subscription Notification Type: {notification_type} ({notification_type_str})")

        await _process_subscription_notification(db, notification)
    elif notification.testNotification:
        logger.info("Received a Test Notification from Google Play.")
        # Test notifications don't require any action
        pass
    elif notification.oneTimeProductNotification:
        notification_type = notification.oneTimeProductNotification.notificationType
        logger.info(f"One-Time Product Notification Type: {notification_type}")

        await _process_one_time_product_notification(db, notification)
    elif notification.voidedPurchaseNotification:
        notification_type = notification.voidedPurchaseNotification.notificationType
        logger.info(f"Voided Purchase Notification Type: {notification_type}")

        await _process_voided_purchase_notification(db, notification)
    else:
        logger.warning(f"Unknown notification type in RTDN: {notification}")

    return


async def _process_subscription_notification(db: Session, notification: DeveloperNotification) -> None:
    """
    Process a subscription notification from Google Play.

    Args:
        db: Database session
        notification: The notification to process
    """
    sub_notification = notification.subscriptionNotification
    notification_type = sub_notification.notificationType
    subscription_id = sub_notification.subscriptionId
    purchase_token = sub_notification.purchaseToken

    logger.info(f"Processing subscription notification type {notification_type} for subscription {subscription_id}")
    logger.info(f"Purchase Token: {purchase_token}")

    # For SUBSCRIPTION_PURCHASED, we might not have a user associated with this purchase token yet
    # For other notification types, we should be able to find the user by purchase token or provider_subscription_id

    if notification_type == SubscriptionNotificationType.SUBSCRIPTION_PURCHASED:
        # For a new subscription purchase, we need to verify the purchase with Google Play
        # and then find or create the user based on the purchase information
        is_valid, subscription_data = await verify_google_play_purchase(
            db=db,
            user_id=None,  # We don't know the user ID yet
            product_id=subscription_id,
            purchase_token=purchase_token,
            notification_type=notification_type,
            use_mock=purchase_token.startswith("test_") or "placeholder" in subscription_id  # Use mock for test tokens
        )

        if is_valid and subscription_data:
            # Try to find the user by external account ID if available
            user = None
            external_account_id = subscription_data.get("externalAccountId") or subscription_data.get("obfuscatedExternalAccountId")

            if external_account_id:
                # In a real implementation, you would have a way to map external_account_id to your user
                # For testing, we'll try to find a user by email that matches the external_account_id
                user = get_user_by_email(db, f"{external_account_id}@example.com")

            # If we still don't have a user, try to find by purchase token
            if not user:
                user = get_user_by_google_play_purchase_token(db, purchase_token)

            # If we still don't have a user, log a warning
            if not user:
                logger.warning(f"No user found for new subscription purchase. Token: {purchase_token}, External ID: {external_account_id}")
                # In a real implementation, you might want to store this purchase information
                # and associate it with a user when they log in or register
                return

            # Update the user's subscription
            await update_user_subscription_from_google_play(
                db=db,
                user=user,
                google_subscription_data=subscription_data,
                product_id=subscription_id
            )
        else:
            logger.error(f"Invalid subscription purchase. Token: {purchase_token}")

    else:
        # For other notification types, try to find the user by purchase token
        user = get_user_by_google_play_purchase_token(db, purchase_token)

        if not user:
            # If not found by purchase token, try by provider_subscription_id
            # This might happen if the purchase token has changed
            order_id = None
            if notification_type == SubscriptionNotificationType.SUBSCRIPTION_RENEWED:
                # For renewals, we need to get the order ID from Google Play
                subscription_data = await get_subscription_data(subscription_id, purchase_token)
                if subscription_data:
                    order_id = subscription_data.get("orderId")

            if order_id:
                user = get_user_by_provider_subscription_id(db, order_id)

        if not user:
            logger.warning(f"No user found for subscription notification. Type: {notification_type}, Token: {purchase_token}")
            return

        # Process based on notification type
        if notification_type == SubscriptionNotificationType.SUBSCRIPTION_RENEWED:
            # Subscription was renewed
            subscription_data = await get_subscription_data(
                subscription_id,
                purchase_token,
                use_mock=purchase_token.startswith("test_") or "placeholder" in subscription_id  # Use mock for test tokens
            )
            if subscription_data:
                await update_user_subscription_from_google_play(
                    db=db,
                    user=user,
                    google_subscription_data=subscription_data,
                    product_id=subscription_id
                )
            else:
                logger.error(f"Failed to get subscription data for renewal. Token: {purchase_token}")

        elif notification_type == SubscriptionNotificationType.SUBSCRIPTION_CANCELED:
            # Subscription was canceled by user
            # Update auto-renew status but keep subscription active until expiry
            logger.info(f"Subscription canceled for user {user.id}. Setting auto_renew_status to False.")
            user.auto_renew_status = False
            user.last_rtdn_received_at = datetime.now(timezone.utc)
            db.add(user)
            db.commit()
            db.refresh(user)

        elif notification_type == SubscriptionNotificationType.SUBSCRIPTION_EXPIRED:
            # Subscription has expired
            # Downgrade user to free tier
            logger.info(f"Subscription expired for user {user.id}. Downgrading to free tier.")
            user.subscription_tier = "free"
            user.monthly_scan_allowance = TIER_ALLOWANCES["free"]["monthly_scans"]
            user.expert_scan_allowance = TIER_ALLOWANCES["free"]["expert_scans"]
            user.auto_renew_status = False
            user.last_rtdn_received_at = datetime.now(timezone.utc)
            db.add(user)
            db.commit()
            db.refresh(user)

        elif notification_type == SubscriptionNotificationType.SUBSCRIPTION_REVOKED:
            # Subscription was revoked (e.g., refunded)
            # Immediately downgrade user to free tier
            logger.info(f"Subscription revoked for user {user.id}. Downgrading to free tier immediately.")
            user.subscription_tier = "free"
            user.monthly_scan_allowance = TIER_ALLOWANCES["free"]["monthly_scans"]
            user.expert_scan_allowance = TIER_ALLOWANCES["free"]["expert_scans"]
            user.auto_renew_status = False
            user.subscription_expiry_date = datetime.now(timezone.utc)
            user.last_rtdn_received_at = datetime.now(timezone.utc)
            db.add(user)
            db.commit()
            db.refresh(user)

        elif notification_type == SubscriptionNotificationType.SUBSCRIPTION_ON_HOLD:
            # Subscription is on hold (e.g., payment issue)
            logger.info(f"Subscription on hold for user {user.id}.")
            # You might want to notify the user or take other actions
            user.last_rtdn_received_at = datetime.now(timezone.utc)
            db.add(user)
            db.commit()
            db.refresh(user)

        elif notification_type == SubscriptionNotificationType.SUBSCRIPTION_IN_GRACE_PERIOD:
            # Subscription is in grace period (e.g., payment issue but still active)
            logger.info(f"Subscription in grace period for user {user.id}.")
            # You might want to notify the user or take other actions
            user.last_rtdn_received_at = datetime.now(timezone.utc)
            db.add(user)
            db.commit()
            db.refresh(user)

        elif notification_type == SubscriptionNotificationType.SUBSCRIPTION_RESTARTED:
            # Subscription was restarted after being on hold or in grace period
            logger.info(f"Subscription restarted for user {user.id}.")
            # Get the latest subscription data from Google Play
            subscription_data = await get_subscription_data(
                subscription_id,
                purchase_token,
                use_mock=purchase_token.startswith("test_") or "placeholder" in subscription_id  # Use mock for test tokens
            )
            if subscription_data:
                await update_user_subscription_from_google_play(
                    db=db,
                    user=user,
                    google_subscription_data=subscription_data,
                    product_id=subscription_id
                )
            else:
                logger.error(f"Failed to get subscription data for restart. Token: {purchase_token}")

        # Other notification types can be handled as needed
        else:
            logger.info(f"Subscription notification type {notification_type} not handled for user {user.id}.")
            # Update the last RTDN received timestamp
            user.last_rtdn_received_at = datetime.now(timezone.utc)
            db.add(user)
            db.commit()
            db.refresh(user)


async def _process_one_time_product_notification(db: Session, notification: DeveloperNotification) -> None:
    """
    Process a one-time product notification from Google Play.

    Args:
        db: Database session
        notification: The notification to process
    """
    one_time_notification = notification.oneTimeProductNotification
    notification_type = one_time_notification.notificationType
    sku = one_time_notification.sku
    purchase_token = one_time_notification.purchaseToken

    logger.info(f"Processing one-time product notification type {notification_type} for SKU {sku}")
    logger.info(f"Purchase Token: {purchase_token}")

    # One-time products could be handled here if needed
    # For example, if you sell scan credits as one-time purchases

    # For testing purposes, we'll just log the notification
    if notification_type == OneTimeProductNotificationType.ONE_TIME_PRODUCT_PURCHASED:
        logger.info(f"One-time product purchased: {sku}")
    elif notification_type == OneTimeProductNotificationType.ONE_TIME_PRODUCT_CANCELED:
        logger.info(f"One-time product canceled: {sku}")
    else:
        logger.info(f"Unknown one-time product notification type: {notification_type}")


async def _process_voided_purchase_notification(db: Session, notification: DeveloperNotification) -> None:
    """
    Process a voided purchase notification from Google Play.

    Args:
        db: Database session
        notification: The notification to process
    """
    voided_notification = notification.voidedPurchaseNotification
    notification_type = voided_notification.notificationType
    purchase_token = voided_notification.purchaseToken
    order_id = voided_notification.orderId

    logger.info(f"Processing voided purchase notification type {notification_type} for order {order_id}")
    logger.info(f"Purchase Token: {purchase_token}")

    # Find the user associated with this purchase
    user = get_user_by_google_play_purchase_token(db, purchase_token)

    if not user:
        # If not found by purchase token, try by provider_subscription_id
        user = get_user_by_provider_subscription_id(db, order_id)

    if not user:
        logger.warning(f"No user found for voided purchase. Token: {purchase_token}, Order ID: {order_id}")
        return

    # If a purchase is refunded, downgrade the user to free tier
    if notification_type == VoidedPurchaseNotificationType.PURCHASE_REFUNDED:
        logger.info(f"Purchase refunded for user {user.id}. Downgrading to free tier immediately.")
        user.subscription_tier = "free"
        user.monthly_scan_allowance = TIER_ALLOWANCES["free"]["monthly_scans"]
        user.expert_scan_allowance = TIER_ALLOWANCES["free"]["expert_scans"]
        user.auto_renew_status = False
        user.subscription_expiry_date = datetime.now(timezone.utc)
        user.last_rtdn_received_at = datetime.now(timezone.utc)
        db.add(user)
        db.commit()
        db.refresh(user)
    else:
        logger.info(f"Unknown voided purchase notification type: {notification_type}")


async def get_subscription_data(
    subscription_id: str,
    purchase_token: str,
    use_mock: bool = False
) -> Optional[Dict[str, Any]]:
    """
    Get subscription data from Google Play Developer API.

    Args:
        subscription_id: The subscription ID (product ID)
        purchase_token: The purchase token
        use_mock: Whether to use mock data instead of making a real API call (for testing)

    Returns:
        Subscription data if successful, None otherwise
    """
    try:
        logger.info(f"Getting subscription data for {subscription_id} with token {purchase_token}")

        # For testing purposes, use mock data if requested
        if use_mock:
            logger.info("Using mock data for testing")
            mock_data = get_mock_subscription_data(subscription_id, purchase_token)
            logger.info(f"Mock subscription data: {json.dumps(mock_data, indent=2)}")
            return mock_data

        # Get access token for Google Play API
        access_token = await _get_google_play_access_token()

        if not access_token:
            logger.error("Failed to get Google Play access token")
            return None

        # Make API request to get subscription data using google-api-python-client
        try:
            from googleapiclient.discovery import build
            from googleapiclient.errors import HttpError

            # Create the Google Play Developer API client
            package_name = settings.GOOGLE_PLAY_PACKAGE_NAME

            # Build the API client
            androidpublisher = build(
                'androidpublisher',
                'v3',
                credentials=None,  # We'll use the access token directly
                cache_discovery=False
            )

            # Create the request
            request = androidpublisher.purchases().subscriptionsv2().get(
                packageName=package_name,
                token=purchase_token
            )

            # Add the access token to the request headers
            request.headers['Authorization'] = f'Bearer {access_token}'

            # Execute the request
            response = request.execute()

            logger.info(f"Successfully retrieved subscription data from Google Play API")
            return response

        except ImportError as e:
            logger.error(f"Failed to import Google API client: {str(e)}")
            logger.warning("Falling back to direct HTTP request")

            # Fallback to direct HTTP request if google-api-python-client is not available
            package_name = settings.GOOGLE_PLAY_PACKAGE_NAME
            url = f"https://androidpublisher.googleapis.com/androidpublisher/v3/applications/{package_name}/purchases/subscriptions/{subscription_id}/tokens/{purchase_token}"

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url,
                    headers={"Authorization": f"Bearer {access_token}"}
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    logger.error(f"Failed to get subscription data: {response.status_code} {response.text}")
                    # Log the specific error message from Google
                    try:
                        error_data = response.json()
                        error_message = error_data.get('error', {}).get('message', 'Unknown error')
                        logger.error(f"Google Play API error: {error_message}")
                    except Exception:
                        pass
                    return None

        except HttpError as e:
            # Handle specific Google API errors
            error_details = json.loads(e.content.decode())
            error_message = error_details.get('error', {}).get('message', str(e))
            logger.error(f"Failed to verify purchase token {purchase_token} for SKU {subscription_id} with Google. Reason: {error_message}")
            return None

    except Exception as e:
        logger.error(f"Error getting subscription data: {str(e)}", exc_info=True)
        return None


async def _get_google_play_access_token() -> Optional[str]:
    """
    Get an access token for the Google Play Developer API.

    Returns:
        Access token if successful, None otherwise
    """
    try:
        # Path to service account key file
        key_file_path = os.path.join(os.path.dirname(__file__), "../../service-account-key.json")

        # Create credentials from service account key file
        credentials = service_account.Credentials.from_service_account_file(
            key_file_path,
            scopes=["https://www.googleapis.com/auth/androidpublisher"]
        )

        # Refresh the credentials if needed
        if credentials.expired:
            credentials.refresh(Request())

        return credentials.token

    except Exception as e:
        logger.error(f"Error getting Google Play access token: {str(e)}", exc_info=True)
        return None


async def verify_google_play_purchase(
    db: Session, user_id: Optional[Any], product_id: str, purchase_token: str,
    notification_type: Optional[int] = None, use_mock: bool = False
) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    Verify a Google Play purchase with the Google Play Developer API.

    Args:
        db: Database session
        user_id: ID of the user making the purchase (can be None for RTDN processing)
        product_id: ID of the product being purchased
        purchase_token: Purchase token to verify
        notification_type: Type of notification (for logging purposes)
        use_mock: Whether to use mock data instead of making a real API call (for testing)

    Returns:
        Tuple of (is_valid, purchase_data)
    """
    logger.info(f"Verifying Google Play Purchase for user {user_id}, product {product_id}, token {purchase_token}")

    if notification_type:
        notification_type_str = SUBSCRIPTION_NOTIFICATION_TYPES.get(notification_type, "UNKNOWN")
        logger.info(f"Notification Type: {notification_type} ({notification_type_str})")

    try:
        # Get subscription data from Google Play
        subscription_data = await get_subscription_data(product_id, purchase_token, use_mock=use_mock)

        if not subscription_data:
            logger.error("Failed to get subscription data")

            # For testing purposes, if real API call fails but we're in a test environment,
            # we can still proceed with mock data to test the rest of the flow
            if not use_mock and (purchase_token.startswith("test_") or "placeholder" in product_id):
                logger.warning("Using mock data as fallback for testing purposes")
                subscription_data = get_mock_subscription_data(product_id, purchase_token)
                logger.info(f"Mock subscription data: {json.dumps(subscription_data, indent=2)}")
            else:
                return False, None

        # Check if the subscription is valid
        # purchaseState: 0 = purchased, 1 = canceled, 2 = pending
        purchase_state = subscription_data.get("purchaseState", -1)

        # Check if the subscription is active
        expiry_time_millis = int(subscription_data.get("expiryTimeMillis", 0))
        current_time_millis = int(time.time() * 1000)

        is_valid = (purchase_state == 0 and expiry_time_millis > current_time_millis)

        if is_valid:
            logger.info(f"Purchase is valid. Expiry: {datetime.fromtimestamp(expiry_time_millis / 1000, tz=timezone.utc)}")

            # If user_id is provided, get the user and update their subscription
            if user_id:
                user = get_user_by_id(db, user_id)
                if user:
                    await update_user_subscription_from_google_play(
                        db=db,
                        user=user,
                        google_subscription_data=subscription_data,
                        product_id=product_id
                    )
                else:
                    logger.error(f"User not found: {user_id}")
                    return True, subscription_data  # Still return valid but no user update
        else:
            logger.warning(f"Purchase is not valid. Purchase state: {purchase_state}, Expiry: {datetime.fromtimestamp(expiry_time_millis / 1000, tz=timezone.utc)}")

        return is_valid, subscription_data

    except Exception as e:
        logger.error(f"Error verifying Google Play purchase: {str(e)}", exc_info=True)
        return False, None


async def update_user_subscription_from_google_play(
    db: Session, user: User, google_subscription_data: Dict[str, Any], product_id: str
) -> User:
    """
    Update a user's subscription based on Google Play purchase information.

    Args:
        db: Database session
        user: User to update
        google_subscription_data: Subscription data from Google Play
        product_id: Google Play product ID

    Returns:
        Updated user
    """
    logger.info(f"Updating subscription for user {user.id} with product {product_id}")

    # Extract data from Google subscription data
    purchase_token = google_subscription_data.get("purchaseToken", "")
    order_id = google_subscription_data.get("orderId", "")
    expiry_time_millis = int(google_subscription_data.get("expiryTimeMillis", 0))
    auto_renew_status = google_subscription_data.get("autoRenewing", False)
    is_trial_period = google_subscription_data.get("paymentState") == 2  # Payment state 2 indicates free trial

    # Convert expiry time from milliseconds to datetime
    expiry_date = datetime.fromtimestamp(expiry_time_millis / 1000, tz=timezone.utc)

    # Map Google Play product ID to our subscription tier
    subscription_tier = GOOGLE_PLAY_SKU_TO_TIER_MAP.get(product_id, "free")

    # Update user's subscription information
    user.subscription_provider = "google_play"
    user.provider_subscription_id = order_id
    user.google_play_purchase_token = purchase_token
    user.subscription_product_id = product_id
    user.subscription_expiry_date = expiry_date
    user.auto_renew_status = auto_renew_status
    user.is_trial_period = is_trial_period
    user.subscription_tier = subscription_tier

    # Update scan allowances based on subscription tier
    user.monthly_scan_allowance = TIER_ALLOWANCES[subscription_tier]["monthly_scans"]
    user.expert_scan_allowance = TIER_ALLOWANCES[subscription_tier]["expert_scans"]

    # Reset scan counters
    # In a real implementation, you might want to check if the user is upgrading from a lower tier
    # and only reset counters in that case
    user.scans_this_month = 0
    user.expert_scans_this_month = 0
    user.scan_counter_reset_at = datetime.now(timezone.utc)

    # Update last RTDN received timestamp
    user.last_rtdn_received_at = datetime.now(timezone.utc)

    # Save changes
    db.add(user)
    db.commit()
    db.refresh(user)

    # Log the subscription update
    logger.info(f"Updated subscription for user {user.id} to tier {subscription_tier}, expires at {expiry_date}")
    logger.info(f"Monthly scan allowance: {user.monthly_scan_allowance}, Expert scan allowance: {user.expert_scan_allowance}")

    return user
