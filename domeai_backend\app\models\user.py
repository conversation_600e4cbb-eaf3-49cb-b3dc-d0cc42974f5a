from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, DateTime, Text, func
from sqlalchemy.orm import relationship

from app.core.database import Base


class User(Base):
    """
    SQLAlchemy model for the users table.
    """
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Subscription and scan limit fields
    subscription_tier = Column(String, default="free", nullable=False)  # Values: "free", "premium", "expert"
    monthly_scan_allowance = Column(Integer, default=5, nullable=False)  # Regular scan allowance (GPT-4.1 for premium/expert)
    scans_this_month = Column(Integer, default=0, nullable=False)  # Regular scan counter
    scan_counter_reset_at = Column(DateTime, default=func.now(), nullable=False)  # Reset date for both regular and expert scans

    # Expert scan fields
    expert_scan_allowance = Column(Integer, default=0, nullable=False)  # Expert scan allowance (o4-mini for expert tier)
    expert_scans_this_month = Column(Integer, default=0, nullable=False)  # Expert scan counter

    # Google Play subscription fields
    subscription_provider = Column(String, nullable=True, default=None)  # Will store "google_play"
    provider_subscription_id = Column(String, nullable=True, unique=True, index=True, default=None)  # Google Play's orderId
    google_play_purchase_token = Column(Text, nullable=True, default=None)  # Latest purchaseToken for verification
    subscription_product_id = Column(String, nullable=True, default=None)  # e.g., "domeai_premium_monthly"
    subscription_expiry_date = Column(DateTime(timezone=True), nullable=True, default=None)
    auto_renew_status = Column(Boolean, nullable=True, default=None)
    is_trial_period = Column(Boolean, nullable=True, default=None)
    last_rtdn_received_at = Column(DateTime(timezone=True), nullable=True, default=None)  # Track latest RTDN processed

    # Relationships
    scans = relationship("Scan", back_populates="owner")
    scan_sessions = relationship("ScanSession", back_populates="owner")
