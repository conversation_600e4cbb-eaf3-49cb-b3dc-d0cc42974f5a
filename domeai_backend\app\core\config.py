from typing import Any, Dict, List, Optional, Union
import os
from pathlib import Path

from pydantic import AnyHttpUrl, PostgresDsn, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore"  # Ignore extra fields in the environment
    )

    # API settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "DomeAI Scam Detector API"

    # CORS settings
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # Database settings
    DATABASE_URL: str

    @model_validator(mode="after")
    def validate_database_url(self) -> "Settings":
        # Convert DATABASE_URL to PostgresDsn if needed
        if self.DATABASE_URL and not self.DATABASE_URL.startswith("postgresql://"):
            raise ValueError("DATABASE_URL must start with postgresql://")
        return self

    # Security settings
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Celery settings (optional for deployment without background tasks)
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"

    # File upload settings
    UPLOAD_DIRECTORY: str = os.path.join(os.getcwd(), "uploads")
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10 MB

    # AI service settings
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    GOOGLE_API_KEY: Optional[str] = None

    # Default AI service to use
    DEFAULT_AI_SERVICE: str = "openai"  # Options: "openai", "anthropic", "google"

    # OpenAI model settings for different tiers
    # Premium tier (GPT-4.1)
    OPENAI_PREMIUM_ANALYSIS_MODEL_NAME: str = "gpt-4.1"
    OPENAI_PREMIUM_EMBEDDING_MODEL_NAME: str = "text-embedding-3-large"
    OPENAI_PREMIUM_EMBEDDING_DIMENSIONS: int = 1536

    # Basic tier (GPT-4.1 mini)
    OPENAI_BASIC_ANALYSIS_MODEL_NAME: str = "gpt-4.1-mini"
    OPENAI_BASIC_EMBEDDING_MODEL_NAME: str = "text-embedding-3-small"
    OPENAI_BASIC_EMBEDDING_DIMENSIONS: int = 1536

    # Expert tier
    OPENAI_EXPERT_ANALYSIS_MODEL_NAME: str = "o4-mini"
    OPENAI_EXPERT_MULTIMODAL_MODEL_NAME: str = "gpt-4.1"  # Use GPT-4.1 for initial OCR/context
    OPENAI_EXPERT_EMBEDDING_MODEL_NAME: str = "text-embedding-3-large"
    OPENAI_EXPERT_EMBEDDING_DIMENSIONS: int = 3072

    # Session limits
    SESSION_MAX_INPUTS_PREMIUM: int = 10
    SESSION_MAX_INPUTS_EXPERT: int = 15
    SESSION_ACTIVITY_WINDOW_HOURS: int = 4  # If a session has no new scans for this many hours, start a new session

    # Google Play settings
    GOOGLE_PLAY_PACKAGE_NAME: str = "com.domeai.scamdetector"

    # Contest feature flags
    HOSTINGER_HORIZONS_CONTEST_MODE: bool = True  # Set to False after contest


settings = Settings()

# Create uploads directory if it doesn't exist
uploads_path = Path(settings.UPLOAD_DIRECTORY)
if not uploads_path.exists():
    uploads_path.mkdir(parents=True, exist_ok=True)
