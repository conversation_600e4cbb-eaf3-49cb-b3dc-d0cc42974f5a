from fastapi import FastAPI
from pydantic import BaseModel
from typing import Dict, Any, Optional

app = FastAPI()

class PubSubMessage(BaseModel):
    data: str
    messageId: str
    publishTime: str
    attributes: Optional[Dict[str, str]] = None

class PubSubMessageData(BaseModel):
    message: PubSubMessage
    subscription: str

@app.get("/")
def root():
    return {"message": "RTDN FastAPI app is running!"}

@app.post("/api/v1/webhooks/googleplay/rtdn")
async def google_play_rtdn(payload: PubSubMessageData):
    print(f"Received RTDN: {payload}")
    return {"status": "success", "message": "RTDN received and processed"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
