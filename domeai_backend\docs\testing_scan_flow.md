# Testing the Scan Submission and Asynchronous Processing Flow

This document outlines the approach for testing the scan submission and asynchronous processing flow in the DomeAI Scam Detector backend.

## Prerequisites

- The DomeAI backend is running with all services (API, Celery worker, Redis, PostgreSQL).
- The database migrations have been applied.
- A user account has been created for authentication.

## Test Flow

### 1. Authentication

First, obtain a JWT token by logging in:

```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "username=<EMAIL>&password=StrongPassword123"
```

Or using PowerShell:

```powershell
$body = "username=<EMAIL>&password=StrongPassword123"
$response = Invoke-WebRequest -Uri "http://localhost:8000/api/v1/auth/login" -Method POST -Headers @{"Content-Type"="application/x-www-form-urlencoded"} -Body $body
$token = ($response.Content | ConvertFrom-Json).access_token
```

### 2. Submit a Scan Request

Submit a scan request with the JWT token:

```bash
curl -X POST "http://localhost:8000/api/v1/scans/" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"input_text": "This is a test scan", "input_content_type": "text"}'
```

Or using PowerShell:

```powershell
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}
$body = '{"input_text": "This is a test scan", "input_content_type": "text"}'
$response = Invoke-WebRequest -Uri "http://localhost:8000/api/v1/scans/" -Method POST -Headers $headers -Body $body
$scan = $response.Content | ConvertFrom-Json
$scanId = $scan.id
```

The response should include a scan object with status "pending" and a 202 Accepted status code.

### 3. Check Scan Status

Check the status of the scan:

```bash
curl -X GET "http://localhost:8000/api/v1/scans/SCAN_ID" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Or using PowerShell:

```powershell
$response = Invoke-WebRequest -Uri "http://localhost:8000/api/v1/scans/$scanId" -Method GET -Headers $headers
$scan = $response.Content | ConvertFrom-Json
```

Initially, the scan status should be "pending" or "processing".

### 4. Wait for Processing

Wait for a short period (e.g., 15 seconds) to allow the Celery worker to process the scan.

### 5. Check Final Result

Check the scan status again:

```bash
curl -X GET "http://localhost:8000/api/v1/scans/SCAN_ID" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Or using PowerShell:

```powershell
$response = Invoke-WebRequest -Uri "http://localhost:8000/api/v1/scans/$scanId" -Method GET -Headers $headers
$scan = $response.Content | ConvertFrom-Json
```

The scan status should now be "completed" and the `analysis_result` field should contain the simulated AI analysis data.

### 6. List All Scans

List all scans for the authenticated user:

```bash
curl -X GET "http://localhost:8000/api/v1/scans/" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Or using PowerShell:

```powershell
$response = Invoke-WebRequest -Uri "http://localhost:8000/api/v1/scans/" -Method GET -Headers $headers
$scans = $response.Content | ConvertFrom-Json
```

The response should include a list of scans, including the one we just created.

## Troubleshooting

If the scan status remains "pending" for an extended period:

1. Check if the Celery worker is running:
   ```bash
   docker ps | grep celery_worker
   ```

2. Check the Celery worker logs for errors:
   ```bash
   docker logs domeai_backend-celery_worker-1
   ```

3. Ensure Redis is running and accessible:
   ```bash
   docker ps | grep redis
   ```

4. Check the API logs for any errors related to task submission:
   ```bash
   docker logs domeai_backend-api-1
   ```

If the scan status changes to "failed":

1. Check the `error_message` field in the scan object for details.
2. Check the Celery worker logs for more detailed error information.

## Automated Testing

For automated testing, consider:

1. Unit tests for the CRUD operations in `crud_scan.py`.
2. Integration tests for the API endpoints in `scans.py`.
3. End-to-end tests that simulate the entire flow from scan submission to completion.

For Celery task testing, consider using the `celery.contrib.testing.worker` module to run a worker in the test environment.
