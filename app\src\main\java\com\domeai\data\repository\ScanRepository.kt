package com.domeai.data.repository

import android.content.Context
import android.net.Uri
import com.domeai.data.model.DetectionDetail
import com.domeai.data.model.RiskLevel
import com.domeai.data.model.ScanResult
import com.domeai.data.model.ScanSourceType
import com.domeai.data.model.ScanType
import com.domeai.data.model.content
import com.domeai.data.model.description
import com.domeai.data.model.getDate
import com.domeai.data.model.isFavorite
import com.domeai.data.model.network.ScanResponse
import com.domeai.data.model.title
import com.domeai.data.model.type
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import java.util.Date
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository interface for scan operations
 */
interface ScanRepository {
    /**
     * Get all scan results
     */
    fun getAllScans(): Flow<List<ScanResult>>

    /**
     * Get recent scan results
     */
    fun getRecentScans(limit: Int): Flow<List<ScanResult>>

    /**
     * Get scan result by ID
     */
    fun getScanById(id: String): Flow<ScanResult?>

    /**
     * Add a new scan result
     */
    suspend fun addScan(scan: ScanResult)

    /**
     * Update an existing scan result
     */
    suspend fun updateScan(scan: ScanResult)

    /**
     * Delete a scan result
     */
    suspend fun deleteScan(id: String)

    /**
     * Toggle favorite status of a scan
     */
    suspend fun toggleFavorite(id: String)

    /**
     * Search for scans by query
     */
    fun searchScans(query: String): Flow<List<ScanResult>>

    /**
     * Filter scans by risk level
     */
    fun filterByRiskLevel(riskLevel: RiskLevel?): Flow<List<ScanResult>>

    /**
     * Filter scans by type
     */
    fun filterByType(type: ScanType?): Flow<List<ScanResult>>

    /**
     * Filter scans by date range
     */
    fun filterByDateRange(startDate: Date?, endDate: Date?): Flow<List<ScanResult>>

    /**
     * Get favorite scans
     */
    fun getFavoriteScans(): Flow<List<ScanResult>>

    /**
     * Submit a text scan to the backend
     */
    suspend fun submitTextScan(text: String, userContext: String? = null, sessionId: String? = null): Result<ScanResponse>

    /**
     * Submit a URL scan to the backend
     */
    suspend fun submitUrlScan(url: String, userContext: String? = null, sessionId: String? = null): Result<ScanResponse>

    /**
     * Submit an image scan to the backend
     */
    suspend fun submitImageScan(imageUri: Uri, userContext: String? = null, sessionId: String? = null, context: Context, useExpertScan: Boolean = false): Result<ScanResponse>

    /**
     * Get scan details by ID from the backend
     */
    suspend fun getScanDetails(scanId: String): Result<ScanResponse>
}
