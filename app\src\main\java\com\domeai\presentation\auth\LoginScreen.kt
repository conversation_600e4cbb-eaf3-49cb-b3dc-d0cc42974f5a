package com.domeai.presentation.auth

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.isImeVisible

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.relocation.BringIntoViewRequester
import androidx.compose.foundation.relocation.bringIntoViewRequester
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.focus.onFocusChanged

import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.domeai.ui.composables.EmailField
import com.domeai.ui.composables.PasswordField
import com.domeai.ui.composables.PrimaryButton
import com.domeai.ui.composables.TextActionButton
import com.domeai.ui.composables.VerticalSpacer

@OptIn(ExperimentalFoundationApi::class, ExperimentalLayoutApi::class)
@Composable
fun LoginScreen(
    onNavigateToSignUp: () -> Unit,
    onNavigateToForgotPassword: () -> Unit,
    onNavigateToMain: () -> Unit,
    viewModel: LoginViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Handle UI events
    LaunchedEffect(key1 = true) {
        viewModel.uiState.collect { state ->
            if (state.errorMessage != null) {
                snackbarHostState.showSnackbar(state.errorMessage)
            }
        }
    }

    LaunchedEffect(key1 = viewModel) {
        viewModel.uiEvent.collect { event ->
            when (event) {
                is LoginUiEvent.NavigateToSignUp -> onNavigateToSignUp()
                is LoginUiEvent.NavigateToForgotPassword -> onNavigateToForgotPassword()
                is LoginUiEvent.NavigateToMain -> onNavigateToMain()
            }
        }
    }

    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
        modifier = Modifier.imePadding()
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(40.dp))

            Text(
                text = "DomeAI Scam Detector",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )

            VerticalSpacer(height = 8)

            Text(
                text = "Protect yourself from online scams",
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )

            VerticalSpacer(height = 40)

            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                val scrollState = rememberScrollState()
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                        .verticalScroll(scrollState)
                ) {
                    Text(
                        text = "Login",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )

                    VerticalSpacer(height = 24)

                    EmailField(
                        email = uiState.email,
                        onEmailChange = { viewModel.sendAction(LoginUiAction.UpdateEmail(it)) },
                        errorMessage = uiState.emailError,
                        enabled = !uiState.isLoading,
                        imeAction = ImeAction.Next
                    )

                    VerticalSpacer()

                    // Password field with BringIntoViewRequester
                    val passwordRequester = remember { BringIntoViewRequester() }
                    val coroutineScope = rememberCoroutineScope()
                    var isPasswordFocused by remember { mutableStateOf(false) }
                    // We'll use a simpler approach without keyboard tracking

                    // This column contains password field and login button
                    // to ensure they all scroll into view together
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .bringIntoViewRequester(passwordRequester)
                    ) {
                        PasswordField(
                            password = uiState.password,
                            onPasswordChange = { viewModel.sendAction(LoginUiAction.UpdatePassword(it)) },
                            errorMessage = uiState.passwordError,
                            enabled = !uiState.isLoading,
                            imeAction = ImeAction.Done,
                            onImeAction = { viewModel.sendAction(LoginUiAction.Login) },
                            modifier = Modifier
                                .fillMaxWidth()
                                .onFocusChanged { focusState ->
                                    val wasFocused = isPasswordFocused
                                    isPasswordFocused = focusState.isFocused
                                }
                        )

                        VerticalSpacer()

                        TextActionButton(
                            text = "Forgot Password?",
                            onClick = { viewModel.sendAction(LoginUiAction.NavigateToForgotPassword) },
                            modifier = Modifier.align(Alignment.End)
                        )

                        VerticalSpacer(height = 24)

                        PrimaryButton(
                            text = "Login",
                            onClick = { viewModel.sendAction(LoginUiAction.Login) },
                            isLoading = uiState.isLoading,
                            enabled = uiState.isFormValid
                        )

                        VerticalSpacer(height = 16)

                        TextActionButton(
                            text = "Don't have an account? Sign Up",
                            onClick = { viewModel.sendAction(LoginUiAction.NavigateToSignUp) },
                            modifier = Modifier.align(Alignment.CenterHorizontally)
                        )
                    }

                    // Track keyboard visibility
                    val imeVisible = WindowInsets.isImeVisible

                    // LaunchedEffect to handle scrolling when password field is focused
                    LaunchedEffect(isPasswordFocused, imeVisible) {
                        if (isPasswordFocused) {
                            // First immediate scroll attempt
                            passwordRequester.bringIntoView()

                            // Then a series of delayed attempts to ensure it works
                            // This helps catch the exact moment when the keyboard is fully visible
                            for (delay in listOf(100L, 300L, 500L)) {
                                kotlinx.coroutines.delay(delay)
                                if (isPasswordFocused && imeVisible) {
                                    passwordRequester.bringIntoView()
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
