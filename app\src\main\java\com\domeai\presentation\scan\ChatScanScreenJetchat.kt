package com.domeai.presentation.scan

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.FileProvider
import java.io.File
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.isImeVisible
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.union
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledTonalButton
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.domeai.data.model.RiskLevel
import com.domeai.data.model.ScanResult
import com.domeai.data.model.ScanSourceType
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.UUID

/**
 * Improved chat screen for the DomeAI app, based on Jetchat's approach
 * for proper keyboard handling and input field positioning.
 *
 * This implementation follows Jetpack Compose best practices for IME and inset handling.
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)
@Composable
fun ChatScanScreenJetchat(
    onScanDetail: (String) -> Unit,
    isStandalone: Boolean = false,
    viewModel: ChatScanViewModel = hiltViewModel()
) {
    // Collect UI state from ViewModel
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()
    // Use LazyListState from ViewModel for true persistence across tab switches
    val lazyListState = viewModel.lazyListState
    val focusManager = LocalFocusManager.current
    val context = LocalContext.current

    // State for the input text
    var inputText by remember { mutableStateOf("") }

    // State for the selected image
    var selectedImageUri by remember { mutableStateOf<Uri?>(null) }

    // State for the selected scan type
    var selectedScanType by remember { mutableStateOf(ScanInputType.TEXT) }

    // State for showing the scan type selector
    var showScanTypeSelector by remember { mutableStateOf(false) }

    // State for showing the image source dialog
    var showImageSourceDialog by remember { mutableStateOf(false) }

    // State for loading
    var isLoading by remember { mutableStateOf(false) }

    // Get chat messages from ViewModel (persistent across tab switches)
    val chatMessages = uiState.chatMessages
    val chatScanIds = uiState.chatScanIds

    // DEBUG: Log the chatMessages references
    Log.d("ChatMessagesDebug", "=== COMPOSITION ===")
    Log.d("ChatMessagesDebug", "Local chatMessages.size: ${chatMessages.size}")
    Log.d("ChatMessagesDebug", "uiState.chatMessages.size: ${uiState.chatMessages.size}")
    Log.d("ChatMessagesDebug", "Are they same reference? ${chatMessages === uiState.chatMessages}")

    // Track processed scan IDs to prevent duplicate "Analyzing" messages
    var processedScanIds by remember { mutableStateOf<Set<String>>(emptySet()) }

    // Track previous message count and whether user was at bottom
    var previousMessageCount by remember { mutableStateOf(chatMessages.size) }
    var wasUserAtBottom by remember { mutableStateOf(true) }

    // Auto-scroll button state
    var showScrollButton by remember { mutableStateOf(false) }
    var scrollButtonJob by remember { mutableStateOf<kotlinx.coroutines.Job?>(null) }

    // Function to start a new chat
    fun startNewChat() {
        viewModel.startNewChat()
        inputText = ""
        selectedImageUri = null
        selectedScanType = ScanInputType.TEXT
        showScanTypeSelector = false
        isLoading = false
        // Reset processed scan IDs for new chat
        processedScanIds = emptySet()
        // Reset scroll tracking
        previousMessageCount = 0
        wasUserAtBottom = true
    }

    // Observe UI state changes
    LaunchedEffect(uiState) {
        // Show error message if any
        uiState.errorMessage?.let { error ->
            snackbarHostState.showSnackbar(error)
            viewModel.clearError()
        }

        // Update loading state
        isLoading = uiState.isLoading

        // If we have a scan ID and status, add a simple processing message
        if (!isLoading && uiState.scanId != null && uiState.scanStatus != null) {
            val scanId = uiState.scanId!!
            Log.d("DomeAI_DuplicateAnalyzing", "=== CHECKING SCAN ID ===")
            Log.d("DomeAI_DuplicateAnalyzing", "Scan ID: $scanId, Status: ${uiState.scanStatus}")
            Log.d("DomeAI_DuplicateAnalyzing", "ProcessedScanIds: $processedScanIds")
            Log.d("DomeAI_DuplicateAnalyzing", "Has analyzing message: ${chatMessages.any { !it.isUser && it.content.contains("Analyzing your submission") }}")
            Log.d("DomeAI_DuplicateAnalyzing", "Current chat messages count: ${chatMessages.size}")
            chatMessages.forEachIndexed { index, msg ->
                Log.d("DomeAI_DuplicateAnalyzing", "  [$index] isUser=${msg.isUser}, content=${msg.content.take(50)}...")
            }

            // Check if we already have scan results for this scan ID
            val hasResultsForThisScan = chatMessages.any { message ->
                message.scanResult?.id == scanId
            }

            Log.d("DomeAI_DuplicateAnalyzing", "Has results for scan $scanId: $hasResultsForThisScan")

            // Only add the message if we haven't processed this scan ID yet AND there's no existing analyzing message AND no results yet
            if (!processedScanIds.contains(scanId) &&
                !chatMessages.any { !it.isUser && it.content.contains("Analyzing your submission") } &&
                !hasResultsForThisScan) {
                Log.d("DomeAI_DuplicateAnalyzing", ">>> ADDING ANALYZING MESSAGE for scan ID: $scanId")
                viewModel.addAnalyzingMessage()
                // Track this scan ID as part of this chat session
                viewModel.trackScanId(scanId)
                // Mark this scan ID as processed
                processedScanIds = processedScanIds + scanId
                Log.d("DomeAI_DuplicateAnalyzing", ">>> Updated processedScanIds: $processedScanIds")
            } else {
                Log.d("DomeAI_DuplicateAnalyzing", ">>> SKIPPING analyzing message - already processed or exists")
            }
        }
    }

    // Observe scan results and add them to chat
    LaunchedEffect(uiState.latestScanResults, chatScanIds) {
        Log.d("ChatScanScreenJetchat", "Observing scan results: ${uiState.latestScanResults.size} results")
        Log.d("ChatScanScreenJetchat", "Chat scan IDs: $chatScanIds")
        Log.d("ChatScanScreenJetchat", "Current chat messages: ${chatMessages.size}")

        // Process new scan results, but only those that belong to this chat session
        uiState.latestScanResults.forEach { scanResult ->
            // Only process scans that belong to this chat session AND are completed (not still processing)
            if (chatScanIds.contains(scanResult.id) && scanResult.explanation != "Processing...") {
                Log.d("ChatScanScreenJetchat", "Processing scan result: ${scanResult.id}, explanation: ${scanResult.explanation.take(50)}...")

                // Check if this scan result is already in chat messages
                val alreadyInChat = chatMessages.any { message ->
                    message.scanResult?.id == scanResult.id
                }

                if (!alreadyInChat) {
                    Log.d("ChatScanScreenJetchat", "Adding new scan result to chat: ${scanResult.id}")
                    Log.d("ChatScanScreenJetchat", "Chat messages before processing:")
                    chatMessages.forEachIndexed { index, msg ->
                        Log.d("ChatScanScreenJetchat", "  [$index] isUser=${msg.isUser}, content=${msg.content.take(30)}...")
                    }

                    // Check if we already have a result for this scan ID to avoid duplicates
                    val hasExistingResult = chatMessages.any { msg ->
                        msg.scanResult?.id == scanResult.id
                    }

                    if (hasExistingResult) {
                        Log.d("ChatScanScreenJetchat", "Scan result ${scanResult.id} already exists in chat, skipping duplicate")
                        return@LaunchedEffect
                    }

                    // Use ViewModel to add scan result message (handles removing "Analyzing" message)
                    viewModel.addScanResultMessage(scanResult)
                    isLoading = false
                }
            } else {
                Log.d("ChatScanScreenJetchat", "Skipping scan result ${scanResult.id} - not part of this chat session")
            }
        }
    }

    // Observe the chat reset trigger from SharedPreferences
    val sharedPrefs = remember { context.getSharedPreferences("dome_ai_prefs", Context.MODE_PRIVATE) }
    var resetTrigger by remember { mutableStateOf(sharedPrefs.getLong("chat_reset_trigger", 0L)) }

    // Check for reset trigger changes
    LaunchedEffect(Unit) {
        while (true) {
            delay(500)
            val newTrigger = sharedPrefs.getLong("chat_reset_trigger", 0L)
            if (newTrigger != resetTrigger) {
                resetTrigger = newTrigger
                startNewChat()
            }
        }
    }

    // Image picker launcher
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        Log.d("DomeUIImageURI", "Image picker result received: uri=$uri")
        selectedImageUri = uri
        Log.d("DomeUIImageURI", "selectedImageUri set to: $selectedImageUri")
        if (uri != null) {
            selectedScanType = ScanInputType.IMAGE
            Log.d("DomeUIImageURI", "Scan type set to IMAGE, selectedImageUri=$selectedImageUri")
        }
    }

    // Camera launcher
    val cameraUri = remember { mutableStateOf<Uri?>(null) }
    val cameraLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture()
    ) { success ->
        Log.d("DomeUIImageURI", "Camera result received: success=$success, cameraUri=${cameraUri.value}")
        if (success && cameraUri.value != null) {
            selectedImageUri = cameraUri.value
            selectedScanType = ScanInputType.IMAGE
            Log.d("DomeUIImageURI", "Camera image set: selectedImageUri=$selectedImageUri")
        }
    }

    // Smart scroll logic: Only scroll to bottom when new messages arrive and user expects it
    LaunchedEffect(uiState.chatMessages.size, lazyListState.isScrollInProgress) {
        val currentChatMessages = uiState.chatMessages // Use UI state consistently
        Log.d("AutoScrollDebug", "=== AUTO-SCROLL EFFECT TRIGGERED ===")
        Log.d("AutoScrollDebug", "currentChatMessages.size: ${currentChatMessages.size}")
        Log.d("AutoScrollDebug", "previousMessageCount: $previousMessageCount")
        Log.d("AutoScrollDebug", "isScrollInProgress: ${lazyListState.isScrollInProgress}")

        if (currentChatMessages.isNotEmpty()) {
            // Check if this is a new message (size increased)
            val hasNewMessages = currentChatMessages.size > previousMessageCount
            Log.d("AutoScrollDebug", "hasNewMessages: $hasNewMessages")

            if (hasNewMessages) {
                // Determine if user is currently at or near the bottom
                val layoutInfo = lazyListState.layoutInfo
                val lastVisibleItem = layoutInfo.visibleItemsInfo.lastOrNull()
                val totalItemsInLazyColumn = layoutInfo.totalItemsCount
                val isCurrentlyAtBottom = lastVisibleItem?.let {
                    it.index >= totalItemsInLazyColumn - 2 // Within last 2 items of LazyColumn
                } ?: true

                Log.d("AutoScrollDebug", "wasUserAtBottom: $wasUserAtBottom")
                Log.d("AutoScrollDebug", "isCurrentlyAtBottom: $isCurrentlyAtBottom")
                Log.d("AutoScrollDebug", "lastVisibleIndex: ${lastVisibleItem?.index}")
                Log.d("AutoScrollDebug", "totalItemsInLazyColumn: $totalItemsInLazyColumn")

                // Smart auto-scroll: preserve existing behavior, fix scan results only
                Log.d("AutoScrollDebug", "✅ CONDITIONS MET - PERFORMING AUTO-SCROLL")
                coroutineScope.launch {
                    // Wait for any ongoing scroll to finish
                    if (lazyListState.isScrollInProgress) {
                        Log.d("AutoScrollDebug", "⏳ Waiting for ongoing scroll to finish...")
                        kotlinx.coroutines.delay(100)
                    }

                    val lastMessageIndex = currentChatMessages.size // Index after spacer

                    // Check if we have scan results in the chat (look for any message with scanResult)
                    val hasScanResults = currentChatMessages.any { it.scanResult != null }
                    val newMessage = currentChatMessages.lastOrNull()
                    val messageContent = newMessage?.content ?: "null"

                    // DEBUG: Log the actual message content to understand the flow
                    Log.d("AutoScrollDebug", "🔍 DEBUG: Message content (first 100 chars): ${messageContent.take(100)}")
                    Log.d("AutoScrollDebug", "🔍 DEBUG: Has scan results in chat? $hasScanResults")
                    Log.d("AutoScrollDebug", "🔍 DEBUG: New message has scanResult? ${newMessage?.scanResult != null}")

                    // If this is a post-scan message AND we have scan results, scroll to scan results
                    if (hasScanResults && messageContent.contains("session messages remaining")) {
                        Log.d("AutoScrollDebug", "📊 POST-SCAN MESSAGE detected - scrolling to scan results")
                        // Find the LATEST scan results message index (not the first one)
                        val scanResultsIndex = currentChatMessages.indexOfLast { it.scanResult != null }
                        if (scanResultsIndex >= 0) {
                            val targetIndex = scanResultsIndex + 1 // +1 for spacer
                            Log.d("AutoScrollDebug", "🎯 Scrolling to LATEST scan results at index: $targetIndex")
                            lazyListState.animateScrollToItem(targetIndex, scrollOffset = -200)
                        } else {
                            Log.d("AutoScrollDebug", "💬 Fallback to normal scroll")
                            lazyListState.animateScrollToItem(lastMessageIndex)
                        }
                    } else {
                        Log.d("AutoScrollDebug", "💬 Regular message - normal scroll")
                        // For all other messages: use normal scroll (preserves existing behavior)
                        lazyListState.animateScrollToItem(lastMessageIndex)
                    }
                }

                // Update tracking variables
                previousMessageCount = currentChatMessages.size
                wasUserAtBottom = isCurrentlyAtBottom
                Log.d("AutoScrollDebug", "Updated: previousMessageCount=$previousMessageCount, wasUserAtBottom=$wasUserAtBottom")
            } else {
                Log.d("AutoScrollDebug", "No new messages - skipping auto-scroll logic")
            }
        } else {
            Log.d("AutoScrollDebug", "Chat messages empty - skipping auto-scroll logic")
        }
    }

    // Efficient scroll position tracking using derivedStateOf
    val isAtBottom by remember {
        derivedStateOf {
            val currentChatMessages = uiState.chatMessages // Use UI state consistently
            if (currentChatMessages.isEmpty()) {
                Log.d("ScrollButtonDebug", "Chat messages empty - isAtBottom = true")
                true
            } else {
                val layoutInfo = lazyListState.layoutInfo
                val lastVisibleItem = layoutInfo.visibleItemsInfo.lastOrNull()
                val totalItemsInLazyColumn = layoutInfo.totalItemsCount

                // Check if we can see the last item in the LazyColumn (which should be the last chat message)
                val result = lastVisibleItem?.let {
                    it.index >= totalItemsInLazyColumn - 1 // At the very last item
                } ?: true

                Log.d("ScrollButtonDebug", "isAtBottom calculation: lastVisibleIndex=${lastVisibleItem?.index}, totalItemsInLazyColumn=$totalItemsInLazyColumn, chatMessages.size=${currentChatMessages.size}, isAtBottom=$result")
                result
            }
        }
    }

    // Track when user stops scrolling to show button
    LaunchedEffect(lazyListState.isScrollInProgress) {
        Log.d("ScrollButtonDebug", "Scroll progress changed: isScrollInProgress=${lazyListState.isScrollInProgress}")
        Log.d("ScrollButtonDebug", "Current state: isAtBottom=$isAtBottom, chatMessages.size=${chatMessages.size}, showScrollButton=$showScrollButton, isLoading=$isLoading")
        Log.d("ScrollButtonDebug", "Chat messages actual count: ${chatMessages.size}, UI state count: ${uiState.chatMessages.size}")

        if (!lazyListState.isScrollInProgress && !isAtBottom && uiState.chatMessages.isNotEmpty() && !isLoading) {
            // User stopped scrolling and is not at bottom - show button
            // Don't show button during loading (analyzing/processing)
            Log.d("ScrollButtonDebug", "CONDITIONS MET: Showing scroll button")
            scrollButtonJob?.cancel()
            showScrollButton = true
            Log.d("ScrollButtonDebug", "showScrollButton set to TRUE")
            scrollButtonJob = coroutineScope.launch {
                Log.d("ScrollButtonDebug", "Starting 4-second timer to hide button")
                delay(4000) // Hide after 4 seconds (compensate for fade animation)
                Log.d("ScrollButtonDebug", "4 seconds passed - hiding button")
                showScrollButton = false
            }
        } else if (isAtBottom || isLoading) {
            // User is at bottom - hide button immediately
            Log.d("ScrollButtonDebug", "At bottom - hiding button immediately")
            showScrollButton = false
            scrollButtonJob?.cancel()
        } else {
            Log.d("ScrollButtonDebug", "Conditions not met for showing button")
        }
    }

    // Update wasUserAtBottom for other scroll logic
    LaunchedEffect(isAtBottom) {
        wasUserAtBottom = isAtBottom
    }

    // Function to add a user message
    fun addUserMessage(content: String, imageUri: Uri? = null, inputType: ScanInputType = ScanInputType.TEXT) {
        // Use ViewModel to add user message (persists across tab switches)
        viewModel.addUserMessage(content, imageUri, inputType)

        // Note: We DON'T clear selectedImageUri here anymore
        // It will be cleared after the scan is successfully submitted
    }

    // Function to add an AI response (now handled by ViewModel)
    fun addAIResponse(scanResult: ScanResult) {
        isLoading = false
        viewModel.addScanResultMessage(scanResult)
    }

    // Function to handle scan
    fun performScan() {
        // If there are already user messages and we have text input but no image, treat as follow-up question
        if (chatMessages.any { it.isUser } && inputText.isNotBlank() && selectedImageUri == null) {
            Log.d("ChatScanScreenJetchat", "Treating as follow-up question: '$inputText'")

            // Add user message
            addUserMessage(inputText)

            // Store the input text in a local variable to ensure it's not lost
            val textToSend = inputText.trim()

            // Call ViewModel to submit the text as a follow-up question
            viewModel.onSendText(textToSend)

            // Clear the input text
            inputText = ""

            // Start loading
            isLoading = true

            // Refresh user plan after scan to update scan counts
            viewModel.refreshUserPlan()
            return
        }

        // Check if user has reached scan limit
        if (uiState.showScanLimitMessage) {
            coroutineScope.launch {
                snackbarHostState.showSnackbar("You have reached your scan limit for this month. Please upgrade your plan or wait until next month.")
            }
            return
        }

        // Check if expert mode is enabled but no expert scans remaining
        if (uiState.isExpertModeEnabled && uiState.expertScansRemaining <= 0) {
            coroutineScope.launch {
                snackbarHostState.showSnackbar("You have used all your expert scans for this month. Turning off Expert Mode.")
            }
            viewModel.toggleExpertMode() // Turn off expert mode
            return
        }

        // Handle combined text + image submission
        if (selectedImageUri != null && inputText.isNotBlank()) {
            Log.d("DomeUIImageURI", "Combined text + image submission: text='$inputText', uri=$selectedImageUri")

            // Add expert mode indicator to message if enabled
            val messagePrefix = if (uiState.isExpertModeEnabled) "[Expert Mode] " else ""
            addUserMessage(messagePrefix + inputText, selectedImageUri, ScanInputType.IMAGE)

            // Store both in local variables
            val imageUriToSend = selectedImageUri
            val textToSend = inputText.trim()

            Log.d("DomeUIImageURI", "About to call viewModel.onSendImage with URI: $imageUriToSend and text: '$textToSend'")

            try {
                // Validate the URI
                if (imageUriToSend == null) {
                    Log.d("DomeUIImageURI", "imageUriToSend is null, throwing exception")
                    throw Exception("Image URI is null")
                }

                // Call ViewModel to submit the image scan with text context
                viewModel.onSendImage(imageUriToSend, userContext = textToSend, context = context)

                // Clear both after successful submission
                selectedImageUri = null
                inputText = ""

            } catch (e: Exception) {
                Log.e("ChatScanScreenJetchat", "Error sending combined text+image to ViewModel", e)
                Log.e("DomeUIImageURI", "Exception in combined submission: ${e.message}")
                coroutineScope.launch {
                    snackbarHostState.showSnackbar("Error processing image: ${e.message}")
                }
                isLoading = false
                return
            }
        }
        // Handle image-only submission
        else if (selectedImageUri != null) {
            Log.d("DomeUIImageURI", "Image-only submission: uri=$selectedImageUri")

            // Add expert mode indicator to message if enabled
            val messagePrefix = if (uiState.isExpertModeEnabled) "[Expert Mode] " else ""
            addUserMessage(messagePrefix + "Image scan", selectedImageUri, ScanInputType.IMAGE)

            // Store the image URI in a local variable to ensure it's not lost
            val imageUriToSend = selectedImageUri
            Log.d("DomeUIImageURI", "imageUriToSend stored: $imageUriToSend")

            try {
                // Validate the URI
                if (imageUriToSend == null) {
                    Log.d("DomeUIImageURI", "imageUriToSend is null, throwing exception")
                    throw Exception("Image URI is null")
                }

                Log.d("DomeUIImageURI", "About to call viewModel.onSendImage with URI: $imageUriToSend")
                // Call ViewModel to submit the image scan
                viewModel.onSendImage(imageUriToSend, context = context)

                // Clear the selected image URI after successful submission
                selectedImageUri = null

            } catch (e: Exception) {
                Log.e("ChatScanScreenJetchat", "Error sending image to ViewModel", e)
                Log.e("DomeUIImageURI", "Exception in image-only submission: ${e.message}")
                coroutineScope.launch {
                    snackbarHostState.showSnackbar("Error processing image: ${e.message}")
                }
                isLoading = false
                return
            }
        }
        // Handle text/URL-only submission
        else if (inputText.isNotBlank()) {
            // Add expert mode indicator to message if enabled
            val messagePrefix = if (uiState.isExpertModeEnabled) "[Expert Mode] " else ""
            addUserMessage(messagePrefix + inputText, null, selectedScanType)

            // Store the input text in a local variable to ensure it's not lost
            val textToSend = inputText.trim()

            Log.d("ChatScanScreenJetchat", "Text-only submission: '$textToSend', type: $selectedScanType")

            // Call ViewModel to submit the scan
            if (selectedScanType == ScanInputType.URL) {
                viewModel.onSendUrl(textToSend)
            } else {
                viewModel.onSendText(textToSend)
            }

            // Clear the input text
            inputText = ""
        }
        // No input provided
        else {
            coroutineScope.launch {
                snackbarHostState.showSnackbar("Please enter some text or select an image to scan")
            }
            return
        }

        // Start loading
        isLoading = true

        // Refresh user plan after scan to update scan counts
        viewModel.refreshUserPlan()
    }

    // Function to handle follow-up question
    fun askFollowUpQuestion() {
        if (inputText.isBlank()) {
            coroutineScope.launch {
                snackbarHostState.showSnackbar("Please enter a question")
            }
            return
        }

        // Log the input text for debugging
        Log.d("ChatScanScreenJetchat", "Asking follow-up question: '$inputText'")

        // Add user message
        addUserMessage(inputText)

        // Start loading
        isLoading = true

        // Store the input text in a local variable to ensure it's not lost
        val textToSend = inputText.trim()

        // Log the input text for debugging
        Log.d("ChatScanScreenJetchat", "About to send follow-up question to ViewModel: '$textToSend'")

        // Call ViewModel to submit the text as a follow-up question
        viewModel.onSendText(textToSend)

        // Now it's safe to clear the input text
        inputText = ""
    }

    // Main UI structure with a completely different approach - no Scaffold
    Box(modifier = Modifier.fillMaxSize()) {
        // Main content column
        Column(modifier = Modifier.fillMaxSize()) {
            // Only show TopAppBar if this is a standalone screen, not when used as a tab
            if (isStandalone) {
                TopAppBar(
                    title = { Text("Dome AI") },
                    actions = {
                        // New Chat button
                        IconButton(onClick = { startNewChat() }) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = "New Chat"
                            )
                        }

                        // Profile button (placeholder)
                        IconButton(onClick = { /* Profile menu will be implemented later */ }) {
                            Icon(
                                imageVector = Icons.Default.Person,
                                contentDescription = "Profile"
                            )
                        }
                    }
                )
            }

            // Content area with messages
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
            ) {
                // Column for content inside the Box
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                ) {
                    // Get Premium Button - only visible when there are no user messages and user is on Basic tier
                    val showGetPremiumButton = chatMessages.all { !it.isUser } && uiState.activeSubscriptionTier == "basic"

                    // Show Expert Mode toggle - only visible for Expert tier users
                    val showExpertModeToggle = uiState.activeSubscriptionTier == "expert"

                    // Show subscription info
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp)
                    ) {
                        // Show current plan
                        Text(
                            text = "Current Plan: ${uiState.activeSubscriptionTier.replaceFirstChar { it.uppercase() }}",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.primary
                        )

                        // Show scans remaining for Basic and Expert tiers
                        if (uiState.activeSubscriptionTier == "basic" || uiState.activeSubscriptionTier == "expert") {
                            Text(
                                text = "Regular Scans Remaining: ${uiState.regularScansRemaining}",
                                style = MaterialTheme.typography.labelSmall
                            )
                        }

                        // Show expert scans remaining for Expert tier
                        if (uiState.activeSubscriptionTier == "expert") {
                            Text(
                                text = "Expert Scans Remaining: ${uiState.expertScansRemaining}",
                                style = MaterialTheme.typography.labelSmall
                            )
                        }

                        // Show Expert Mode toggle for Expert tier
                        if (showExpertModeToggle) {
                            androidx.compose.material3.Switch(
                                checked = uiState.isExpertModeEnabled,
                                onCheckedChange = { viewModel.toggleExpertMode() },
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                            Text(
                                text = if (uiState.isExpertModeEnabled) "Expert Mode: ON" else "Expert Mode: OFF",
                                style = MaterialTheme.typography.labelMedium,
                                color = if (uiState.isExpertModeEnabled)
                                    MaterialTheme.colorScheme.tertiary
                                else
                                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                            )
                        }
                    }

                    // Use AnimatedVisibility for smooth transitions
                    AnimatedVisibility(
                        visible = showGetPremiumButton,
                        enter = fadeIn() + expandVertically(),
                        exit = fadeOut() + shrinkVertically()
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            FilledTonalButton(
                                onClick = { /* Will be implemented later */ },
                                modifier = Modifier
                                    .width(IntrinsicSize.Min)
                                    .padding(horizontal = 16.dp)
                            ) {
                                Text(
                                    text = "Get Premium",
                                    style = MaterialTheme.typography.labelLarge,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }

                    // Messages list - Takes all available space
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .padding(horizontal = 16.dp),
                        state = lazyListState,
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        item {
                            Spacer(modifier = Modifier.height(8.dp))
                        }

                        items(chatMessages) { message ->
                            ChatMessageItem(
                                message = message,
                                onViewDetails = { scanResult ->
                                    if (scanResult != null) {
                                        onScanDetail(scanResult.id)
                                    }
                                },
                                onStartNewScan = { startNewChat() }
                            )
                        }

                        if (isLoading) {
                            item {
                                AITypingIndicator()
                            }
                        }

                        item {
                            Spacer(modifier = Modifier.height(8.dp))
                        }
                    }
                }

                // Auto-scroll to bottom button with fade animation
                androidx.compose.animation.AnimatedVisibility(
                    visible = showScrollButton,
                    enter = fadeIn(animationSpec = tween(300)),
                    exit = fadeOut(animationSpec = tween(300)),
                    modifier = Modifier.align(Alignment.BottomEnd)
                ) {
                    Log.d("ScrollButtonDebug", "RENDERING BUTTON: showScrollButton=$showScrollButton")
                    FloatingActionButton(
                        onClick = {
                            Log.d("ScrollButtonDebug", "Button clicked - scrolling to bottom")
                            coroutineScope.launch {
                                // Scroll to the last item in the LazyColumn, not just last chat message
                                val targetIndex = lazyListState.layoutInfo.totalItemsCount - 1
                                Log.d("ScrollButtonDebug", "Scrolling to index: $targetIndex (totalItems: ${lazyListState.layoutInfo.totalItemsCount})")
                                lazyListState.animateScrollToItem(targetIndex)
                            }
                            showScrollButton = false
                            scrollButtonJob?.cancel()
                        },
                        modifier = Modifier
                            .padding(bottom = 80.dp, end = 16.dp) // Above input bar, right corner
                            .size(48.dp),
                        containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f),
                        contentColor = MaterialTheme.colorScheme.onSurface
                    ) {
                        Icon(
                            imageVector = Icons.Default.KeyboardArrowDown,
                            contentDescription = "Scroll to bottom",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }

                // Snackbar host
                SnackbarHost(
                    hostState = snackbarHostState,
                    modifier = Modifier.align(Alignment.BottomCenter)
                )
            }

            // Input bar at the bottom with ZERO spacing
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .imePadding()
            ) {
                ChatInputBar(
                    text = inputText,
                    onTextChange = { inputText = it },
                    onSendMessage = {
                        // Always use performScan() for the first scan logic
                        // The distinction between first scan and follow-up is handled inside performScan()
                        performScan()
                    },
                    onAttachmentClick = {
                        showScanTypeSelector = !showScanTypeSelector
                    },
                    showScanTypeSelector = showScanTypeSelector,
                    onScanTypeSelected = { type ->
                        selectedScanType = type
                        showScanTypeSelector = false

                        // For image type, show the image source dialog
                        if (type == ScanInputType.IMAGE) {
                            showImageSourceDialog = true
                        }
                    },
                    selectedImageUri = selectedImageUri,
                    onImageRemove = { selectedImageUri = null },
                    focusManager = focusManager,
                    selectedScanType = selectedScanType,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }

    // Show image source dialog if needed
    if (showImageSourceDialog) {
        ImageSourceDialog(
            onDismiss = { showImageSourceDialog = false },
            onGallerySelected = {
                imagePickerLauncher.launch("image/*")
            },
            onCameraSelected = {
                // Create a temporary file for the camera image
                val tempFile = File(context.cacheDir, "camera_photo_${System.currentTimeMillis()}.jpg")
                tempFile.createNewFile()

                // Create a content URI using FileProvider
                val uri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    tempFile
                )

                // Store the URI and launch the camera
                cameraUri.value = uri
                cameraLauncher.launch(uri)
            }
        )
    }
}
