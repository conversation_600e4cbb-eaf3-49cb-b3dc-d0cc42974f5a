-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Subscription and scan limit fields
    subscription_tier VARCHAR(10) DEFAULT 'free' NOT NULL,
    monthly_scan_allowance INTEGER DEFAULT 5 NOT NULL,
    scans_this_month INTEGER DEFAULT 0 NOT NULL,
    scan_counter_reset_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    
    -- Expert scan fields
    expert_scan_allowance INTEGER DEFAULT 0 NOT NULL,
    expert_scans_this_month INTEGER DEFAULT 0 NOT NULL,
    
    -- Google Play subscription fields
    subscription_provider VARCHAR(20) DEFAULT NULL,
    provider_subscription_id VARCHAR(255) DEFAULT NULL UNIQUE,
    google_play_purchase_token TEXT DEFAULT NULL,
    subscription_product_id VARCHAR(50) DEFAULT NULL,
    subscription_expiry_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    auto_renew_status BOOLEAN DEFAULT NULL,
    is_trial_period BOOLEAN DEFAULT NULL,
    last_rtdn_received_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
);

-- Create scan_sessions table
CREATE TABLE IF NOT EXISTS scan_sessions (
    id UUID PRIMARY KEY,
    owner_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create scans table
CREATE TABLE IF NOT EXISTS scans (
    id SERIAL PRIMARY KEY,
    owner_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    scan_session_id UUID REFERENCES scan_sessions(id) ON DELETE SET NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    input_text TEXT DEFAULT NULL,
    input_url VARCHAR(2048) DEFAULT NULL,
    input_content_type VARCHAR(20) NOT NULL,
    user_provided_context TEXT DEFAULT NULL,
    raw_input_payload JSONB DEFAULT NULL,
    analysis_result JSONB DEFAULT NULL,
    error_message TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_subscription_tier ON users(subscription_tier);
CREATE INDEX IF NOT EXISTS idx_users_provider_subscription_id ON users(provider_subscription_id);
CREATE INDEX IF NOT EXISTS idx_scan_sessions_owner_id ON scan_sessions(owner_id);
CREATE INDEX IF NOT EXISTS idx_scans_owner_id ON scans(owner_id);
CREATE INDEX IF NOT EXISTS idx_scans_scan_session_id ON scans(scan_session_id);
CREATE INDEX IF NOT EXISTS idx_scans_status ON scans(status);
CREATE INDEX IF NOT EXISTS idx_scans_created_at ON scans(created_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to update updated_at timestamp
CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scans_updated_at
BEFORE UPDATE ON scans
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
