#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test individual images with the real Premium Tier AI service.
"""

import asyncio
import logging
import sys
import uuid
import os
import time
import re
import requests
import json
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User
from app.models.scan_session import ScanSession
from app.models.scan import Scan
from app.crud import crud_scan_session
from app.services.ai_service_factory import get_ai_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Google Drive image URLs
IMAGE_URLS = [
    "https://drive.google.com/file/d/1Rbdq60DXO8Fvvz9XHJnUWkEw29GFXzeC/view?usp=sharing",
    "https://drive.google.com/file/d/1-niQevQraY1PJzu9m79-LM-LwRGUUtxE/view?usp=sharing",
    "https://drive.google.com/file/d/1dQuE-aFYIHKHg6ZV6EoRTQw3QPXTKijX/view?usp=sharing",
    "https://drive.google.com/file/d/1fxdt5H_kfGHO2XaHQSMx7NfNFJoYMDtV/view?usp=sharing"
]

# User context for the first scan
USER_CONTEXT = """They almost got me. My intuition is almost on point. I was so close too. Holy Shit. My intuition is "almost" on point. I finally got one. So I'm on a "dating" app. I matched with a girl in my area. She seemed a little too good to be true. So I of course proceeded with caution. When she asked me personal details, I didn't tell her much. My big mistake was to add her on my personal instagram. Onto the good part. She started getting sexual immediately. I told her I don't send pics until I meet the person. This upset her because she had already sent pics over dm. So I sent her a couple fully clothed pics of me in the mirror. This upset her, she wanted a "limp dick" picture. Red flag, what girl wants to see it limp. 😂😂 After she asked for my number and I turned that down, she turned down my offer to video call. Here's a few of the chats. I warned the people on my Instagram about a potential fake dick pic incoming. Will update if this gets more interesting."""

def download_image_from_gdrive(url: str, output_path: str) -> str:
    """
    Download an image from Google Drive.

    Args:
        url: Google Drive URL
        output_path: Directory to save the image

    Returns:
        Path to the downloaded image
    """
    try:
        # Extract file ID from Google Drive URL
        file_id = re.search(r'\/d\/(.*?)\/view', url)
        if not file_id:
            logger.error(f"Could not extract file ID from URL: {url}")
            return None
        file_id = file_id.group(1)

        # Create direct download link
        download_url = f"https://drive.google.com/uc?export=download&id={file_id}"

        # Create output directory if it doesn't exist
        os.makedirs(output_path, exist_ok=True)

        # Download the file
        local_filename = os.path.join(output_path, f"image_{file_id}.jpg")

        # Download with requests
        response = requests.get(download_url, stream=True)
        if response.status_code != 200:
            logger.error(f"Failed to download image: {response.status_code}")
            # Create a placeholder file instead
            with open(local_filename, 'w') as f:
                f.write(f"Placeholder for {url}")
            return local_filename

        with open(local_filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        logger.info(f"Downloaded image to {local_filename}")
        return local_filename
    except Exception as e:
        logger.error(f"Error downloading image: {str(e)}")
        # Create a placeholder file
        local_filename = os.path.join(output_path, f"placeholder_{int(time.time())}.jpg")
        with open(local_filename, 'w') as f:
            f.write(f"Placeholder for {url}")
        return local_filename

async def process_image_with_real_ai(
    db: Session,
    image_path: str,
    user_context: Optional[str] = None,
    image_index: int = 0
) -> Dict[str, Any]:
    """
    Process an image with the real Premium Tier AI service.

    Args:
        db: Database session
        image_path: Path to the image file
        user_context: Optional user-provided context
        image_index: Index of the image (for logging)

    Returns:
        The analysis result
    """
    logger.info(f"Processing Image {image_index + 1} with real Premium Tier AI service")

    try:
        # Create a premium tier test user
        unique_email = f"test_premium_user_{int(time.time())}@example.com"
        test_user = User(
            email=unique_email,
            hashed_password="hashed_password",
            subscription_tier="premium",
            monthly_scan_allowance=100,
            scans_this_month=0,
            scan_counter_reset_at=datetime.now(timezone.utc)
        )
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        logger.info(f"Created premium test user with ID {test_user.id}")

        # Create a scan session
        session = crud_scan_session.create_scan_session(
            db=db, owner_id=test_user.id, title=f"Test Session for Image {image_index + 1}"
        )
        logger.info(f"Created scan session with ID {session.id}")

        # Create a scan
        scan = Scan(
            owner_id=test_user.id,
            status="pending",
            input_content_type="image_path",
            user_provided_context=user_context,
            raw_input_payload={"file_path": image_path},
            scan_session_id=session.id
        )
        db.add(scan)
        db.commit()
        db.refresh(scan)
        logger.info(f"Created scan with ID {scan.id} for Image {image_index + 1}")

        # Get the AI service
        ai_service = get_ai_service(user_tier="premium")

        # Update scan status to "processing"
        scan.status = "processing"
        db.add(scan)
        db.commit()
        db.refresh(scan)

        # Get multimodal analysis
        logger.info(f"Getting multimodal analysis for image at {image_path}")
        multimodal_result = await ai_service.get_multimodal_analysis(
            image_path=image_path
        )

        # Extract text and get embedding
        extracted_text = multimodal_result.get("extracted_text", "")
        logger.info(f"Extracted text: {extracted_text[:100]}...")

        # Get embedding
        logger.info(f"Getting text embedding")
        embedding = await ai_service.get_text_embedding(text=extracted_text)
        logger.info(f"Embedding dimensions: {len(embedding)}")

        # Perform RAG analysis
        logger.info(f"Performing RAG analysis")

        # First, check if we can retrieve knowledge base chunks
        from app.crud import crud_kb
        logger.info(f"Testing knowledge base retrieval")
        similar_chunks = crud_kb.find_similar_kb_chunks(
            db=db,
            query_embedding=embedding,
            top_k=3
        )

        logger.info(f"Found {len(similar_chunks)} similar knowledge base chunks")
        for i, chunk in enumerate(similar_chunks):
            logger.info(f"Similar chunk {i+1}: ID={chunk.id}, Source={chunk.source}")
            if hasattr(chunk, 'similarity_score'):
                logger.info(f"Similarity score: {chunk.similarity_score}")

        # Now perform the RAG analysis
        rag_result = await ai_service.perform_scam_analysis_with_rag(
            query_text=extracted_text,
            query_embedding=embedding,
            original_image_description=multimodal_result.get("image_description", ""),
            original_platform_identified=multimodal_result.get("platform_identified", ""),
            db=db
        )

        # Convert ScanResultData to dict
        rag_result_dict = {
            "risk_score": rag_result.risk_score,
            "explanation": rag_result.explanation,
            "detected_red_flags": rag_result.detected_red_flags,
            "recommendations": rag_result.recommendations,
            "confidence_level": rag_result.confidence_level,
            "model_used": rag_result.model_used
        }

        # Combine results
        analysis_result = {
            **multimodal_result,
            **rag_result_dict
        }

        # Update scan with result
        scan.status = "completed"
        scan.analysis_result = analysis_result
        db.add(scan)
        db.commit()
        db.refresh(scan)

        logger.info(f"Completed processing of scan {scan.id} for Image {image_index + 1}")

        # Print detailed analysis results
        logger.info(f"\n=== DETAILED ANALYSIS FOR IMAGE {image_index + 1} ===")
        logger.info(f"Scan ID: {scan.id}")
        logger.info(f"Risk Score: {rag_result.risk_score}")
        logger.info(f"Explanation: {rag_result.explanation}")
        logger.info(f"Detected Red Flags: {rag_result.detected_red_flags}")
        logger.info(f"Recommendations: {rag_result.recommendations}")
        logger.info(f"Confidence Level: {rag_result.confidence_level}")
        logger.info(f"Model Used: {rag_result.model_used}")

        # Clean up
        db.delete(scan)
        db.delete(session)
        db.delete(test_user)
        db.commit()

        return analysis_result

    except Exception as e:
        logger.error(f"Error processing Image {image_index + 1}: {str(e)}")
        return None

async def test_individual_images_with_real_ai():
    """Test individual images with the real Premium Tier AI service."""
    logger.info("Testing Individual Images with Real Premium Tier AI")

    # Create a database session
    db = SessionLocal()
    try:
        # Download the test images
        image_paths = []
        for i, url in enumerate(IMAGE_URLS):
            image_path = download_image_from_gdrive(url, "temp_images")
            image_paths.append(image_path)

        # Process each image individually
        for i, image_path in enumerate(image_paths):
            # Use user context only for the first image
            context = USER_CONTEXT if i == 0 else None

            # Process the image
            await process_image_with_real_ai(db, image_path, context, i)

            # Wait a bit between images to avoid rate limiting
            if i < len(image_paths) - 1:
                logger.info(f"Waiting 5 seconds before processing next image...")
                await asyncio.sleep(5)

        # Clean up downloaded images
        for image_path in image_paths:
            if os.path.exists(image_path):
                os.remove(image_path)
                logger.info(f"Deleted temporary image: {image_path}")

    finally:
        db.close()

def main():
    """Run the test."""
    asyncio.run(test_individual_images_with_real_ai())

if __name__ == "__main__":
    main()
