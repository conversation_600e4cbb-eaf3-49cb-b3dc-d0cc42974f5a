-- Create the database user with the correct password if it doesn't exist
DO
$$
BEGIN
   IF NOT EXISTS (
      SELECT FROM pg_catalog.pg_roles
      WHERE  rolname = 'dome_api_user') THEN
      CREATE USER dome_api_user WITH PASSWORD 'Dome2025ApiSecure!';
   ELSE
      ALTER USER dome_api_user WITH PASSWORD 'Dome2025ApiSecure!';
   END IF;
END
$$;

-- Grant privileges
ALTER USER dome_api_user WITH SUPERUSER;
GRANT ALL PRIVILEGES ON DATABASE dome_app_main_db TO dome_api_user;
