"""
Create a test user for RTDN testing.

This script creates a test user in the database for RTDN testing.
"""
import sys
import os
import logging
from datetime import datetime, timezone, timedelta
import json

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    from app.models.user import User
    from app.core.config import settings
    from app.core.security import get_password_hash
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure you're running this script from the correct directory.")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Test data
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "password123"
TEST_EXTERNAL_ACCOUNT_ID = "user_123456"  # This should match the externalAccountId in the mock Google data


def create_test_user():
    """
    Create a test user for RTDN testing.
    """
    try:
        # Create database connection
        engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # Check if the user already exists
        existing_user = db.query(User).filter(User.email == TEST_EMAIL).first()
        if existing_user:
            logger.info(f"User {TEST_EMAIL} already exists. Updating to initial state.")
            user = existing_user
        else:
            logger.info(f"Creating new user {TEST_EMAIL}")
            user = User(
                email=TEST_EMAIL,
                hashed_password=get_password_hash(TEST_PASSWORD),
                is_active=True
            )
            db.add(user)
            db.commit()
            db.refresh(user)
        
        # Set initial state (free tier)
        user.subscription_provider = "google_play"
        user.provider_subscription_id = None
        user.google_play_purchase_token = None
        user.subscription_product_id = None
        user.subscription_tier = "free"
        user.subscription_expiry_date = None
        user.auto_renew_status = False
        user.is_trial_period = False
        user.monthly_scan_allowance = 5
        user.expert_scan_allowance = 0
        user.scans_this_month = 0
        user.expert_scans_this_month = 0
        user.scan_counter_reset_at = datetime.now(timezone.utc)
        user.last_rtdn_received_at = None
        user.external_account_id = TEST_EXTERNAL_ACCOUNT_ID
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        logger.info(f"User {TEST_EMAIL} created/updated with initial state.")
        logger.info(f"User ID: {user.id}")
        logger.info(f"External Account ID: {user.external_account_id}")
        logger.info(f"Subscription Tier: {user.subscription_tier}")
        logger.info(f"Monthly Scan Allowance: {user.monthly_scan_allowance}")
        logger.info(f"Expert Scan Allowance: {user.expert_scan_allowance}")
        
        return user.id
        
    except Exception as e:
        logger.error(f"Error creating test user: {str(e)}")
        return None


def get_user_state(user_id):
    """
    Get the current state of a user.
    
    Args:
        user_id: The ID of the user to get the state for.
        
    Returns:
        A dictionary containing the user's state.
    """
    try:
        # Create database connection
        engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # Get the user
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            logger.error(f"User with ID {user_id} not found.")
            return None
        
        # Create a dictionary with the user's state
        user_state = {
            "id": user.id,
            "email": user.email,
            "subscription_provider": user.subscription_provider,
            "provider_subscription_id": user.provider_subscription_id,
            "google_play_purchase_token": user.google_play_purchase_token,
            "subscription_product_id": user.subscription_product_id,
            "subscription_tier": user.subscription_tier,
            "subscription_expiry_date": str(user.subscription_expiry_date) if user.subscription_expiry_date else None,
            "auto_renew_status": user.auto_renew_status,
            "is_trial_period": user.is_trial_period,
            "monthly_scan_allowance": user.monthly_scan_allowance,
            "expert_scan_allowance": user.expert_scan_allowance,
            "scans_this_month": user.scans_this_month,
            "expert_scans_this_month": user.expert_scans_this_month,
            "scan_counter_reset_at": str(user.scan_counter_reset_at) if user.scan_counter_reset_at else None,
            "last_rtdn_received_at": str(user.last_rtdn_received_at) if user.last_rtdn_received_at else None,
            "external_account_id": user.external_account_id
        }
        
        return user_state
        
    except Exception as e:
        logger.error(f"Error getting user state: {str(e)}")
        return None


if __name__ == "__main__":
    user_id = create_test_user()
    if user_id:
        user_state = get_user_state(user_id)
        if user_state:
            logger.info(f"User state: {json.dumps(user_state, indent=2)}")
        else:
            logger.error("Failed to get user state.")
