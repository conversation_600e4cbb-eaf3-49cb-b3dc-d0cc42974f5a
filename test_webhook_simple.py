"""
Simple test script for Google Play RTDN webhook.
"""
import base64
import json
import requests
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Webhook URL
webhook_url = "http://localhost:8000/api/v1/webhooks/googleplay/rtdn"

# Create a test notification
developer_notification = {
    "version": "1.0",
    "packageName": "com.domeai.scamdetector",
    "eventTimeMillis": "1621234567890",
    "testNotification": {
        "version": "1.0"
    }
}

# Encode the notification as base64
encoded_data = base64.b64encode(json.dumps(developer_notification).encode("utf-8")).decode("utf-8")

# Create the Pub/Sub message
pubsub_message = {
    "message": {
        "data": encoded_data,
        "messageId": "test-message-id",
        "publishTime": "2023-05-20T10:00:00.000Z"
    },
    "subscription": "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
}

# Print the decoded notification
logger.info(f"Decoded notification: {json.dumps(developer_notification, indent=2)}")

# Send the request
logger.info(f"Sending test notification to {webhook_url}")
try:
    response = requests.post(
        webhook_url,
        json=pubsub_message,
        headers={"Content-Type": "application/json"}
    )
    logger.info(f"Status code: {response.status_code}")
    logger.info(f"Response: {response.text}")
except Exception as e:
    logger.error(f"Error sending notification: {str(e)}")
