#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update specific user to premium tier for Hostinger Horizons contest.
This script <NAME_EMAIL> to premium tier.

SAFETY: This script only updates one specific user and preserves all existing data.
"""

import os
import sys
from datetime import datetime, timezone

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import SessionLocal
from app.models.user import User
from sqlalchemy import text

def update_user_to_premium():
    """Update specific user to premium tier safely using SQLAlchemy."""

    # Target user email
    TARGET_EMAIL = "<EMAIL>"

    try:
        # Create database session
        print(f"Connecting to database...")
        db = SessionLocal()

        # First, check if user exists
        print(f"Checking if user {TARGET_EMAIL} exists...")
        user = db.query(User).filter(User.email == TARGET_EMAIL).first()

        if not user:
            print(f"❌ ERROR: User {TARGET_EMAIL} not found in database!")
            return False

        print(f"✅ Found user: ID={user.id}, Email={user.email}")
        print(f"   Current tier: {user.subscription_tier}")
        print(f"   Current allowance: {user.monthly_scan_allowance}")
        print(f"   Current scans this month: {user.scans_this_month}")

        # Update user to premium tier
        print(f"\n🔄 Updating user to premium tier...")

        user.subscription_tier = "premium"
        user.monthly_scan_allowance = 100
        user.scans_this_month = 0
        user.expert_scan_allowance = 0
        user.expert_scans_this_month = 0
        user.updated_at = datetime.now(timezone.utc)

        # Commit the changes
        db.add(user)
        db.commit()
        db.refresh(user)

        print(f"✅ User successfully updated!")
        print(f"   New tier: {user.subscription_tier}")
        print(f"   New monthly allowance: {user.monthly_scan_allowance}")
        print(f"   Scans this month: {user.scans_this_month}")
        print(f"   Expert allowance: {user.expert_scan_allowance}")
        print(f"✅ Changes committed to database!")

        return True

    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        if 'db' in locals():
            db.rollback()
        return False
    finally:
        if 'db' in locals():
            db.close()
        print("🔒 Database connection closed.")

if __name__ == "__main__":
    print("=== DomeAI User Premium Upgrade Script ===")
    print("Target: <EMAIL>")
    print("Action: Upgrade to premium tier (100 scans/month)")
    print("=" * 50)
    
    success = update_user_to_premium()
    
    if success:
        print("\n🎉 SUCCESS: User successfully upgraded to premium tier!")
        print("The user can now enjoy premium features for the Hostinger Horizons contest.")
    else:
        print("\n❌ FAILED: User upgrade was not successful.")
        print("Please check the error messages above.")
