package com.domeai.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import com.domeai.util.DeviceUtils
import android.widget.ImageView
import androidx.core.app.NotificationCompat
import com.domeai.R
import com.domeai.presentation.main.MainActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * A more robust implementation of a floating view service
 */
class FloatingViewService : Service() {

    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var dismissAreaView: View? = null
    private var layoutParams: WindowManager.LayoutParams? = null
    private var dismissAreaParams: WindowManager.LayoutParams? = null
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private val mainHandler = Handler(Looper.getMainLooper())

    // Flag to track if we're currently dragging the button
    private var isDragging = false

    // Flag to track if the view is already added to window manager
    private var isViewAttached = false
    private var isDismissAreaShowing = false

    private var initialX: Int = 0
    private var initialY: Int = 0
    private var initialTouchX: Float = 0f
    private var initialTouchY: Float = 0f

    // For tracking dismiss area
    private var showDismissArea = false
    private var inDismissArea = false

    // Screen dimensions
    private var screenWidth: Int = 0
    private var screenHeight: Int = 0

    // For Xiaomi devices, we need to use a more aggressive approach
    private val isXiaomiDevice by lazy {
        try {
            DeviceUtils.isXiaomiDevice()
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error checking device type: ${e.message}")
            false
        }
    }

    companion object {
        private const val NOTIFICATION_CHANNEL_ID = "floating_view_service_channel"
        private const val NOTIFICATION_ID = 1001

        // Intent actions
        const val ACTION_START = "com.domeai.service.ACTION_START_FLOATING"
        const val ACTION_STOP = "com.domeai.service.ACTION_STOP_FLOATING"

        // Intent to start the service
        fun getStartIntent(context: Context): Intent {
            return Intent(context, FloatingViewService::class.java).apply {
                action = ACTION_START
            }
        }

        // Intent to stop the service
        fun getStopIntent(context: Context): Intent {
            return Intent(context, FloatingViewService::class.java).apply {
                action = ACTION_STOP
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        Log.d("FloatingViewService", "Service created")
        createNotificationChannel()
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d("FloatingViewService", "onStartCommand: ${intent?.action}")

        when (intent?.action) {
            ACTION_START -> {
                startFloatingView()
                // Schedule a delayed check to make sure the view is still visible
                serviceScope.launch {
                    delay(2000)
                    if (floatingView?.parent == null) {
                        Log.d("FloatingViewService", "View not attached, trying again")
                        startFloatingView()
                    }
                }
            }
            ACTION_STOP -> stopFloatingView()
            else -> {
                // Default to starting the floating view
                startFloatingView()
            }
        }

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        super.onDestroy()
        stopFloatingView()
        serviceScope.cancel()
        Log.d("FloatingViewService", "Service destroyed")
    }

    private fun startFloatingView() {
        try {
            // Start as foreground service first to avoid ANR
            startForeground(NOTIFICATION_ID, createNotification())
            Log.d("FloatingViewService", "Started as foreground service")

            // Check for overlay permission
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
                Log.e("FloatingViewService", "No overlay permission")
                stopSelf()
                return
            }

            // CRITICAL: Stop any monitoring first to prevent race conditions
            stopAllMonitoring()

            // If view is already attached, don't create a new one
            if (isViewAttached && floatingView != null) {
                Log.d("FloatingViewService", "View already attached, not creating a new one")
                return
            }

            // Remove any existing view first - this is critical to prevent duplicates
            removeExistingView()

            // Reset state completely
            floatingView = null
            layoutParams = null
            isViewAttached = false

            // Create the floating view
            createFloatingView()

            // Add the view to the window manager
            addViewToWindowManager()

            // Start monitoring to ensure the view stays visible
            startViewMonitoring()

        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error starting floating view: ${e.message}")
            e.printStackTrace()

            // Try again after a short delay
            mainHandler.postDelayed({ startFloatingView() }, 500)
        }
    }

    private fun stopAllMonitoring() {
        try {
            // Cancel all pending handlers
            mainHandler.removeCallbacksAndMessages(null)

            // Cancel all coroutines
            serviceScope.cancel()

            // Create a new scope
            val newScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

            Log.d("FloatingViewService", "All monitoring stopped")
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error stopping monitoring: ${e.message}")
        }
    }

    private fun createFloatingView() {
        try {
            // Inflate the floating view layout
            val inflater = LayoutInflater.from(this)
            floatingView = inflater.inflate(R.layout.floating_view, null)

            // Get screen dimensions
            val displayMetrics = resources.displayMetrics
            screenWidth = displayMetrics.widthPixels
            screenHeight = displayMetrics.heightPixels

            // Configure the layout parameters
            layoutParams = WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                },
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT
            ).apply {
                gravity = Gravity.TOP or Gravity.END
                x = 0  // With END gravity, x=0 means right edge
                y = 100  // Position near the top
            }

            // Set up touch listener for dragging
            floatingView?.setOnTouchListener { _, event ->
                handleTouchEvent(event)
            }

            // Also create the dismiss area view (but don't add it yet)
            createDismissAreaView()

            Log.d("FloatingViewService", "Floating view created")
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error creating floating view: ${e.message}")
            throw e
        }
    }

    private fun createDismissAreaView() {
        try {
            // Inflate the dismiss area layout
            val inflater = LayoutInflater.from(this)
            dismissAreaView = inflater.inflate(R.layout.dismiss_area, null)

            // Configure the layout parameters
            dismissAreaParams = WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                },
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                PixelFormat.TRANSLUCENT
            ).apply {
                gravity = Gravity.BOTTOM
            }

            Log.d("FloatingViewService", "Dismiss area view created")
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error creating dismiss area view: ${e.message}")
        }
    }

    private fun handleTouchEvent(event: MotionEvent): Boolean {
        val params = layoutParams ?: return false

        return when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // Start dragging
                isDragging = true
                initialX = params.x
                initialY = params.y
                initialTouchX = event.rawX
                initialTouchY = event.rawY

                // Don't show dismiss area yet - we'll show it only when near bottom
                showDismissArea = false
                inDismissArea = false

                true
            }
            MotionEvent.ACTION_MOVE -> {
                if (isDragging) {
                    try {
                        // Calculate new position
                        params.x = initialX + (event.rawX - initialTouchX).toInt()
                        params.y = initialY + (event.rawY - initialTouchY).toInt()

                        // Update the view position
                        if (isViewAttached) {
                            windowManager?.updateViewLayout(floatingView, params)
                        }

                        // Check if we're near the bottom of the screen
                        val dismissThreshold = screenHeight - 300
                        val isNearBottom = event.rawY > dismissThreshold

                        // Show or hide dismiss area based on position
                        if (isNearBottom && !showDismissArea) {
                            showDismissArea = true
                            showDismissArea()
                            Log.d("FloatingViewService", "Near bottom, showing dismiss area")
                        } else if (!isNearBottom && showDismissArea) {
                            showDismissArea = false
                            hideDismissArea()
                            Log.d("FloatingViewService", "Not near bottom, hiding dismiss area")
                        }

                        // If dismiss area is showing, check if we're in it
                        if (showDismissArea) {
                            checkIfInDismissArea(event.rawY)
                        }
                    } catch (e: Exception) {
                        Log.e("FloatingViewService", "Error moving view: ${e.message}")
                    }
                }
                true
            }
            MotionEvent.ACTION_UP -> {
                if (isDragging) {
                    isDragging = false

                    // Hide the dismiss area
                    hideDismissArea()

                    // Check if we should dismiss the button
                    if (inDismissArea) {
                        inDismissArea = false
                        // Hide the button temporarily
                        temporarilyHideButton()
                        return true
                    }

                    // Check if it was moved or just clicked
                    val moved = Math.abs(event.rawX - initialTouchX) > 10 ||
                            Math.abs(event.rawY - initialTouchY) > 10

                    if (moved) {
                        // Snap to edge with animation
                        snapToEdge(params)
                    } else {
                        // Handle click - open the app
                        val intent = Intent(this@FloatingViewService, MainActivity::class.java).apply {
                            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        }
                        startActivity(intent)
                    }
                }
                true
            }
            else -> false
        }
    }

    private fun showDismissArea() {
        try {
            if (!isDismissAreaShowing && dismissAreaView != null && dismissAreaParams != null) {
                windowManager?.addView(dismissAreaView, dismissAreaParams)
                isDismissAreaShowing = true
                Log.d("FloatingViewService", "Dismiss area shown")
            }
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error showing dismiss area: ${e.message}")
        }
    }

    private fun hideDismissArea() {
        try {
            if (isDismissAreaShowing && dismissAreaView != null) {
                windowManager?.removeView(dismissAreaView)
                isDismissAreaShowing = false
                Log.d("FloatingViewService", "Dismiss area hidden")
            }
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error hiding dismiss area: ${e.message}")
        }
    }

    private fun checkIfInDismissArea(rawY: Float) {
        try {
            // Check if the button is in the bottom area of the screen
            val dismissThreshold = screenHeight - 200
            val wasInDismissArea = inDismissArea
            inDismissArea = rawY > dismissThreshold

            // Only update UI if the state changed
            if (inDismissArea != wasInDismissArea) {
                Log.d("FloatingViewService", "Dismiss area state changed: $inDismissArea")

                // Highlight the dismiss area if we're in it
                if (inDismissArea) {
                    dismissAreaView?.findViewById<ImageView>(R.id.dismiss_icon)?.setColorFilter(
                        android.graphics.Color.RED, android.graphics.PorterDuff.Mode.SRC_IN
                    )

                    // Provide haptic feedback
                    try {
                        val vibrator = getSystemService(Context.VIBRATOR_SERVICE) as android.os.Vibrator
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            vibrator.vibrate(android.os.VibrationEffect.createOneShot(50, android.os.VibrationEffect.DEFAULT_AMPLITUDE))
                        } else {
                            @Suppress("DEPRECATION")
                            vibrator.vibrate(50)
                        }
                    } catch (e: Exception) {
                        Log.e("FloatingViewService", "Error providing haptic feedback: ${e.message}")
                    }
                } else {
                    dismissAreaView?.findViewById<ImageView>(R.id.dismiss_icon)?.clearColorFilter()
                }
            }
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error checking dismiss area: ${e.message}")
        }
    }

    private fun temporarilyHideButton() {
        try {
            Log.d("FloatingViewService", "Attempting to temporarily hide button")

            // First, make sure the dismiss area is hidden
            hideDismissArea()

            // Remove the button from the screen
            if (isViewAttached) {
                try {
                    windowManager?.removeView(floatingView)
                    isViewAttached = false
                    Log.d("FloatingViewService", "Button successfully removed from window")
                } catch (e: Exception) {
                    Log.e("FloatingViewService", "Error removing view: ${e.message}")
                }
            }

            // Make sure our state is consistent
            isViewAttached = false

            // Show a toast to inform the user
            mainHandler.post {
                android.widget.Toast.makeText(
                    this,
                    "Button hidden for 5 seconds",
                    android.widget.Toast.LENGTH_SHORT
                ).show()
            }

            Log.d("FloatingViewService", "Scheduling button to reappear in 5 seconds")

            // Schedule to show it again after 5 seconds
            mainHandler.postDelayed({
                try {
                    Log.d("FloatingViewService", "5 seconds elapsed, showing button again")

                    // Make sure we don't have any existing views
                    try {
                        windowManager?.removeView(floatingView)
                    } catch (e: Exception) {
                        // Ignore, view might not be attached
                    }

                    // Reset state
                    isViewAttached = false

                    // Add the view back
                    if (floatingView != null && layoutParams != null) {
                        windowManager?.addView(floatingView, layoutParams)
                        isViewAttached = true
                        Log.d("FloatingViewService", "Button shown again after temporary hide")
                    } else {
                        // If view was somehow lost, recreate it
                        Log.d("FloatingViewService", "View was lost, recreating")
                        createFloatingView()
                        addViewToWindowManager()
                    }
                } catch (e: Exception) {
                    Log.e("FloatingViewService", "Error showing button again: ${e.message}")
                    // Try to recover
                    startFloatingView()
                }
            }, 5000)
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error temporarily hiding button: ${e.message}")
            // Try to recover
            startFloatingView()
        }
    }

    private fun addViewToWindowManager() {
        try {
            if (floatingView == null || layoutParams == null) {
                Log.e("FloatingViewService", "Cannot add null view or params")
                return
            }

            // Only add if not already attached
            if (!isViewAttached) {
                windowManager?.addView(floatingView, layoutParams)
                isViewAttached = true
                Log.d("FloatingViewService", "View added to window manager")
            }
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error adding view: ${e.message}")
            isViewAttached = false
            throw e
        }
    }

    private fun removeExistingView() {
        try {
            if (floatingView != null && isViewAttached) {
                windowManager?.removeView(floatingView)
                isViewAttached = false
                Log.d("FloatingViewService", "Existing view removed")
            }
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error removing existing view: ${e.message}")
            // Reset state even if there was an error
            isViewAttached = false
        }
    }

    private fun startViewMonitoring() {
        // For Xiaomi devices, use a more aggressive approach
        if (isXiaomiDevice) {
            Log.d("FloatingViewService", "Xiaomi device detected, using aggressive monitoring")

            // Use a handler for immediate checking
            mainHandler.postDelayed({ ensureViewIsVisible() }, 100)

            // Schedule periodic checks
            scheduleViewCheck()
        }

        // For all devices, use a coroutine for periodic checking
        serviceScope.launch {
            while (true) {
                delay(2000) // Check every 2 seconds
                ensureViewIsVisible()
            }
        }
    }

    private fun ensureViewIsVisible() {
        try {
            if (floatingView == null) {
                Log.d("FloatingViewService", "View is null, creating new one")
                createFloatingView()
                addViewToWindowManager()
                return
            }

            // Check if view is attached to window
            val viewParent = floatingView?.parent
            if (viewParent == null) {
                Log.d("FloatingViewService", "View not attached, re-adding")
                isViewAttached = false
                addViewToWindowManager()
            }
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error ensuring view visibility: ${e.message}")
        }
    }

    private fun ensureViewIsAttached(params: WindowManager.LayoutParams) {
        try {
            // Check if the view is already attached
            val isAttached = floatingView?.parent != null

            if (!isAttached) {
                Log.d("FloatingViewService", "View detached, re-adding")
                try {
                    // First, make sure we don't have any existing views
                    try {
                        windowManager?.removeView(floatingView)
                    } catch (e: Exception) {
                        // Ignore, view might not be attached
                    }

                    // Then add the view
                    windowManager?.addView(floatingView, params)
                    Log.d("FloatingViewService", "View re-added successfully")
                } catch (e: Exception) {
                    Log.e("FloatingViewService", "Error re-adding view: ${e.message}")

                    // If we can't re-add the view, try recreating it
                    if (e.message?.contains("already added") == false) {
                        Log.d("FloatingViewService", "Recreating view")
                        stopFloatingView()
                        mainHandler.postDelayed({ startFloatingView() }, 500)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error in ensureViewIsAttached: ${e.message}")
        }
    }

    private fun scheduleViewCheck() {
        // Check every second on Xiaomi devices
        mainHandler.postDelayed(object : Runnable {
            override fun run() {
                try {
                    // Check if the view is still visible
                    if (!isViewAttached || floatingView?.parent == null) {
                        Log.d("FloatingViewService", "Handler: View not attached, ensuring visibility")
                        ensureViewIsVisible()
                    }
                } catch (e: Exception) {
                    Log.e("FloatingViewService", "Handler: Error checking view: ${e.message}")
                }

                // Schedule the next check
                mainHandler.postDelayed(this, 1000)
            }
        }, 1000)
    }

    private fun snapToEdge(params: WindowManager.LayoutParams) {
        try {
            if (!isViewAttached) {
                Log.d("FloatingViewService", "View not attached, can't snap to edge")
                return
            }

            // Get screen dimensions
            val displayMetrics = resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val screenHeight = displayMetrics.heightPixels

            // Get button dimensions
            val buttonWidth = floatingView?.width ?: 100
            val buttonHeight = floatingView?.height ?: 100

            // Calculate distances to edges
            val distanceToLeft = params.x
            val distanceToRight = screenWidth - (params.x + buttonWidth)
            val distanceToTop = params.y
            val distanceToBottom = screenHeight - (params.y + buttonHeight)

            // Find the closest edge
            val minHorizontalDistance = Math.min(distanceToLeft, distanceToRight)
            val minVerticalDistance = Math.min(distanceToTop, distanceToBottom)

            // Target coordinates
            val targetX: Int
            val targetY: Int

            // Determine target position based on closest edge
            if (minHorizontalDistance < minVerticalDistance) {
                // Snap horizontally
                if (distanceToLeft < distanceToRight) {
                    // Snap to left
                    targetX = 0
                    targetY = params.y
                } else {
                    // Snap to right
                    targetX = screenWidth - buttonWidth
                    targetY = params.y
                }
            } else {
                // Snap vertically
                if (distanceToTop < distanceToBottom) {
                    // Snap to top
                    targetX = params.x
                    targetY = 0
                } else {
                    // Snap to bottom
                    targetX = params.x
                    targetY = screenHeight - buttonHeight
                }
            }

            // Animate the snapping
            animateViewToPosition(params, targetX, targetY)

            Log.d("FloatingViewService", "Snapping to edge: x=$targetX, y=$targetY")
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error snapping to edge: ${e.message}")
        }
    }

    private fun animateViewToPosition(params: WindowManager.LayoutParams, targetX: Int, targetY: Int) {
        // Store original position
        val startX = params.x
        val startY = params.y

        // Calculate distance
        val distanceX = targetX - startX
        val distanceY = targetY - startY

        // Animation duration in milliseconds
        val duration = 200L
        val steps = 10
        val delay = duration / steps

        // Animate in steps
        for (i in 1..steps) {
            val progress = i.toFloat() / steps
            val x = startX + (distanceX * progress).toInt()
            val y = startY + (distanceY * progress).toInt()

            mainHandler.postDelayed({
                try {
                    if (isViewAttached) {
                        params.x = x
                        params.y = y
                        windowManager?.updateViewLayout(floatingView, params)
                    }
                } catch (e: Exception) {
                    Log.e("FloatingViewService", "Error during animation: ${e.message}")
                }
            }, (delay * i))
        }

        // Ensure final position is set
        mainHandler.postDelayed({
            try {
                if (isViewAttached) {
                    params.x = targetX
                    params.y = targetY
                    windowManager?.updateViewLayout(floatingView, params)
                    Log.d("FloatingViewService", "Animation complete, snapped to edge")
                }
            } catch (e: Exception) {
                Log.e("FloatingViewService", "Error setting final position: ${e.message}")
            }
        }, duration + 10)
    }

    private fun stopFloatingView() {
        try {
            // Remove the floating view
            removeExistingView()

            // Remove the dismiss area if it's showing
            hideDismissArea()

            // Reset state
            floatingView = null
            dismissAreaView = null
            layoutParams = null
            dismissAreaParams = null
            isViewAttached = false
            isDismissAreaShowing = false
            isDragging = false
            showDismissArea = false
            inDismissArea = false

            // Stop the foreground service
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                if (Build.VERSION.SDK_INT >= 31) { // Android 12+
                    stopForeground(Service.STOP_FOREGROUND_REMOVE)
                } else {
                    stopForeground(true)
                }
            } else {
                stopForeground(true)
            }

            stopSelf()
            Log.d("FloatingViewService", "Floating view stopped")
        } catch (e: Exception) {
            Log.e("FloatingViewService", "Error stopping floating view: ${e.message}")

            // Make sure we stop the service even if there's an error
            try {
                stopSelf()
            } catch (e2: Exception) {
                Log.e("FloatingViewService", "Error stopping service: ${e2.message}")
            }
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Floating View Service"
            val descriptionText = "Shows a floating button for scanning"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(NOTIFICATION_CHANNEL_ID, name, importance).apply {
                description = descriptionText
                setShowBadge(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): android.app.Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setContentTitle("DomeAI Scam Detector")
            .setContentText("Overlay protection is active")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .build()
    }
}
