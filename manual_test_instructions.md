# Manual Testing Instructions for Google Play RTDN Endpoint

## Test Data

### Test Notification

```json
{
  "message": {
    "data": "eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJjb20uZG9tZWFpLnNjYW1kZXRlY3RvciIsImV2ZW50VGltZU1pbGxpcyI6IjE2MjEyMzQ1Njc4OTAiLCJ0ZXN0Tm90aWZpY2F0aW9uIjp7InZlcnNpb24iOiIxLjAifX0=",
    "messageId": "test-message-id",
    "publishTime": "2023-05-20T10:00:00.000Z"
  },
  "subscription": "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
}
```

The `data` field is a base64-encoded JSON string that decodes to:

```json
{
  "version": "1.0",
  "packageName": "com.domeai.scamdetector",
  "eventTimeMillis": "1621234567890",
  "testNotification": {
    "version": "1.0"
  }
}
```

### Subscription Purchased Notification

```json
{
  "message": {
    "data": "eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJjb20uZG9tZWFpLnNjYW1kZXRlY3RvciIsImV2ZW50VGltZU1pbGxpcyI6IjE2MjEyMzQ1Njc4OTAiLCJzdWJzY3JpcHRpb25Ob3RpZmljYXRpb24iOnsidmVyc2lvbiI6IjEuMCIsIm5vdGlmaWNhdGlvblR5cGUiOjQsInB1cmNoYXNlVG9rZW4iOiJ0ZXN0LXB1cmNoYXNlLXRva2VuIiwic3Vic2NyaXB0aW9uSWQiOiJkb21lYWlfcHJlbWl1bV9tb250aGx5In19",
    "messageId": "test-subscription-message-id",
    "publishTime": "2023-05-20T10:00:00.000Z"
  },
  "subscription": "projects/domeai-project/subscriptions/google-play-rtdn-subscription"
}
```

The `data` field is a base64-encoded JSON string that decodes to:

```json
{
  "version": "1.0",
  "packageName": "com.domeai.scamdetector",
  "eventTimeMillis": "1621234567890",
  "subscriptionNotification": {
    "version": "1.0",
    "notificationType": 4,
    "purchaseToken": "test-purchase-token",
    "subscriptionId": "domeai_premium_monthly"
  }
}
```

## Testing Steps

1. Start the API server:
   ```bash
   cd domeai_backend
   uvicorn app.main:app --reload
   ```

2. In a separate terminal, send a test notification:
   ```bash
   curl -X POST http://localhost:8000/api/v1/webhooks/googleplay/rtdn \
     -H "Content-Type: application/json" \
     -d '{"message":{"data":"eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJjb20uZG9tZWFpLnNjYW1kZXRlY3RvciIsImV2ZW50VGltZU1pbGxpcyI6IjE2MjEyMzQ1Njc4OTAiLCJ0ZXN0Tm90aWZpY2F0aW9uIjp7InZlcnNpb24iOiIxLjAifX0=","messageId":"test-message-id","publishTime":"2023-05-20T10:00:00.000Z"},"subscription":"projects/domeai-project/subscriptions/google-play-rtdn-subscription"}'
   ```

3. Send a subscription purchased notification:
   ```bash
   curl -X POST http://localhost:8000/api/v1/webhooks/googleplay/rtdn \
     -H "Content-Type: application/json" \
     -d '{"message":{"data":"eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJjb20uZG9tZWFpLnNjYW1kZXRlY3RvciIsImV2ZW50VGltZU1pbGxpcyI6IjE2MjEyMzQ1Njc4OTAiLCJzdWJzY3JpcHRpb25Ob3RpZmljYXRpb24iOnsidmVyc2lvbiI6IjEuMCIsIm5vdGlmaWNhdGlvblR5cGUiOjQsInB1cmNoYXNlVG9rZW4iOiJ0ZXN0LXB1cmNoYXNlLXRva2VuIiwic3Vic2NyaXB0aW9uSWQiOiJkb21lYWlfcHJlbWl1bV9tb250aGx5In19","messageId":"test-subscription-message-id","publishTime":"2023-05-20T10:00:00.000Z"},"subscription":"projects/domeai-project/subscriptions/google-play-rtdn-subscription"}'
   ```

4. Check the API server logs for the following:
   - Successful receipt of the notifications
   - Proper decoding of the base64 data
   - Correct identification of notification types
   - Appropriate processing of the notifications

## Expected Response

For both requests, you should receive a response like:

```json
{
  "status": "success",
  "message": "RTDN received and processed"
}
```

## Troubleshooting

If you encounter any issues:

1. Make sure the API server is running
2. Check that the webhook endpoint is properly registered in the API router
3. Verify that the Pydantic schemas are correctly defined
4. Ensure that the Google Play service is properly implemented
