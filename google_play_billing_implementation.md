# Google Play Billing Implementation for DomeAI Scam Detector

This document explains the implementation of Google Play Billing for the DomeAI Scam Detector app.

## Backend Implementation

### 1. User Model Updates

We've added the following fields to the User model:
- `subscription_provider` (String, will store "google_play")
- `provider_subscription_id` (String, Google Play's orderId)
- `google_play_purchase_token` (Text, latest purchaseToken for verification)
- `subscription_product_id` (String, e.g., "domeai_premium_monthly")
- `subscription_expiry_date` (DateTime)
- `auto_renew_status` (Boolean)
- `is_trial_period` (Boolean)

### 2. Database Migration

We've created a migration file (`006_add_google_play_subscription_fields.py`) to add these fields to the database.

### 3. Pydantic Schemas

We've created schemas for different types of notifications:
- `GooglePlaySubscriptionNotification`
- `GooglePlayTestNotification`
- `GooglePlayOneTimeProductNotification`
- `GooglePlayVoidedPurchaseNotification`
- `DeveloperNotification`
- `PubSubMessage`
- `PubSubMessageData`

### 4. Google Play Service

We've implemented a service for processing RTDNs and updating user subscriptions. The service includes:
- `process_rtdn`: Processes a Google Play Real-Time Developer Notification
- `_process_subscription_notification`: Processes a subscription notification
- `_process_one_time_product_notification`: Processes a one-time product notification
- `_process_voided_purchase_notification`: Processes a voided purchase notification
- `get_subscription_data`: Gets subscription data from Google Play Developer API
- `_get_google_play_access_token`: Gets an access token for the Google Play Developer API
- `verify_google_play_purchase`: Verifies a Google Play purchase with the Google Play Developer API
- `update_user_subscription_from_google_play`: Updates a user's subscription based on Google Play purchase information

### 5. Webhook Endpoint

We've created an endpoint to receive and process Google Play RTDNs at `/api/v1/webhooks/googleplay/rtdn`.

### 6. API Router

We've included the webhooks router in the API router in `app/api/v1/api.py`.

## Android Implementation

### 1. Dependencies

We've added the Google Play Billing Library to the app's build.gradle file:
```kotlin
// Google Play Billing
implementation("com.android.billingclient:billing-ktx:6.0.1")
```

### 2. Permissions

We've added the Google Play Billing permission to the AndroidManifest.xml file:
```xml
<!-- Google Play Billing permission -->
<uses-permission android:name="com.android.vending.BILLING" />
```

### 3. Billing Manager

We've implemented a `BillingManager` class that handles Google Play Billing operations:
- Connects to the Google Play Billing service
- Queries available subscription products
- Handles purchases and purchase updates
- Verifies purchases with the backend
- Updates the user's subscription status

### 4. Billing Repository

We've implemented a `BillingRepository` class that provides a clean API for billing operations:
- Gets subscription products
- Gets the user's subscription status
- Launches the purchase flow for a subscription
- Gets subscription product details

### 5. Billing ViewModel

We've implemented a `BillingViewModel` class that exposes billing data to the UI:
- Gets subscription products
- Gets the user's subscription status
- Launches the purchase flow for a subscription
- Checks if the user has a premium or expert subscription

### 6. Subscription Screen

We've implemented a `SubscriptionScreen` composable that displays:
- The user's current subscription status
- Available subscription plans
- Buttons to subscribe to different plans

## Testing

### 1. Backend Testing

To test the backend implementation:
1. Start the API server
2. Send a test notification to the webhook endpoint
3. Check the logs to verify that the notification is processed correctly

### 2. Android Testing

To test the Android implementation:
1. Build and run the app
2. Navigate to the Subscription screen
3. Verify that the available subscription plans are displayed
4. Try to subscribe to a plan
5. Verify that the purchase flow works correctly
6. Verify that the user's subscription status is updated correctly

## Next Steps

1. Set up a Google Play service account and download the service account key
2. Configure the backend to use the service account key for API authentication
3. Set up subscription products in the Google Play Console
4. Configure the RTDN endpoint in the Google Play Console
5. Test the full flow from purchase to subscription activation

## Resources

- [Google Play Billing Library documentation](https://developer.android.com/google/play/billing)
- [Google Play Developer API documentation](https://developers.google.com/android-publisher)
- [Real-Time Developer Notifications documentation](https://developer.android.com/google/play/billing/rtdn-reference)
