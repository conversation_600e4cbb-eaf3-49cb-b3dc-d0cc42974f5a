from typing import Optional
from datetime import datetime

from pydantic import BaseModel, EmailStr, Field


class UserBase(BaseModel):
    """
    Base schema for user data.
    """
    email: EmailStr


class UserCreate(UserBase):
    """
    Schema for user creation.
    """
    password: str = Field(..., min_length=8)


class UserLogin(BaseModel):
    """
    Schema for user login.
    """
    email: EmailStr
    password: str


class User(UserBase):
    """
    Schema for user response.
    """
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserInDB(User):
    """
    Schema for user in database.
    """
    hashed_password: str

    class Config:
        from_attributes = True
