# Google Play Billing Implementation Report

## Overview

I've implemented detailed processing logic for key RTDN types within `GooglePlayService` and ensured correct user entitlement updates based on mocked Google API interactions. This implementation follows the requirements specified in the task.

## Part A: Define Placeholder SKUs and Tier Mapping

I've created a configuration file `app/core/google_play_config.py` that defines:

1. **SKU to Tier Mapping**:
   ```python
   GOOGLE_PLAY_SKU_TO_TIER_MAP = {
       # Premium tier
       "domeai_placeholder_premium_monthly": "premium",
       "domeai_placeholder_premium_yearly": "premium",
       # Expert tier
       "domeai_placeholder_expert_monthly": "expert",
       "domeai_placeholder_expert_yearly": "expert",
       # Free tier (if managed as a $0 product)
       "domeai_placeholder_free": "free"
   }
   ```

2. **Tier Allowances**:
   ```python
   TIER_ALLOWANCES = {
       "free": {"monthly_scans": 5, "expert_scans": 0},
       "premium": {"monthly_scans": 100, "expert_scans": 0},
       "expert": {"monthly_scans": 100, "expert_scans": 20},
   }
   ```

3. **Notification Type Mapping**:
   ```python
   SUBSCRIPTION_NOTIFICATION_TYPES = {
       1: "SUBSCRIPTION_RECOVERED",
       2: "SUBSCRIPTION_RENEWED",
       3: "SUBSCRIPTION_CANCELED",
       4: "SUBSCRIPTION_PURCHASED",
       # ... other notification types
   }
   ```

4. **Mock Google Play API Response**:
   ```python
   def get_mock_subscription_data(product_id: str, purchase_token: str) -> Dict[str, Any]:
       # ... returns a mock Google Play API response
   ```

## Part B: Enhanced `GooglePlayService` Functions

### 1. Main `process_rtdn` Router Logic

The `process_rtdn` function now:
- Logs detailed information about the notification
- Identifies the notification type and calls the appropriate processing function
- Handles all notification types (subscription, test, one-time product, voided purchase)

### 2. `_process_subscription_notification` Function

This function now:
- Extracts subscription details from the notification
- For `SUBSCRIPTION_PURCHASED`, verifies the purchase with Google Play and finds the user
- For other notification types, finds the user by purchase token or provider subscription ID
- Processes different notification types (purchased, renewed, canceled, expired, revoked, on hold, in grace period, restarted)
- Updates the user's subscription status accordingly

### 3. `verify_google_play_purchase` Function

This function now:
- Gets subscription data from Google Play (mocked for testing)
- Checks if the subscription is valid (purchased and not expired)
- If valid and a user ID is provided, updates the user's subscription
- Returns a tuple of (is_valid, subscription_data)

### 4. `update_user_subscription_from_google_play` Function

This function now:
- Extracts data from the Google subscription data
- Maps the product ID to a subscription tier using `GOOGLE_PLAY_SKU_TO_TIER_MAP`
- Updates the user's subscription information
- Updates scan allowances based on the subscription tier using `TIER_ALLOWANCES`
- Resets scan counters
- Updates the last RTDN received timestamp
- Logs detailed information about the subscription update

## Part C: Testing with Manually Crafted RTDN Payloads

I've created test scripts to test the Google Play RTDN webhook with different notification types:

1. **`test_google_play_rtdn.py`**: A script that sends test notifications to the webhook endpoint
2. **`test_google_play_rtdn.bat`**: A batch file that runs all the tests
3. **`create_test_user.py`**: A script that creates a test user with Google Play subscription data

### Test Case 1: New Premium Subscription Purchase

**RTDN Payload**:
```json
{
  "version": "1.0",
  "packageName": "com.domeai.scamdetector",
  "eventTimeMillis": "*************",
  "subscriptionNotification": {
    "version": "1.0",
    "notificationType": 4,
    "purchaseToken": "test_purchase_token_123456789",
    "subscriptionId": "domeai_placeholder_premium_monthly"
  }
}
```

**Expected Flow**:
1. `process_rtdn` receives the notification
2. Calls `_process_subscription_notification`
3. Identifies the notification type as `SUBSCRIPTION_PURCHASED`
4. Calls `verify_google_play_purchase`
5. `get_subscription_data` returns the mocked Google Play API response
6. `verify_google_play_purchase` validates the purchase
7. Finds the user by external account ID or purchase token
8. Calls `update_user_subscription_from_google_play`
9. Updates the user's subscription tier to "premium"
10. Updates the user's scan allowances to 100 monthly scans and 0 expert scans
11. Resets the user's scan counters
12. Updates the user's subscription expiry date

**Expected DB State Change**:
- User's subscription tier is set to "premium"
- User's monthly scan allowance is set to 100
- User's expert scan allowance is set to 0
- User's scan counters are reset
- User's subscription expiry date is updated
- User's auto-renew status is set to true
- User's is-trial-period status is set based on the payment state

### Test Case 2: Subscription Cancellation

**RTDN Payload**:
```json
{
  "version": "1.0",
  "packageName": "com.domeai.scamdetector",
  "eventTimeMillis": "*************",
  "subscriptionNotification": {
    "version": "1.0",
    "notificationType": 3,
    "purchaseToken": "test_purchase_token_123456789",
    "subscriptionId": "domeai_placeholder_premium_monthly"
  }
}
```

**Expected Flow**:
1. `process_rtdn` receives the notification
2. Calls `_process_subscription_notification`
3. Identifies the notification type as `SUBSCRIPTION_CANCELED`
4. Finds the user by purchase token
5. Sets the user's auto-renew status to false
6. Updates the last RTDN received timestamp

**Expected DB State Change**:
- User's auto-renew status is set to false
- User's subscription tier remains "premium" until the expiry date
- User's last RTDN received timestamp is updated

### Test Case 3: Subscription Expiry

**RTDN Payload**:
```json
{
  "version": "1.0",
  "packageName": "com.domeai.scamdetector",
  "eventTimeMillis": "*************",
  "subscriptionNotification": {
    "version": "1.0",
    "notificationType": 13,
    "purchaseToken": "test_purchase_token_123456789",
    "subscriptionId": "domeai_placeholder_premium_monthly"
  }
}
```

**Expected Flow**:
1. `process_rtdn` receives the notification
2. Calls `_process_subscription_notification`
3. Identifies the notification type as `SUBSCRIPTION_EXPIRED`
4. Finds the user by purchase token
5. Downgrades the user to "free" tier
6. Updates the user's scan allowances to 5 monthly scans and 0 expert scans
7. Sets the user's auto-renew status to false
8. Updates the last RTDN received timestamp

**Expected DB State Change**:
- User's subscription tier is set to "free"
- User's monthly scan allowance is set to 5
- User's expert scan allowance is set to 0
- User's auto-renew status is set to false
- User's last RTDN received timestamp is updated

## User Identification Strategy

For processing RTDNs, the system identifies users in the following ways:

1. **For `SUBSCRIPTION_PURCHASED`**:
   - First tries to find the user by external account ID if available
   - If not found, tries to find by purchase token
   - If still not found, logs a warning (in a real implementation, you might store this purchase information and associate it with a user when they log in or register)

2. **For other notification types**:
   - First tries to find the user by purchase token
   - If not found, tries to find by provider subscription ID (for renewals, gets the order ID from Google Play)
   - If still not found, logs a warning

## Mocked Google Play API Response

The mocked Google Play API response used in `get_subscription_data` is:

```python
{
    "kind": "androidpublisher#subscriptionPurchaseV2",
    "startTime": str(current_time_millis - 30 * 24 * 60 * 60 * 1000),  # 30 days ago
    "expiryTime": str(current_time_millis + 30 * 24 * 60 * 60 * 1000),  # 30 days from now
    "autoRenewing": True,
    "priceCurrencyCode": "USD",
    "priceAmountMicros": "*********",  # $9.99
    "countryCode": "US",
    "developerPayload": "",
    "cancelReason": 0,
    "orderId": f"GPA.1234-5678-9012-{purchase_token[:8]}",
    "purchaseType": 0,
    "acknowledgementState": 1,  # 1 for ACKNOWLEDGED
    "purchaseState": 0,  # 0 for PURCHASED, 1 for CANCELED, 2 for PENDING
    "regionCode": "US",
    "externalAccountId": "user_123456",  # This would be your user ID if set during purchase
    "promotionType": 0,
    "promotionCode": "",
    "obfuscatedExternalAccountId": "user_123456",
    "obfuscatedExternalProfileId": "",
    "linkedPurchaseToken": "",
    "pauseReason": 0,
    "productId": product_id,
    "startTimeMillis": str(current_time_millis - 30 * 24 * 60 * 60 * 1000),  # 30 days ago
    "expiryTimeMillis": str(current_time_millis + 30 * 24 * 60 * 60 * 1000),  # 30 days from now
}
```

## Conclusion

The implementation now handles all the key RTDN types and updates user entitlements correctly based on the notification type. The system uses mocked Google Play API responses for testing, which can be replaced with real API calls in a production environment.

To test the implementation, run the `test_google_play_rtdn.bat` script, which will send test notifications for all the key notification types. Before running the tests, make sure to create a test user with Google Play subscription data using the `create_test_user.py` script.
