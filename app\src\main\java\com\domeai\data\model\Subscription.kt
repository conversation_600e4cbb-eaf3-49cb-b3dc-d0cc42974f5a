package com.domeai.data.model

import java.util.Date

/**
 * Data class representing a subscription
 */
data class Subscription(
    val id: String,
    val userId: String,
    val planName: String,
    val price: Double,
    val currency: String = "USD",
    val billingCycle: BillingCycle,
    val startDate: Date,
    val endDate: Date,
    val isAutoRenewEnabled: Boolean,
    val status: SubscriptionStatus,
    val scansThisMonth: Int = 0,
    val expertScansThisMonth: Int = 0,
    val monthlyScanAllowance: Int = 5,
    val expertScanAllowance: Int = 0
)

/**
 * Enum representing subscription billing cycles
 */
enum class BillingCycle {
    MONTHLY,
    ANNUAL,
    FREE
}

/**
 * Enum representing subscription statuses
 */
enum class SubscriptionStatus {
    ACTIVE,
    CANCELLED,
    EXPIRED,
    PENDING
}

/**
 * Data class representing a payment record
 */
data class PaymentRecord(
    val id: String,
    val subscriptionId: String,
    val userId: String,
    val amount: Double,
    val currency: String = "USD",
    val description: String,
    val date: Date,
    val status: PaymentStatus
)

/**
 * Enum representing payment statuses
 */
enum class PaymentStatus {
    COMPLETED,
    FAILED,
    REFUNDED,
    PENDING
}
