package com.domeai.presentation.auth

import androidx.lifecycle.viewModelScope
import com.domeai.data.model.AuthResult
import com.domeai.data.model.SignUpCredentials
import com.domeai.data.model.ValidationResult
import com.domeai.data.repository.AuthRepository
import com.domeai.data.util.Validator
import com.domeai.presentation.common.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * UI State for SignUp Screen
 */
data class SignUpUiState(
    val email: String = "",
    val password: String = "",
    val confirmPassword: String = "",
    val acceptedTerms: Boolean = false,
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val isSignUpSuccessful: Boolean = false,
    val emailError: String? = null,
    val passwordError: String? = null,
    val confirmPasswordError: String? = null,
    val termsError: String? = null,
    val passwordCriteria: com.domeai.ui.composables.PasswordCriteria = com.domeai.ui.composables.PasswordCriteria(),
    val isFormValid: Boolean = false
) : com.domeai.presentation.common.UiState

/**
 * UI Events for SignUp Screen
 */
sealed class SignUpUiEvent : com.domeai.presentation.common.UiEvent {
    data object NavigateToLogin : SignUpUiEvent()
    data object NavigateToDataConsent : SignUpUiEvent()
    data object NavigateToMain : SignUpUiEvent()
    data object NavigateToTermsOfService : SignUpUiEvent()
    data object NavigateToPrivacyPolicy : SignUpUiEvent()
}

/**
 * UI Actions for SignUp Screen
 */
sealed class SignUpUiAction : com.domeai.presentation.common.UiAction {
    data class UpdateEmail(val email: String) : SignUpUiAction()
    data class UpdatePassword(val password: String) : SignUpUiAction()
    data class UpdateConfirmPassword(val confirmPassword: String) : SignUpUiAction()
    data class UpdateTermsAccepted(val accepted: Boolean) : SignUpUiAction()
    data object SignUp : SignUpUiAction()
    data object NavigateToLogin : SignUpUiAction()
    data object NavigateToTermsOfService : SignUpUiAction()
    data object NavigateToPrivacyPolicy : SignUpUiAction()
}

/**
 * ViewModel for SignUp Screen
 */
@HiltViewModel
class SignUpViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val validator: Validator
) : BaseViewModel<SignUpUiState, SignUpUiEvent, SignUpUiAction>() {

    override fun createInitialState(): SignUpUiState = SignUpUiState()

    override fun handleAction(action: SignUpUiAction) {
        when (action) {
            is SignUpUiAction.UpdateEmail -> updateEmail(action.email)
            is SignUpUiAction.UpdatePassword -> updatePassword(action.password)
            is SignUpUiAction.UpdateConfirmPassword -> updateConfirmPassword(action.confirmPassword)
            is SignUpUiAction.UpdateTermsAccepted -> updateTermsAccepted(action.accepted)
            is SignUpUiAction.SignUp -> signUp()
            is SignUpUiAction.NavigateToLogin -> navigateToLogin()
            is SignUpUiAction.NavigateToTermsOfService -> navigateToTermsOfService()
            is SignUpUiAction.NavigateToPrivacyPolicy -> navigateToPrivacyPolicy()
        }
    }

    private fun updateEmail(email: String) {
        updateState { it.copy(email = email, emailError = null, errorMessage = null) }
        validateForm()
    }

    private fun updatePassword(password: String) {
        val passwordCriteria = com.domeai.ui.composables.evaluatePasswordStrength(password)
        updateState {
            it.copy(
                password = password,
                passwordError = null,
                passwordCriteria = passwordCriteria,
                errorMessage = null
            )
        }
        validateForm()
    }

    private fun updateConfirmPassword(confirmPassword: String) {
        updateState { it.copy(confirmPassword = confirmPassword, confirmPasswordError = null, errorMessage = null) }
        validateForm()
    }

    private fun updateTermsAccepted(accepted: Boolean) {
        updateState { it.copy(acceptedTerms = accepted, termsError = null, errorMessage = null) }
        validateForm()
    }

    private fun signUp() {
        val currentState = uiState.value
        val emailValidation = validator.validateEmail(currentState.email)
        val passwordValidation = validator.validatePassword(currentState.password)
        val confirmPasswordValidation = validator.validatePasswordConfirmation(
            currentState.password,
            currentState.confirmPassword
        )
        val termsValidation = validator.validateTermsAccepted(currentState.acceptedTerms)

        val hasErrors = handleValidationResults(
            emailValidation,
            passwordValidation,
            confirmPasswordValidation,
            termsValidation
        )

        if (hasErrors) return

        viewModelScope.launch {
            val credentials = SignUpCredentials(
                email = currentState.email,
                password = currentState.password,
                confirmPassword = currentState.confirmPassword,
                acceptedTerms = currentState.acceptedTerms
            )

            authRepository.signUp(credentials).collectLatest { result ->
                when (result) {
                    is AuthResult.Loading -> {
                        updateState { it.copy(isLoading = true, errorMessage = null) }
                    }
                    is AuthResult.Success -> {
                        updateState {
                            it.copy(
                                isLoading = false,
                                isSignUpSuccessful = true,
                                errorMessage = null
                            )
                        }
                        // Navigate to main screen
                        sendEvent(SignUpUiEvent.NavigateToMain)
                    }
                    is AuthResult.Error -> {
                        updateState {
                            it.copy(
                                isLoading = false,
                                errorMessage = result.message
                            )
                        }
                    }
                    else -> { /* Ignore other states */ }
                }
            }
        }
    }

    private fun handleValidationResults(
        emailValidation: ValidationResult,
        passwordValidation: ValidationResult,
        confirmPasswordValidation: ValidationResult,
        termsValidation: ValidationResult
    ): Boolean {
        var hasErrors = false

        if (emailValidation is ValidationResult.Invalid) {
            updateState { it.copy(emailError = emailValidation.errorMessage) }
            hasErrors = true
        }

        if (passwordValidation is ValidationResult.Invalid) {
            updateState { it.copy(passwordError = passwordValidation.errorMessage) }
            hasErrors = true
        }

        if (confirmPasswordValidation is ValidationResult.Invalid) {
            updateState { it.copy(confirmPasswordError = confirmPasswordValidation.errorMessage) }
            hasErrors = true
        }

        if (termsValidation is ValidationResult.Invalid) {
            updateState { it.copy(termsError = termsValidation.errorMessage) }
            hasErrors = true
        }

        return hasErrors
    }

    private fun navigateToLogin() {
        sendEvent(SignUpUiEvent.NavigateToLogin)
    }

    private fun navigateToTermsOfService() {
        sendEvent(SignUpUiEvent.NavigateToTermsOfService)
    }

    private fun navigateToPrivacyPolicy() {
        sendEvent(SignUpUiEvent.NavigateToPrivacyPolicy)
    }

    private fun validateForm() {
        val currentState = uiState.value
        val emailValid = validator.validateEmail(currentState.email) is ValidationResult.Valid
        val passwordValid = currentState.passwordCriteria.isStrong
        val confirmPasswordValid = currentState.password == currentState.confirmPassword && currentState.confirmPassword.isNotBlank()
        val termsAccepted = currentState.acceptedTerms

        val isFormValid = emailValid && passwordValid && confirmPasswordValid && termsAccepted

        // Clear error message if form is valid
        val errorMessage = if (isFormValid) null else currentState.errorMessage

        updateState {
            it.copy(
                isFormValid = isFormValid,
                errorMessage = errorMessage
            )
        }
    }
}
