from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.api.deps import get_current_user
from app.core.database import get_db
from app.core.security import create_access_token
from app.crud.crud_user import authenticate_user, create_user, get_user_by_email
from app.models.user import User
from app.schemas.token import Token
from app.schemas.user import User as UserSchema, UserCreate

router = APIRouter()


@router.post("/register", response_model=UserSchema)
def register_user(
    user_in: UserCreate, db: Session = Depends(get_db)
) -> User:
    """
    Register a new user.

    Args:
        user_in: User creation data.
        db: Database session.

    Returns:
        User: The created user.

    Raises:
        HTTPException: If a user with the given email already exists.
    """
    try:
        # Check if user already exists
        user = get_user_by_email(db, email=user_in.email)
        if user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered",
            )

        # Create new user
        return create_user(db, user_in=user_in)
    except Exception as e:
        # Log the error
        print(f"Error in register_user: {str(e)}")
        # Re-raise the exception
        raise


@router.post("/login", response_model=Token)
def login_for_access_token(
    db: Session = Depends(get_db), form_data: OAuth2PasswordRequestForm = Depends()
) -> dict:
    """
    OAuth2 compatible token login, get an access token for future requests.

    Args:
        db: Database session.
        form_data: OAuth2 form data.

    Returns:
        dict: Access token and token type.

    Raises:
        HTTPException: If authentication fails.
    """
    # Authenticate user
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token
    access_token = create_access_token(subject=user.id)

    return {"access_token": access_token, "token_type": "bearer"}


@router.get("/me", response_model=UserSchema)
def read_users_me(current_user: User = Depends(get_current_user)) -> User:
    """
    Get current user.

    Args:
        current_user: Current authenticated user.

    Returns:
        User: The current user.
    """
    return current_user
