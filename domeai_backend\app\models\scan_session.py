import uuid
from datetime import datetime
from sqlalchemy import Column, String, DateTime, Foreign<PERSON>ey, Integer, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.core.database import Base


class ScanSession(Base):
    """
    Model for storing scan sessions.

    A scan session represents a group of related scans, such as screenshots
    from an ongoing conversation.
    """
    __tablename__ = "scan_sessions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    title = Column(String, nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    last_activity_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    owner = relationship("User")
    scans = relationship("Scan", back_populates="session", order_by="Scan.created_at", cascade="all, delete-orphan")
