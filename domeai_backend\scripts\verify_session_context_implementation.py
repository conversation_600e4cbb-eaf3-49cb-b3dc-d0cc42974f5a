#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to verify the session context implementation.

This script checks the code implementation of session context accumulation and session usage limits
without actually running the tests.
"""

import logging
import sys
import os
import inspect

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.config import settings
from app.tasks.scan_tasks import process_scan_task, _process_scan_with_ai
from app.services.ai_services import OpenAIModelService, AIServiceInterface
from app.schemas.scan import ScanResultData

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def verify_config_settings():
    """Verify that the session limit settings are correctly defined in the config."""
    logger.info("Verifying session limit settings in config")
    
    # Check if the session limit settings are defined
    has_premium_limit = hasattr(settings, "SESSION_MAX_INPUTS_PREMIUM")
    has_expert_limit = hasattr(settings, "SESSION_MAX_INPUTS_EXPERT")
    has_activity_window = hasattr(settings, "SESSION_ACTIVITY_WINDOW_HOURS")
    
    logger.info(f"SESSION_MAX_INPUTS_PREMIUM defined: {has_premium_limit}")
    if has_premium_limit:
        logger.info(f"SESSION_MAX_INPUTS_PREMIUM value: {settings.SESSION_MAX_INPUTS_PREMIUM}")
    
    logger.info(f"SESSION_MAX_INPUTS_EXPERT defined: {has_expert_limit}")
    if has_expert_limit:
        logger.info(f"SESSION_MAX_INPUTS_EXPERT value: {settings.SESSION_MAX_INPUTS_EXPERT}")
    
    logger.info(f"SESSION_ACTIVITY_WINDOW_HOURS defined: {has_activity_window}")
    if has_activity_window:
        logger.info(f"SESSION_ACTIVITY_WINDOW_HOURS value: {settings.SESSION_ACTIVITY_WINDOW_HOURS}")
    
    return has_premium_limit and has_expert_limit and has_activity_window

def verify_scan_result_data_schema():
    """Verify that the ScanResultData schema includes the overall_session_assessment field."""
    logger.info("Verifying ScanResultData schema")
    
    # Check if the overall_session_assessment field is defined in the ScanResultData schema
    has_session_assessment = hasattr(ScanResultData.__fields__, "overall_session_assessment")
    
    logger.info(f"overall_session_assessment field defined: {has_session_assessment}")
    
    return has_session_assessment

def verify_ai_service_interface():
    """Verify that the AIServiceInterface includes the accumulated_session_context parameter."""
    logger.info("Verifying AIServiceInterface")
    
    # Get the signature of the perform_scam_analysis_with_rag method
    signature = inspect.signature(AIServiceInterface.perform_scam_analysis_with_rag)
    
    # Check if the accumulated_session_context parameter is defined
    has_session_context = "accumulated_session_context" in signature.parameters
    
    logger.info(f"accumulated_session_context parameter defined: {has_session_context}")
    
    return has_session_context

def verify_openai_model_service():
    """Verify that the OpenAIModelService implementation includes the accumulated_session_context parameter."""
    logger.info("Verifying OpenAIModelService implementation")
    
    # Get the signature of the perform_scam_analysis_with_rag method
    signature = inspect.signature(OpenAIModelService.perform_scam_analysis_with_rag)
    
    # Check if the accumulated_session_context parameter is defined
    has_session_context = "accumulated_session_context" in signature.parameters
    
    logger.info(f"accumulated_session_context parameter defined: {has_session_context}")
    
    # Check if the method implementation uses the accumulated_session_context parameter
    source = inspect.getsource(OpenAIModelService.perform_scam_analysis_with_rag)
    uses_session_context = "accumulated_session_context" in source and "if accumulated_session_context:" in source
    
    logger.info(f"Method implementation uses accumulated_session_context: {uses_session_context}")
    
    return has_session_context and uses_session_context

def verify_process_scan_task():
    """Verify that the process_scan_task function compiles and passes the accumulated session context."""
    logger.info("Verifying process_scan_task function")
    
    # Get the source code of the process_scan_task function
    source = inspect.getsource(process_scan_task)
    
    # Check if the function compiles the accumulated session context
    compiles_context = "accumulated_session_context" in source and "context_parts" in source
    
    logger.info(f"Function compiles accumulated_session_context: {compiles_context}")
    
    # Check if the function passes the accumulated session context to _process_scan_with_ai
    passes_context = "_process_scan_with_ai(scan, ai_service, accumulated_session_context)" in source
    
    logger.info(f"Function passes accumulated_session_context to _process_scan_with_ai: {passes_context}")
    
    return compiles_context and passes_context

def verify_process_scan_with_ai():
    """Verify that the _process_scan_with_ai function accepts and uses the accumulated session context."""
    logger.info("Verifying _process_scan_with_ai function")
    
    # Get the signature of the _process_scan_with_ai function
    signature = inspect.signature(_process_scan_with_ai)
    
    # Check if the accumulated_session_context parameter is defined
    has_session_context = "accumulated_session_context" in signature.parameters
    
    logger.info(f"accumulated_session_context parameter defined: {has_session_context}")
    
    # Get the source code of the _process_scan_with_ai function
    source = inspect.getsource(_process_scan_with_ai)
    
    # Check if the function passes the accumulated session context to perform_scam_analysis_with_rag
    passes_context = "accumulated_session_context=accumulated_session_context" in source
    
    logger.info(f"Function passes accumulated_session_context to perform_scam_analysis_with_rag: {passes_context}")
    
    return has_session_context and passes_context

def main():
    """Run all verification checks."""
    logger.info("Starting verification of session context implementation")
    
    # Run all verification checks
    config_ok = verify_config_settings()
    schema_ok = verify_scan_result_data_schema()
    interface_ok = verify_ai_service_interface()
    service_ok = verify_openai_model_service()
    task_ok = verify_process_scan_task()
    process_ok = verify_process_scan_with_ai()
    
    # Print summary
    logger.info("\n--- Verification Summary ---")
    logger.info(f"Config settings: {'✅' if config_ok else '❌'}")
    logger.info(f"ScanResultData schema: {'✅' if schema_ok else '❌'}")
    logger.info(f"AIServiceInterface: {'✅' if interface_ok else '❌'}")
    logger.info(f"OpenAIModelService: {'✅' if service_ok else '❌'}")
    logger.info(f"process_scan_task: {'✅' if task_ok else '❌'}")
    logger.info(f"_process_scan_with_ai: {'✅' if process_ok else '❌'}")
    
    # Overall result
    all_ok = config_ok and schema_ok and interface_ok and service_ok and task_ok and process_ok
    logger.info(f"\nOverall result: {'✅ All checks passed' if all_ok else '❌ Some checks failed'}")

if __name__ == "__main__":
    main()
