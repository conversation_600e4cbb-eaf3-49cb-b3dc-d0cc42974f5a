# Google Play API Implementation

## Overview

I've implemented the real Google Play API call structure in `get_subscription_data` with graceful error handling, while ensuring the overall RTDN processing logic can still be tested with controlled outcomes.

## 1. Real API Call Implementation in `get_subscription_data`

The `get_subscription_data` function now:

1. Takes an optional `use_mock` parameter (default: `False`) to control whether to use mock data or make a real API call
2. Attempts to make a real API call to the Google Play Developer API using `google-api-python-client`
3. Falls back to a direct HTTP request using `httpx` if `google-api-python-client` is not available
4. Handles errors gracefully and logs detailed error messages
5. Returns `None` if the API call fails

```python
async def get_subscription_data(
    subscription_id: str, 
    purchase_token: str, 
    use_mock: bool = False
) -> Optional[Dict[str, Any]]:
    """
    Get subscription data from Google Play Developer API.
    
    Args:
        subscription_id: The subscription ID (product ID)
        purchase_token: The purchase token
        use_mock: Whether to use mock data instead of making a real API call (for testing)
        
    Returns:
        Subscription data if successful, None otherwise
    """
    try:
        logger.info(f"Getting subscription data for {subscription_id} with token {purchase_token}")
        
        # For testing purposes, use mock data if requested
        if use_mock:
            logger.info("Using mock data for testing")
            mock_data = get_mock_subscription_data(subscription_id, purchase_token)
            logger.info(f"Mock subscription data: {json.dumps(mock_data, indent=2)}")
            return mock_data
        
        # Get access token for Google Play API
        access_token = await _get_google_play_access_token()
        
        if not access_token:
            logger.error("Failed to get Google Play access token")
            return None
        
        # Make API request to get subscription data using google-api-python-client
        try:
            from googleapiclient.discovery import build
            from googleapiclient.errors import HttpError
            
            # Create the Google Play Developer API client
            package_name = settings.GOOGLE_PLAY_PACKAGE_NAME
            
            # Build the API client
            androidpublisher = build(
                'androidpublisher', 
                'v3', 
                credentials=None,  # We'll use the access token directly
                cache_discovery=False
            )
            
            # Create the request
            request = androidpublisher.purchases().subscriptionsv2().get(
                packageName=package_name,
                token=purchase_token
            )
            
            # Add the access token to the request headers
            request.headers['Authorization'] = f'Bearer {access_token}'
            
            # Execute the request
            response = request.execute()
            
            logger.info(f"Successfully retrieved subscription data from Google Play API")
            return response
            
        except ImportError as e:
            logger.error(f"Failed to import Google API client: {str(e)}")
            logger.warning("Falling back to direct HTTP request")
            
            # Fallback to direct HTTP request if google-api-python-client is not available
            package_name = settings.GOOGLE_PLAY_PACKAGE_NAME
            url = f"https://androidpublisher.googleapis.com/androidpublisher/v3/applications/{package_name}/purchases/subscriptions/{subscription_id}/tokens/{purchase_token}"
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url,
                    headers={"Authorization": f"Bearer {access_token}"}
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.error(f"Failed to get subscription data: {response.status_code} {response.text}")
                    # Log the specific error message from Google
                    try:
                        error_data = response.json()
                        error_message = error_data.get('error', {}).get('message', 'Unknown error')
                        logger.error(f"Google Play API error: {error_message}")
                    except Exception:
                        pass
                    return None
                
        except HttpError as e:
            # Handle specific Google API errors
            error_details = json.loads(e.content.decode())
            error_message = error_details.get('error', {}).get('message', str(e))
            logger.error(f"Failed to verify purchase token {purchase_token} for SKU {subscription_id} with Google. Reason: {error_message}")
            return None
    
    except Exception as e:
        logger.error(f"Error getting subscription data: {str(e)}", exc_info=True)
        return None
```

## 2. Authentication Using `service-account-key.json`

The authentication using `service-account-key.json` is handled by the `_get_google_play_access_token` function:

```python
async def _get_google_play_access_token() -> Optional[str]:
    """
    Get an access token for the Google Play Developer API.
    
    Returns:
        Access token if successful, None otherwise
    """
    try:
        # Path to service account key file
        key_file_path = os.path.join(os.path.dirname(__file__), "../../service-account-key.json")
        
        # Create credentials from service account key file
        credentials = service_account.Credentials.from_service_account_file(
            key_file_path,
            scopes=["https://www.googleapis.com/auth/androidpublisher"]
        )
        
        # Refresh the credentials if needed
        if credentials.expired:
            credentials.refresh(Request())
        
        return credentials.token
    
    except Exception as e:
        logger.error(f"Error getting Google Play access token: {str(e)}", exc_info=True)
        return None
```

This function:
1. Loads the service account key file from `domeai_backend/service-account-key.json`
2. Creates credentials from the key file with the appropriate scope for the Google Play Developer API
3. Refreshes the credentials if needed
4. Returns the access token

## 3. Testing Strategy for `GooglePlayService`

To ensure the rest of the `GooglePlayService` can still be tested even if live calls from `get_subscription_data` will currently fail with test tokens, I've implemented the following strategy:

1. **Automatic Mock Data for Test Tokens**: The `verify_google_play_purchase` function now checks if the real API call fails and the purchase token starts with "test_" or the product ID contains "placeholder". If so, it falls back to using mock data:

```python
if not subscription_data:
    logger.error("Failed to get subscription data")
    
    # For testing purposes, if real API call fails but we're in a test environment,
    # we can still proceed with mock data to test the rest of the flow
    if not use_mock and (purchase_token.startswith("test_") or "placeholder" in product_id):
        logger.warning("Using mock data as fallback for testing purposes")
        subscription_data = get_mock_subscription_data(product_id, purchase_token)
        logger.info(f"Mock subscription data: {json.dumps(subscription_data, indent=2)}")
    else:
        return False, None
```

2. **Explicit Mock Mode**: The `get_subscription_data` function now takes a `use_mock` parameter that can be set to `True` to explicitly use mock data instead of making a real API call:

```python
if use_mock:
    logger.info("Using mock data for testing")
    mock_data = get_mock_subscription_data(subscription_id, purchase_token)
    logger.info(f"Mock subscription data: {json.dumps(mock_data, indent=2)}")
    return mock_data
```

3. **Automatic Mock Mode for Test Data**: The `_process_subscription_notification` function now automatically uses mock data for test tokens:

```python
is_valid, subscription_data = await verify_google_play_purchase(
    db=db,
    user_id=None,  # We don't know the user ID yet
    product_id=subscription_id,
    purchase_token=purchase_token,
    notification_type=notification_type,
    use_mock=purchase_token.startswith("test_") or "placeholder" in subscription_id  # Use mock for test tokens
)
```

This ensures that the rest of the `GooglePlayService` can still be tested even if live calls from `get_subscription_data` will currently fail with test tokens.

## 4. Testing the Implementation

I've created a test script `test_google_play_api.py` that tests:

1. Getting an access token from Google Play
2. Getting subscription data with mock data
3. Getting subscription data with a real API call (which should fail gracefully with test tokens)

This script can be used to verify that the implementation works correctly and handles errors gracefully.

## Conclusion

The implementation now:
1. Makes real API calls to the Google Play Developer API when `use_mock` is `False`
2. Uses mock data when `use_mock` is `True` or when the real API call fails with test tokens
3. Handles errors gracefully and logs detailed error messages
4. Allows the rest of the `GooglePlayService` to be tested even if live calls from `get_subscription_data` will currently fail with test tokens

This approach ensures we're building the correct code for future live operations in `get_subscription_data` while still enabling us to test the dependent entitlement logic using controlled, simulated outcomes from the Google verification step until the Play Console is fully live.
