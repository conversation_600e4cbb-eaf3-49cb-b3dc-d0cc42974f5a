package com.domeai.data.model.network

import com.google.gson.annotations.SerializedName
import java.util.UUID

/**
 * Inner DTO for scan details
 */
data class ScanDetailsRequestData(
    @SerializedName("input_content_type") val inputContentType: String,
    @SerializedName("input_text") val inputText: String? = null,
    @SerializedName("input_url") val inputUrl: String? = null,
    @SerializedName("user_provided_context") val userProvidedContext: String? = null,
    @SerializedName("scan_session_id") val scanSessionId: String? = null
)

/**
 * Outer DTO for the main request body with nested scan_in
 */
data class ScanSubmissionPayload(
    @SerializedName("scan_in") val scanIn: ScanDetailsRequestData
)

/**
 * Subscription tier constants
 */
object SubscriptionTiers {
    const val BASIC = "basic"
    const val PREMIUM = "premium"
    const val EXPERT = "expert"

    // Scan limits
    const val BASIC_SCAN_LIMIT = 5
    const val PREMIUM_SCAN_LIMIT = Int.MAX_VALUE // Unlimited
    const val EXPERT_REGULAR_SCAN_LIMIT = 100
    const val EXPERT_ADVANCED_SCAN_LIMIT = 20
}

/**
 * Response model for scan operations
 */
data class ScanResponse(
    @SerializedName("id") val id: Int,
    @SerializedName("owner_id") val ownerId: Int,
    @SerializedName("status") val status: String,
    @SerializedName("created_at") val createdAt: String,
    @SerializedName("updated_at") val updatedAt: String,
    @SerializedName("input_text") val inputText: String? = null,
    @SerializedName("input_url") val inputUrl: String? = null,
    @SerializedName("input_content_type") val inputContentType: String,
    @SerializedName("user_provided_context") val userProvidedContext: String? = null,
    @SerializedName("raw_input_payload") val rawInputPayload: Map<String, Any>? = null,
    @SerializedName("analysis_result") val analysisResult: ScanResultData? = null,
    @SerializedName("error_message") val errorMessage: String? = null,
    @SerializedName("scan_session_id") val scanSessionId: String? = null
)

/**
 * Model for scan result data
 */
data class ScanResultData(
    @SerializedName("risk_score") val riskScore: Float? = null,
    @SerializedName("detected_red_flags") val detectedRedFlags: List<String>? = null,
    @SerializedName("explanation") val explanation: String? = null,
    @SerializedName("recommendations") val recommendations: String? = null,
    @SerializedName("confidence_level") val confidenceLevel: String? = null,
    @SerializedName("model_used") val modelUsed: String? = null,
    @SerializedName("overall_session_assessment") val overallSessionAssessment: String? = null,
    @SerializedName("key_findings") val keyFindings: String? = null,
    @SerializedName("knowledge_base_references") val knowledgeBaseReferences: String? = null,
    @SerializedName("is_general_question") val isGeneralQuestion: Boolean? = false
)
