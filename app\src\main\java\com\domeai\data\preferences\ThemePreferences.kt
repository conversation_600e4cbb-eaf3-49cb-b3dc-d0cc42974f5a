package com.domeai.data.preferences

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Preference keys for theme settings
 */
private object ThemePreferenceKeys {
    val DARK_MODE = booleanPreferencesKey("dark_mode")
}

/**
 * Repository for managing theme preferences
 */
@Singleton
class ThemePreferences @Inject constructor(
    private val dataStore: DataStore<Preferences>
) {
    /**
     * Get the current dark mode state
     */
    val isDarkMode: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[ThemePreferenceKeys.DARK_MODE] ?: false // Default to light mode
    }

    /**
     * Set the dark mode state
     */
    suspend fun setDarkMode(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[ThemePreferenceKeys.DARK_MODE] = enabled
        }
    }

    /**
     * Toggle the dark mode state
     */
    suspend fun toggleDarkMode() {
        dataStore.edit { preferences ->
            val current = preferences[ThemePreferenceKeys.DARK_MODE] ?: false
            preferences[ThemePreferenceKeys.DARK_MODE] = !current
        }
    }
}
