package com.domeai.data.model

/**
 * Data class representing the overlay service state
 */
data class OverlayServiceState(
    val isEnabled: Boolean = false,
    val hasPermission: Boolean = false
)

/**
 * Data class representing a scan result
 */
data class ScanResult(
    val id: String,
    val timestamp: Long,
    val riskScore: Int, // 0-100
    val riskLevel: RiskLevel,
    val redFlags: List<String>,
    val explanation: String,
    val sourceType: ScanSourceType,
    val sourceContent: String? = null,
    val isGeneralQuestion: Boolean = false
)

/**
 * Enum representing the risk level of a scan
 */
enum class RiskLevel {
    SAFE,
    LOW_RISK,
    MEDIUM_RISK,
    HIGH_RISK
}

/**
 * Enum representing the source type of a scan
 */
enum class ScanSourceType {
    OVERLAY_SCREENSHOT,
    MANUAL_TEXT,
    MANUAL_IMAGE
}
