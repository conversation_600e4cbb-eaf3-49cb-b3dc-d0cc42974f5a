#!/usr/bin/env python3

import psycopg2
import bcrypt
import sys

# Database connection
conn = psycopg2.connect(
    host="db",
    port="5432",
    database="dome_app_main_db",
    user="dome_api_user",
    password="Dome2025ApiSecure!"
)

cur = conn.cursor()

# Check if user exists
email = "<EMAIL>"
cur.execute("SELECT id, email, hashed_password, is_active FROM users WHERE email = %s", (email,))
user = cur.fetchone()

if user:
    print(f"User found: ID={user[0]}, Email={user[1]}, Active={user[3]}")

    # Test password verification
    password = "@#48CrVGt3"
    stored_hash = user[2]

    # Try to verify password
    try:
        if bcrypt.checkpw(password.encode('utf-8'), stored_hash.encode('utf-8')):
            print("Password verification: SUCCESS")
        else:
            print("Password verification: FAILED")
            print("Need to update password hash")

            # Generate new hash
            new_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            cur.execute("UPDATE users SET hashed_password = %s WHERE email = %s", (new_hash, email))
            conn.commit()
            print("Password hash updated successfully")
    except Exception as e:
        print(f"Error verifying password: {e}")

        # Generate new hash
        new_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        cur.execute("UPDATE users SET hashed_password = %s WHERE email = %s", (new_hash, email))
        conn.commit()
        print("Password hash updated successfully")

else:
    print("User not found! Creating user...")

    # Create the user
    password = "@#48CrVGt3"
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    cur.execute("""
        INSERT INTO users (email, hashed_password, is_active, subscription_tier, monthly_scan_allowance, scans_this_month, expert_scan_allowance, expert_scans_this_month)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    """, (email, hashed_password, True, 'premium', 100, 0, 15, 0))

    conn.commit()
    print("User created successfully with premium subscription")

# Check final state
cur.execute("SELECT id, email, is_active, subscription_tier FROM users WHERE email = %s", (email,))
user = cur.fetchone()
print(f"Final user state: ID={user[0]}, Email={user[1]}, Active={user[2]}, Tier={user[3]}")

cur.close()
conn.close()
print("Done!")
