-- Test knowledge base insertion
-- Insert a few test chunks to verify the RAG pipeline works

-- First, let's create a simple test chunk
INSERT INTO knowledge_base_chunks (content, source, created_at) VALUES
(
  'A primary red flag in cashier''s check scams is the overpayment tactic. The scammer sends you a check for more than the agreed price and asks you to wire back the excess. This is a classic scam pattern.',
  'kb_cashier_check_test',
  now()
);
