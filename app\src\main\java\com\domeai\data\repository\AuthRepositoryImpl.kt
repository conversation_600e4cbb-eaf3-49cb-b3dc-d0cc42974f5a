package com.domeai.data.repository

import com.domeai.data.model.AuthResult
import com.domeai.data.model.ForgotPasswordRequest
import com.domeai.data.model.LoginCredentials
import com.domeai.data.model.SignUpCredentials
import com.domeai.data.model.User
import com.domeai.data.model.network.LoginRequest
import com.domeai.data.model.network.RegisterRequest
import com.domeai.data.network.AuthApiService
import com.domeai.data.preferences.AuthPreferences
import com.domeai.data.util.ApiResponse
import com.domeai.data.util.safeApiCall
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of AuthRepository
 * This implementation connects to the backend API
 */
@Singleton
class AuthRepositoryImpl @Inject constructor(
    private val authApiService: AuthApiService,
    private val authPreferences: AuthPreferences
) : AuthRepository {

    override suspend fun login(credentials: LoginCredentials): Flow<AuthResult> = flow {
        emit(AuthResult.Loading)

        try {
            // Create login request
            val loginRequest = LoginRequest(
                email = credentials.email,
                password = credentials.password
            )

            android.util.Log.d("AuthRepository", "Attempting login for email: ${credentials.email}")

            // Call API using safe API call
            when (val response = safeApiCall { authApiService.login(loginRequest) }) {
                is ApiResponse.Success -> {
                    android.util.Log.d("AuthRepository", "Login successful, received token: ${response.data.accessToken.take(10)}...")

                    // Save tokens
                    authPreferences.saveAuthTokens(
                        accessToken = response.data.accessToken,
                        tokenType = response.data.tokenType
                    )

                    android.util.Log.d("AuthRepository", "Tokens saved, fetching user info")

                    // Get user info
                    val userResponse = safeApiCall { authApiService.getCurrentUser() }
                    if (userResponse is ApiResponse.Success) {
                        val userInfo = userResponse.data
                        android.util.Log.d("AuthRepository", "User info fetched: ${userInfo.email}, tier: ${userInfo.subscriptionTier}")

                        val user = User(
                            id = userInfo.id.toString(),
                            email = userInfo.email,
                            name = userInfo.email.substringBefore("@"),
                            isEmailVerified = userInfo.isActive,
                            isPremium = userInfo.subscriptionTier == "premium" || userInfo.subscriptionTier == "expert"
                        )
                        emit(AuthResult.Success(user))
                    } else if (userResponse is ApiResponse.Error) {
                        android.util.Log.e("AuthRepository", "Error fetching user info: ${userResponse.errorMessage}")
                        android.util.Log.e("AuthRepository", "Error details: ${userResponse.errorBody}")

                        // If we can't get user info, create a basic user object
                        val user = User(
                            id = "user_${System.currentTimeMillis()}",
                            email = credentials.email,
                            name = credentials.email.substringBefore("@"),
                            isEmailVerified = true,
                            isPremium = false
                        )
                        emit(AuthResult.Success(user))
                    }
                }
                is ApiResponse.Error -> {
                    android.util.Log.e("AuthRepository", "Login failed: ${response.errorMessage}")
                    android.util.Log.e("AuthRepository", "Error details: ${response.errorBody}, code: ${response.code}")

                    val formattedError = com.domeai.data.util.ErrorFormatter.formatErrorMessage(response.errorMessage)
                    emit(AuthResult.Error(formattedError))
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("AuthRepository", "Exception during login", e)
            emit(AuthResult.Error("Unable to connect to the server. Please check your internet connection."))
        }
    }

    override suspend fun signUp(credentials: SignUpCredentials): Flow<AuthResult> = flow {
        emit(AuthResult.Loading)

        // Basic validation
        if (!credentials.email.contains("@")) {
            emit(AuthResult.Error("Invalid email format"))
            return@flow
        }

        if (credentials.password.length < 6) {
            emit(AuthResult.Error("Password must be at least 6 characters"))
            return@flow
        }

        if (credentials.password != credentials.confirmPassword) {
            emit(AuthResult.Error("Passwords do not match"))
            return@flow
        }

        if (!credentials.acceptedTerms) {
            emit(AuthResult.Error("You must accept the terms and conditions"))
            return@flow
        }

        try {
            // Create register request
            val registerRequest = RegisterRequest(
                email = credentials.email,
                password = credentials.password
            )

            // Call API using safe API call
            when (val response = safeApiCall { authApiService.register(registerRequest) }) {
                is ApiResponse.Success -> {
                    val userInfo = response.data

                    // After successful registration, login the user
                    val loginRequest = LoginRequest(
                        email = credentials.email,
                        password = credentials.password
                    )

                    // Try to login
                    try {
                        val loginResponse = safeApiCall { authApiService.login(loginRequest) }
                        if (loginResponse is ApiResponse.Success) {
                            // Save tokens
                            authPreferences.saveAuthTokens(
                                accessToken = loginResponse.data.accessToken,
                                tokenType = loginResponse.data.tokenType
                            )
                        }
                    } catch (e: Exception) {
                        // If login fails, we still continue with registration success
                        // but the user will need to login manually
                        println("Auto-login after registration failed: ${e.message}")
                    }

                    val user = User(
                        id = userInfo.id.toString(),
                        email = userInfo.email,
                        name = userInfo.email.substringBefore("@"),
                        isEmailVerified = userInfo.isActive,
                        isPremium = userInfo.subscriptionTier == "premium" || userInfo.subscriptionTier == "expert"
                    )
                    emit(AuthResult.Success(user))
                }
                is ApiResponse.Error -> {
                    val formattedError = com.domeai.data.util.ErrorFormatter.formatErrorMessage(response.errorMessage)
                    emit(AuthResult.Error(formattedError))
                }
            }
        } catch (e: Exception) {
            emit(AuthResult.Error("Unable to connect to the server. Please check your internet connection."))
        }
    }

    override suspend fun forgotPassword(request: ForgotPasswordRequest): Flow<AuthResult> = flow {
        emit(AuthResult.Loading)

        // Basic validation
        if (!request.email.contains("@")) {
            emit(AuthResult.Error("Invalid email format"))
            return@flow
        }

        // In a real app, this would call a password reset API
        // For now, we'll just simulate success
        emit(AuthResult.Success(User(id = "", email = request.email)))
    }

    override suspend fun getCurrentUser(): User? {
        // Check if we have a token
        val isAuthenticated = authPreferences.isAuthenticated.first()

        if (!isAuthenticated) {
            return null
        }

        try {
            // Call API using safe API call
            when (val response = safeApiCall { authApiService.getCurrentUser() }) {
                is ApiResponse.Success -> {
                    val userInfo = response.data
                    return User(
                        id = userInfo.id.toString(),
                        email = userInfo.email,
                        name = userInfo.email.substringBefore("@"),
                        isEmailVerified = userInfo.isActive,
                        isPremium = userInfo.subscriptionTier == "premium" || userInfo.subscriptionTier == "expert"
                    )
                }
                is ApiResponse.Error -> {
                    // Log error
                    android.util.Log.e("AuthRepository", "Error getting current user: ${response.errorMessage}")
                }
            }
        } catch (e: Exception) {
            // Log error
            android.util.Log.e("AuthRepository", "Exception getting current user: ${e.message}")
        }

        return null
    }

    override suspend fun isAuthenticated(): Boolean {
        return authPreferences.isAuthenticated.first()
    }

    override suspend fun logout() {
        // Clear auth tokens
        authPreferences.clearAuthTokens()
    }
}
