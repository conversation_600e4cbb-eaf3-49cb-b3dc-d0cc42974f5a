package com.domeai.presentation.main

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import com.domeai.presentation.theme.ThemeViewModel
import com.domeai.service.ServiceManager
import com.domeai.ui.navigation.NavGraph
import com.domeai.ui.navigation.Screen
import com.domeai.ui.theme.DomeAITheme
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var serviceManager: ServiceManager

    private val themeViewModel: ThemeViewModel by viewModels()
    private val startupViewModel: StartupViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Start monitoring the overlay service state
        // This will be called after the app is launched, but the overlay
        // won't be shown until the user enables it in the main screen
        android.util.Log.d("MainActivity", "Starting service monitoring")

        setContent {
            // Collect the dark mode state
            val isDarkMode by themeViewModel.isDarkMode.collectAsState()
            val startDestination by startupViewModel.startDestination.collectAsState()
            val navController = rememberNavController()

            DomeAITheme(darkTheme = isDarkMode) {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    if (startDestination != null) {
                        NavGraph(
                            navController = navController,
                            startDestination = startDestination!!,
                            onMainScreenEntered = {
                                // Start monitoring when the user enters the main screen
                                // This ensures the overlay service is only started after login
                                serviceManager.startMonitoring()
                            },
                            isDarkMode = isDarkMode,
                            onToggleTheme = { themeViewModel.toggleDarkMode() }
                        )
                    } else {
                        // Show loading indicator while determining start destination
                        CircularProgressIndicator(modifier = Modifier.padding(innerPadding))
                    }
                }
            }
        }
    }
}

/**
 * A simple welcome screen that can be used as a placeholder
 */
@Composable
fun WelcomeScreen(modifier: Modifier = Modifier) {
    Text(
        text = "Welcome to DomeAI Scam Detector!",
        modifier = modifier.fillMaxSize(),
        textAlign = TextAlign.Center
    )
}

@Preview(showBackground = true)
@Composable
fun WelcomeScreenPreview() {
    DomeAITheme {
        WelcomeScreen()
    }
}
