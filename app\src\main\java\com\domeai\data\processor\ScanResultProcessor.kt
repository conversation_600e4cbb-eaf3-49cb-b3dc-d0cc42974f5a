package com.domeai.data.processor

import android.net.Uri
import com.domeai.data.model.RiskLevel
import com.domeai.data.model.ScanResult
import com.domeai.data.model.ScanSourceType
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Processor for handling scan results from different sources
 */
@Singleton
class ScanResultProcessor @Inject constructor() {
    
    /**
     * Process text input for scanning
     */
    suspend fun processTextInput(text: String): ScanResult {
        // In a real app, this would call the backend API
        // For now, we'll just create a mock result
        
        // Simulate processing delay
        kotlinx.coroutines.delay(1500)
        
        // Create a mock result
        return ScanResult(
            id = UUID.randomUUID().toString(),
            timestamp = System.currentTimeMillis(),
            riskScore = (0..100).random(),
            riskLevel = RiskLevel.values().random(),
            redFlags = generateMockRedFlags(),
            explanation = "This is a mock analysis of the text: ${text.take(50)}${if (text.length > 50) "..." else ""}",
            sourceType = ScanSourceType.MANUAL_TEXT,
            sourceContent = text
        )
    }
    
    /**
     * Process URL input for scanning
     */
    suspend fun processUrlInput(url: String): ScanResult {
        // In a real app, this would call the backend API
        // For now, we'll just create a mock result
        
        // Simulate processing delay
        kotlinx.coroutines.delay(2000)
        
        // Create a mock result
        return ScanResult(
            id = UUID.randomUUID().toString(),
            timestamp = System.currentTimeMillis(),
            riskScore = (0..100).random(),
            riskLevel = RiskLevel.values().random(),
            redFlags = generateMockRedFlags(),
            explanation = "This is a mock analysis of the URL: $url",
            sourceType = ScanSourceType.MANUAL_TEXT,
            sourceContent = url
        )
    }
    
    /**
     * Process image input for scanning
     */
    suspend fun processImageInput(imageUri: Uri): ScanResult {
        // In a real app, this would call the backend API
        // For now, we'll just create a mock result
        
        // Simulate processing delay
        kotlinx.coroutines.delay(3000)
        
        // Create a mock result
        return ScanResult(
            id = UUID.randomUUID().toString(),
            timestamp = System.currentTimeMillis(),
            riskScore = (0..100).random(),
            riskLevel = RiskLevel.values().random(),
            redFlags = generateMockRedFlags(),
            explanation = "This is a mock analysis of the image at: $imageUri",
            sourceType = ScanSourceType.MANUAL_IMAGE,
            sourceContent = imageUri.toString()
        )
    }
    
    /**
     * Process screenshot for scanning
     */
    suspend fun processScreenshot(screenshotUri: Uri, appContext: String?): ScanResult {
        // In a real app, this would call the backend API
        // For now, we'll just create a mock result
        
        // Simulate processing delay
        kotlinx.coroutines.delay(2500)
        
        // Create a mock result
        return ScanResult(
            id = UUID.randomUUID().toString(),
            timestamp = System.currentTimeMillis(),
            riskScore = (0..100).random(),
            riskLevel = RiskLevel.values().random(),
            redFlags = generateMockRedFlags(),
            explanation = "This is a mock analysis of a screenshot from: ${appContext ?: "unknown app"}",
            sourceType = ScanSourceType.OVERLAY_SCREENSHOT,
            sourceContent = screenshotUri.toString(),
            isGeneralQuestion = false
        )
    }
    
    /**
     * Generate mock red flags for testing
     */
    private fun generateMockRedFlags(): List<String> {
        val allFlags = listOf(
            "Suspicious URL structure",
            "Recently registered domain",
            "Requests sensitive information",
            "Poor grammar and spelling",
            "Urgent call to action",
            "Unrealistic promises",
            "Suspicious sender address",
            "Mismatched links",
            "Unusual payment methods",
            "Too good to be true offers"
        )
        
        // Randomly select 0-4 flags
        return allFlags.shuffled().take((0..4).random())
    }
}
