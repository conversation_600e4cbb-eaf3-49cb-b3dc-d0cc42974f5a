package com.domeai.presentation.onboarding

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.domeai.data.preferences.OnboardingPreferences
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the onboarding screen
 */
@HiltViewModel
class OnboardingViewModel @Inject constructor(
    private val onboardingPreferences: OnboardingPreferences
) : ViewModel() {

    // Current page index
    private val _currentPage = MutableStateFlow(0)
    val currentPage: StateFlow<Int> = _currentPage.asStateFlow()

    // Total number of pages
    val pageCount = 4

    /**
     * Move to the next page
     */
    fun nextPage() {
        if (_currentPage.value < pageCount - 1) {
            _currentPage.value += 1
        }
    }

    /**
     * Move to the previous page
     */
    fun previousPage() {
        if (_currentPage.value > 0) {
            _currentPage.value -= 1
        }
    }

    /**
     * Set the current page
     */
    fun setPage(page: Int) {
        if (page in 0 until pageCount) {
            _currentPage.value = page
        }
    }

    /**
     * Complete onboarding
     */
    fun completeOnboarding() {
        viewModelScope.launch {
            onboardingPreferences.setOnboardingCompleted(true)
        }
    }

    /**
     * Skip onboarding
     */
    fun skipOnboarding() {
        completeOnboarding()
    }
}
