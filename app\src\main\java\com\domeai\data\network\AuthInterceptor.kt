package com.domeai.data.network

import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import com.domeai.data.preferences.AuthPreferences
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Interceptor to add authentication headers to requests
 */
@Singleton
class AuthInterceptor @Inject constructor(
    private val authPreferences: AuthPreferences
) : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        // Special handling for login endpoint
        if (isLoginEndpoint(originalRequest)) {
            return handleLoginRequest(chain, originalRequest)
        }

        // For all other requests, add the Authorization header if available
        val authHeader = runBlocking { authPreferences.authHeader.first() }

        return if (authHeader != null) {
            val newRequest = originalRequest.newBuilder()
                .header("Authorization", authHeader)
                .build()
            chain.proceed(newRequest)
        } else {
            chain.proceed(originalRequest)
        }
    }

    /**
     * Check if the request is for the login endpoint
     */
    private fun isLoginEndpoint(request: Request): Boolean {
        return request.url.encodedPath.endsWith("/auth/login") && request.method == "POST"
    }

    /**
     * Handle login request by converting JSON to form data
     */
    private fun handleLoginRequest(chain: Interceptor.Chain, originalRequest: Request): Response {
        // Get the original request body as a string
        val originalBody = originalRequest.body

        // If there's no body, just proceed with the original request
        if (originalBody == null) {
            return chain.proceed(originalRequest)
        }

        try {
            // Buffer the body so we can read it
            val buffer = okio.Buffer()
            originalBody.writeTo(buffer)
            val bodyString = buffer.readUtf8()

            // Log the original request body for debugging
            android.util.Log.d("AuthInterceptor", "Original login request body: $bodyString")

            // Parse the JSON to extract email and password
            // This is a simple approach - in a production app, use a proper JSON parser
            val emailRegex = "\"username\"\\s*:\\s*\"([^\"]+)\"".toRegex()
            val passwordRegex = "\"password\"\\s*:\\s*\"([^\"]+)\"".toRegex()

            val emailMatch = emailRegex.find(bodyString)
            val passwordMatch = passwordRegex.find(bodyString)

            val email = emailMatch?.groupValues?.get(1) ?: ""
            val password = passwordMatch?.groupValues?.get(1) ?: ""

            android.util.Log.d("AuthInterceptor", "Extracted email: $email, password length: ${password.length}")

            // Create a form data request
            val formBody = okhttp3.FormBody.Builder()
                .add("username", email)
                .add("password", password)
                .build()

            // Create a new request with form data
            val newRequest = originalRequest.newBuilder()
                .header("Content-Type", "application/x-www-form-urlencoded")
                .method("POST", formBody)
                .build()

            android.util.Log.d("AuthInterceptor", "Converted login request to form data")

            // Execute the request
            val response = chain.proceed(newRequest)

            // Log the response for debugging
            val responseBody = response.peekBody(Long.MAX_VALUE).string()
            android.util.Log.d("AuthInterceptor", "Login response: ${response.code}, body: $responseBody")

            return response
        } catch (e: Exception) {
            // Log the exception
            android.util.Log.e("AuthInterceptor", "Error handling login request", e)

            // If anything goes wrong, proceed with the original request
            return chain.proceed(originalRequest)
        }
    }
}
