package com.domeai.data.repository

import com.domeai.data.model.PrivacySettings
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Fake implementation of PrivacyRepository for testing and development
 */
@Singleton
class FakePrivacyRepository @Inject constructor() : PrivacyRepository {
    
    private val privacySettingsFlow = MutableStateFlow(
        PrivacySettings(
            userId = "user123",
            dataSharingEnabled = true,
            analyticsCollectionEnabled = true,
            personalizationEnabled = true,
            biometricAuthEnabled = false,
            screenLockEnabled = false,
            twoFactorAuthEnabled = false
        )
    )
    
    override suspend fun getPrivacySettings(): PrivacySettings {
        return privacySettingsFlow.value
    }
    
    override suspend fun setDataSharing(enabled: Boolean): PrivacySettings {
        val updatedSettings = privacySettingsFlow.value.copy(
            dataSharingEnabled = enabled,
            lastUpdated = System.currentTimeMillis()
        )
        privacySettingsFlow.value = updatedSettings
        return updatedSettings
    }
    
    override suspend fun setAnalyticsCollection(enabled: Boolean): PrivacySettings {
        val updatedSettings = privacySettingsFlow.value.copy(
            analyticsCollectionEnabled = enabled,
            lastUpdated = System.currentTimeMillis()
        )
        privacySettingsFlow.value = updatedSettings
        return updatedSettings
    }
    
    override suspend fun setPersonalization(enabled: Boolean): PrivacySettings {
        val updatedSettings = privacySettingsFlow.value.copy(
            personalizationEnabled = enabled,
            lastUpdated = System.currentTimeMillis()
        )
        privacySettingsFlow.value = updatedSettings
        return updatedSettings
    }
    
    override suspend fun setBiometricAuth(enabled: Boolean): PrivacySettings {
        val updatedSettings = privacySettingsFlow.value.copy(
            biometricAuthEnabled = enabled,
            lastUpdated = System.currentTimeMillis()
        )
        privacySettingsFlow.value = updatedSettings
        return updatedSettings
    }
    
    override suspend fun setScreenLock(enabled: Boolean): PrivacySettings {
        val updatedSettings = privacySettingsFlow.value.copy(
            screenLockEnabled = enabled,
            lastUpdated = System.currentTimeMillis()
        )
        privacySettingsFlow.value = updatedSettings
        return updatedSettings
    }
    
    override suspend fun setTwoFactorAuth(enabled: Boolean): PrivacySettings {
        val updatedSettings = privacySettingsFlow.value.copy(
            twoFactorAuthEnabled = enabled,
            lastUpdated = System.currentTimeMillis()
        )
        privacySettingsFlow.value = updatedSettings
        return updatedSettings
    }
    
    override suspend fun exportData(): String {
        // Simulate data export
        return "data_export_url"
    }
    
    override suspend fun deleteData(): Boolean {
        // Simulate data deletion
        return true
    }
    
    override fun getPrivacySettingsFlow(): Flow<PrivacySettings> {
        return privacySettingsFlow
    }
}
