from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/')
def root():
    return jsonify({"message": "Flask webhook test is running!"})

@app.route('/webhook', methods=['POST'])
def webhook():
    data = request.json
    print(f"Received webhook: {data}")
    return jsonify({"status": "success", "message": "Webhook received"})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8000, debug=True)
