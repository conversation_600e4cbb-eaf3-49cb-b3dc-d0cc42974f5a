#!/usr/bin/env python3
"""
Test script for image analysis with the DomeAI API using Google Drive images.
"""

import requests
import io
import json
import time
import re
import sys

def get_direct_download_url(gdrive_url):
    """
    Convert a Google Drive sharing URL to a direct download URL.
    """
    # Extract the file ID from the Google Drive URL
    file_id_match = re.search(r'/d/([^/]+)', gdrive_url)
    if not file_id_match:
        raise ValueError(f"Could not extract file ID from URL: {gdrive_url}")
    
    file_id = file_id_match.group(1)
    return f"https://drive.google.com/uc?export=download&id={file_id}"

def get_auth_token(api_url="http://localhost:8000"):
    """Get an authentication token from the API."""
    response = requests.post(
        f"{api_url}/api/v1/auth/login",
        data={"username": "<EMAIL>", "password": "StrongPassword123"},
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    if response.status_code != 200:
        raise Exception(f"Failed to get auth token: {response.text}")
    return response.json()["access_token"]

def download_image(url):
    """Download an image from a URL."""
    print(f"Downloading image from URL: {url}")
    response = requests.get(url)
    if response.status_code != 200:
        raise Exception(f"Failed to download image: {response.status_code}, {response.text}")
    return response.content

def upload_image_for_analysis(image_data, context, token, api_url="http://localhost:8000"):
    """Upload an image to the API for analysis."""
    files = {"file": ("image.jpg", io.BytesIO(image_data), "image/jpeg")}
    data = {"user_provided_context": context}
    response = requests.post(
        f"{api_url}/api/v1/scans/upload-image/",
        files=files,
        data=data,
        headers={"Authorization": f"Bearer {token}"}
    )
    
    if response.status_code != 200:
        raise Exception(f"Failed to upload image: {response.status_code}, {response.text}")
    
    return response.json()

def get_scan_result(scan_id, token, api_url="http://localhost:8000"):
    """Get the result of a scan."""
    response = requests.get(
        f"{api_url}/api/v1/scans/{scan_id}",
        headers={"Authorization": f"Bearer {token}"}
    )
    
    if response.status_code != 200:
        raise Exception(f"Failed to get scan result: {response.status_code}, {response.text}")
    
    return response.json()

def wait_for_scan_completion(scan_id, token, api_url="http://localhost:8000", max_wait_seconds=120):
    """Wait for a scan to complete."""
    start_time = time.time()
    while time.time() - start_time < max_wait_seconds:
        result = get_scan_result(scan_id, token, api_url)
        if result["status"] == "completed":
            return result
        elif result["status"] == "failed":
            raise Exception(f"Scan failed: {result.get('error_message', 'Unknown error')}")
        
        print(f"Waiting for scan {scan_id} to complete... Current status: {result['status']}")
        time.sleep(5)
    
    raise Exception(f"Scan did not complete within {max_wait_seconds} seconds")

def print_analysis_summary(result):
    """Print a summary of the analysis result."""
    analysis_result = result.get("analysis_result", {})
    if not analysis_result:
        print("No analysis result found in the scan result.")
        return
    
    print("\nAnalysis Summary:")
    print(f"Extracted Text: {analysis_result.get('extracted_text', 'N/A')[:200]}...")
    print(f"Image Description: {analysis_result.get('image_description', 'N/A')[:200]}...")
    print(f"Platform Identified: {analysis_result.get('platform_identified', 'N/A')}")
    
    scam_indicators = analysis_result.get('scam_indicators', [])
    print(f"Scam Indicators: {len(scam_indicators)} found")
    for i, indicator in enumerate(scam_indicators[:5], 1):
        print(f"  {i}. {indicator[:100]}...")
    if len(scam_indicators) > 5:
        print(f"  ... and {len(scam_indicators) - 5} more")
    
    print(f"Authenticity Explanation: {analysis_result.get('authenticity_explanation', 'N/A')[:200]}...")
    print(f"Model Used: {analysis_result.get('model_used', 'N/A')}")

def test_image(gdrive_url, context, output_file=None):
    """Test image analysis with a Google Drive image."""
    # Get direct download URL
    direct_url = get_direct_download_url(gdrive_url)
    
    # Get auth token
    token = get_auth_token()
    print(f"Got auth token: {token[:10]}...")
    
    # Download image
    image_data = download_image(direct_url)
    print(f"Downloaded image: {len(image_data)} bytes")
    
    # Upload image for analysis
    upload_result = upload_image_for_analysis(image_data, context, token)
    scan_id = upload_result["id"]
    print(f"Image uploaded successfully. Scan ID: {scan_id}")
    
    # Wait for scan to complete
    print("Waiting for scan to complete...")
    scan_result = wait_for_scan_completion(scan_id, token)
    
    # Print analysis summary
    print_analysis_summary(scan_result)
    
    # Save result to file if requested
    if output_file:
        with open(output_file, "w") as f:
            json.dump(scan_result, f, indent=2)
        print(f"Full result saved to {output_file}")
    
    return scan_result

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python test_gdrive_images.py <gdrive_url> <context> [output_file]")
        sys.exit(1)
    
    gdrive_url = sys.argv[1]
    context = sys.argv[2]
    output_file = sys.argv[3] if len(sys.argv) > 3 else None
    
    test_image(gdrive_url, context, output_file)
