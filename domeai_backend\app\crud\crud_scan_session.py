import uuid
from datetime import datetime
from typing import List, Optional

from sqlalchemy.orm import Session

from app.models.scan_session import ScanSession
from app.models.scan import Scan


def create_scan_session(
    db: Session, *, owner_id: int, title: Optional[str] = None
) -> ScanSession:
    """
    Create a new scan session.

    Args:
        db: Database session
        owner_id: ID of the user who owns the session
        title: Optional title for the session

    Returns:
        The created scan session
    """
    db_session = ScanSession(
        owner_id=owner_id,
        title=title
    )
    db.add(db_session)
    db.commit()
    db.refresh(db_session)
    return db_session


def get_scan_session(
    db: Session, *, session_id: uuid.UUID, owner_id: int
) -> Optional[ScanSession]:
    """
    Get a scan session by ID and owner ID.

    Args:
        db: Database session
        session_id: ID of the scan session to get
        owner_id: ID of the user who owns the session

    Returns:
        The scan session, or None if not found
    """
    return db.query(ScanSession).filter(
        ScanSession.id == session_id,
        ScanSession.owner_id == owner_id
    ).first()


def get_scan_sessions_by_owner(
    db: Session, *, owner_id: int, skip: int = 0, limit: int = 10
) -> List[ScanSession]:
    """
    Get all scan sessions for a user.

    Args:
        db: Database session
        owner_id: ID of the user who owns the sessions
        skip: Number of sessions to skip
        limit: Maximum number of sessions to return

    Returns:
        List of scan sessions
    """
    return db.query(ScanSession).filter(
        ScanSession.owner_id == owner_id
    ).order_by(ScanSession.last_activity_at.desc()).offset(skip).limit(limit).all()


def update_scan_session_activity(
    db: Session, *, db_session: ScanSession
) -> ScanSession:
    """
    Update the last_activity_at timestamp for a scan session.

    Args:
        db: Database session
        db_session: The scan session to update

    Returns:
        The updated scan session
    """
    # Force update the last_activity_at timestamp
    db.query(ScanSession).filter(ScanSession.id == db_session.id).update(
        {"last_activity_at": datetime.now()},
        synchronize_session=False
    )
    db.commit()
    db.refresh(db_session)
    return db_session


def get_previous_scans_in_session(
    db: Session, *, session_id: uuid.UUID, current_scan_id: int
) -> List[Scan]:
    """
    Get all scans in a session that were created before the current scan.

    Args:
        db: Database session
        session_id: ID of the scan session
        current_scan_id: ID of the current scan

    Returns:
        List of scans
    """
    return db.query(Scan).filter(
        Scan.scan_session_id == session_id,
        Scan.id < current_scan_id
    ).order_by(Scan.created_at.asc()).all()


def update_scan_session(
    db: Session, *, db_session: ScanSession, title: Optional[str] = None
) -> ScanSession:
    """
    Update a scan session.

    Args:
        db: Database session
        db_session: The scan session to update
        title: New title for the session

    Returns:
        The updated scan session
    """
    if title is not None:
        db_session.title = title

    db.commit()
    db.refresh(db_session)
    return db_session
