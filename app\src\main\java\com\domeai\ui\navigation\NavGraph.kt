package com.domeai.ui.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.domeai.presentation.auth.ForgotPasswordScreen
import com.domeai.presentation.auth.LoginScreen
import com.domeai.presentation.auth.SignUpScreen
import com.domeai.presentation.legal.PrivacyPolicyScreen
import com.domeai.presentation.legal.TermsOfServiceScreen
import com.domeai.presentation.main.MainScreen
import com.domeai.presentation.main.WelcomeScreen
import com.domeai.presentation.onboarding.OnboardingScreen
import com.domeai.presentation.scan.ManualScanScreen
import com.domeai.presentation.scan.ScanDetailScreen
import com.domeai.presentation.scan.ScanHistoryScreen

@Composable
fun NavGraph(
    navController: NavHostController,
    startDestination: String = Screen.Login.route,
    onMainScreenEntered: () -> Unit = {},
    isDarkMode: Boolean = false,
    onToggleTheme: () -> Unit = {}
) {
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // Authentication Screens
        composable(route = Screen.Login.route) {
            LoginScreen(
                onNavigateToSignUp = { navController.navigate(Screen.SignUp.route) },
                onNavigateToForgotPassword = { navController.navigate(Screen.ForgotPassword.route) },
                onNavigateToMain = {
                    navController.navigate(Screen.Main.route) {
                        popUpTo(Screen.Login.route) { inclusive = true }
                    }
                }
            )
        }

        composable(route = Screen.SignUp.route) {
            SignUpScreen(
                onNavigateToLogin = { navController.navigate(Screen.Login.route) {
                    popUpTo(Screen.SignUp.route) { inclusive = true }
                }},
                onNavigateToDataConsent = { navController.navigate(Screen.DataConsent.route) },
                onNavigateToMain = {
                    navController.navigate(Screen.Main.route) {
                        popUpTo(Screen.SignUp.route) { inclusive = true }
                    }
                },
                onNavigateToTermsOfService = { navController.navigate(Screen.TermsOfService.route) },
                onNavigateToPrivacyPolicy = { navController.navigate(Screen.PrivacyPolicy.route) }
            )
        }

        composable(route = Screen.ForgotPassword.route) {
            ForgotPasswordScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // Temporary Welcome Screen (will be replaced with Main Screen)
        composable(route = Screen.Welcome.route) {
            WelcomeScreen()
        }

        // Main Screen
        composable(route = Screen.Main.route) {
            // Call the callback when the main screen is entered
            onMainScreenEntered()

            MainScreen(
                onNavigateToManualScan = { navController.navigate(Screen.ManualAnalysis.route) },
                onNavigateToSettings = { navController.navigate(Screen.Settings.route) },
                onNavigateToScanDetail = { scanId ->
                    navController.navigate(Screen.ScanDetail.createRoute(scanId))
                },
                onNavigateToLogin = {
                    navController.navigate(Screen.Login.route) {
                        popUpTo(Screen.Main.route) { inclusive = true }
                    }
                },
                onNavigateToScanHistory = { navController.navigate(Screen.ScanHistory.route) },
                onNavigateToAccountSettings = { navController.navigate(Screen.AccountSettings.route) },
                onNavigateToUserProfileMenu = { navController.navigate(Screen.UserProfileMenu.route) },
                onNavigateToSubscriptionDetails = { navController.navigate(Screen.SubscriptionDetails.route) },
                isDarkMode = isDarkMode,
                onToggleTheme = onToggleTheme
            )
        }

        // Data Consent Screen (currently using Welcome Screen as placeholder)
        composable(route = Screen.DataConsent.route) {
            WelcomeScreen()
        }

        // Scan History Screen
        composable(route = Screen.ScanHistory.route) {
            ScanHistoryScreen(
                onNavigateBack = { navController.popBackStack() },
                onScanClick = { scanId ->
                    navController.navigate(Screen.ScanDetail.createRoute(scanId))
                }
            )
        }

        // Scan Detail Screen
        composable(
            route = Screen.ScanDetail.route
        ) { backStackEntry ->
            val scanId = backStackEntry.arguments?.getString("scanId") ?: ""
            ScanDetailScreen(
                scanId = scanId,
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // Manual Analysis Screen
        composable(route = Screen.ManualAnalysis.route) {
            ManualScanScreen(
                onNavigateBack = { navController.popBackStack() },
                onScanComplete = { scanId ->
                    navController.navigate(Screen.ScanDetail.createRoute(scanId))
                }
            )
        }

        // Onboarding Screen
        composable(route = Screen.Onboarding.route) {
            OnboardingScreen(
                onNavigateToLogin = {
                    navController.navigate(Screen.Login.route) {
                        popUpTo(Screen.Onboarding.route) { inclusive = true }
                    }
                }
            )
        }

        // Account Settings Screen
        composable(route = Screen.AccountSettings.route) {
            com.domeai.presentation.settings.AccountSettingsScreen(
                onNavigateBack = { navController.popBackStack() },
                onNavigateToChangePassword = { navController.navigate(Screen.ChangePassword.route) },
                onNavigateToSubscriptionDetails = { navController.navigate(Screen.SubscriptionDetails.route) },
                onNavigateToPrivacySecurity = { navController.navigate(Screen.PrivacySecurity.route) }
            )
        }

        // User Profile Menu Screen
        composable(route = Screen.UserProfileMenu.route) {
            com.domeai.presentation.profile.UserProfileMenuScreen(
                onNavigateBack = { navController.popBackStack() },
                onNavigateToAccountSettings = { navController.navigate(Screen.AccountSettings.route) },
                onNavigateToSubscriptionDetails = { navController.navigate(Screen.SubscriptionDetails.route) },
                onNavigateToCustomerSupport = { /* Open email app or support page */ }
            )
        }

        // Change Password Screen
        composable(route = Screen.ChangePassword.route) {
            com.domeai.presentation.settings.ChangePasswordScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // Subscription Details Screen
        composable(route = Screen.SubscriptionDetails.route) {
            com.domeai.presentation.settings.SubscriptionDetailsScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // Privacy & Security Screen
        composable(route = Screen.PrivacySecurity.route) {
            com.domeai.presentation.settings.PrivacySecurityScreen(
                onNavigateBack = { navController.popBackStack() },
                onNavigateToPrivacyPolicy = { navController.navigate(Screen.PrivacyPolicy.route) },
                onNavigateToTermsOfService = { navController.navigate(Screen.TermsOfService.route) }
            )
        }

        // Terms of Service Screen
        composable(route = Screen.TermsOfService.route) {
            TermsOfServiceScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // Privacy Policy Screen
        composable(route = Screen.PrivacyPolicy.route) {
            PrivacyPolicyScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
    }
}
