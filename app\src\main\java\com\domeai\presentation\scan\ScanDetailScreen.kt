package com.domeai.presentation.scan

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewModelScope
import com.domeai.data.model.RiskLevel
import com.domeai.data.model.ScanResult
import com.domeai.data.model.content
import com.domeai.data.model.description
import com.domeai.data.model.getDate
import com.domeai.data.model.isFavorite
import com.domeai.data.model.title
import com.domeai.data.repository.OverlayRepository
import com.domeai.data.repository.ScanRepository
import com.domeai.ui.composables.getRiskColor
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Locale
import javax.inject.Inject

/**
 * ViewModel for the scan detail screen
 */
@HiltViewModel
class ScanDetailViewModel @Inject constructor(
    private val scanRepository: ScanRepository,
    private val overlayRepository: OverlayRepository
) : androidx.lifecycle.ViewModel() {

    private val _scanResult = MutableStateFlow<ScanResult?>(null)
    val scanResult: StateFlow<ScanResult?> = _scanResult.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // Example scan data for demonstration
    private val exampleScans = mapOf(
        "high_risk" to ScanResult(
            id = "high_risk",
            timestamp = System.currentTimeMillis() - 86400000, // 1 day ago
            riskScore = 85,
            riskLevel = RiskLevel.HIGH_RISK,
            redFlags = listOf(
                "Suspicious URL detected",
                "Request for personal information",
                "Urgent action required language"
            ),
            explanation = "This appears to be a phishing attempt trying to steal your personal information.",
            sourceType = com.domeai.data.model.ScanSourceType.OVERLAY_SCREENSHOT,
            sourceContent = "Your account has been compromised. Please verify your identity by clicking on this link: http://suspicious-url.com"
        ),
        "medium_risk" to ScanResult(
            id = "medium_risk",
            timestamp = System.currentTimeMillis() - *********, // 2 days ago
            riskScore = 45,
            riskLevel = RiskLevel.MEDIUM_RISK,
            redFlags = listOf(
                "Unusual request",
                "Grammar errors"
            ),
            explanation = "This message contains some suspicious elements but may be legitimate. Proceed with caution.",
            sourceType = com.domeai.data.model.ScanSourceType.MANUAL_TEXT,
            sourceContent = "Hello, we need to verify you're account details. Please respond with your information."
        ),
        "low_risk" to ScanResult(
            id = "low_risk",
            timestamp = System.currentTimeMillis() - *********, // 3 days ago
            riskScore = 10,
            riskLevel = RiskLevel.LOW_RISK,
            redFlags = listOf(
                "Contains promotional content"
            ),
            explanation = "This appears to be a legitimate marketing message.",
            sourceType = com.domeai.data.model.ScanSourceType.MANUAL_IMAGE,
            sourceContent = "Check out our new summer sale! 20% off all items."
        ),
        "safe" to ScanResult(
            id = "safe",
            timestamp = System.currentTimeMillis() - *********, // 4 days ago
            riskScore = 0,
            riskLevel = RiskLevel.SAFE,
            redFlags = emptyList(),
            explanation = "No suspicious elements detected in this content.",
            sourceType = com.domeai.data.model.ScanSourceType.OVERLAY_SCREENSHOT,
            sourceContent = "Your order #12345 has been shipped and will arrive on Monday. Track your package here: https://legitimate-store.com/track"
        )
    )

    fun loadScan(scanId: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                // For demonstration purposes, we'll just use example data
                // In a real app, this would fetch from repositories
                delay(500) // Simulate network delay

                // Get a random example scan if the ID doesn't match any specific example
                val scan = when {
                    exampleScans.containsKey(scanId) -> exampleScans[scanId]
                    scanId.contains("high") -> exampleScans["high_risk"]
                    scanId.contains("medium") -> exampleScans["medium_risk"]
                    scanId.contains("low") -> exampleScans["low_risk"]
                    scanId.contains("safe") -> exampleScans["safe"]
                    else -> exampleScans.values.random()
                }

                _scanResult.value = scan
            } catch (e: Exception) {
                _error.value = "Error loading scan: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun toggleFavorite() {
        viewModelScope.launch {
            try {
                _scanResult.value?.let { scan ->
                    // In a real app, this would update in the repository
                    // For now, we'll just log it
                    scanRepository.toggleFavorite(scan.id)
                }
            } catch (e: Exception) {
                _error.value = "Error updating favorite status: ${e.message}"
            }
        }
    }

    fun share() {
        // In a real app, this would share the scan details
    }
}

/**
 * Screen to display detailed information about a scan
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScanDetailScreen(
    scanId: String,
    onNavigateBack: () -> Unit,
    viewModel: ScanDetailViewModel = hiltViewModel()
) {
    val scan by viewModel.scanResult.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Load the scan when the screen is first displayed
    LaunchedEffect(scanId) {
        viewModel.loadScan(scanId)
    }

    // Show error in snackbar if present
    LaunchedEffect(error) {
        error?.let {
            snackbarHostState.showSnackbar(it)
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Scan Details") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                actions = {
                    IconButton(
                        onClick = { viewModel.toggleFavorite() }
                    ) {
                        Icon(
                            imageVector = if (scan?.isFavorite == true) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                            contentDescription = if (scan?.isFavorite == true) "Remove from Favorites" else "Add to Favorites"
                        )
                    }

                    IconButton(onClick = { viewModel.share() }) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = "Share"
                        )
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (scan == null) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Scan not found",
                    style = MaterialTheme.typography.titleLarge
                )
            }
        } else {
            ScanDetailContent(
                scan = scan!!,
                onToggleFavorite = { viewModel.toggleFavorite() },
                modifier = Modifier.padding(paddingValues)
            )
        }
    }
}

/**
 * Content of the scan detail screen
 */
@Composable
fun ScanDetailContent(
    scan: ScanResult,
    onToggleFavorite: () -> Unit,
    modifier: Modifier = Modifier
) {
    val dateFormat = remember { SimpleDateFormat("MMMM dd, yyyy 'at' HH:mm", Locale.getDefault()) }
    val scrollState = rememberScrollState()

    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
            .padding(16.dp)
    ) {
        // Risk level indicator
        RiskLevelHeader(
            riskLevel = scan.riskLevel,
            riskScore = scan.riskScore
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Scan content
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Scanned Content",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = scan.content,
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "Scanned on ${dateFormat.format(scan.getDate())}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Analysis
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "AI Analysis",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = scan.description,
                    style = MaterialTheme.typography.bodyMedium
                )

                if (scan.redFlags.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "Red Flags Detected",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    scan.redFlags.forEach { redFlag ->
                        Row(
                            modifier = Modifier.padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Warning,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.error,
                                modifier = Modifier.size(16.dp)
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            Text(
                                text = redFlag,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Recommendations
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Recommended Actions",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(16.dp))

                // These would come from the AI model in a real app
                val recommendations = when (scan.riskLevel) {
                    RiskLevel.SAFE -> listOf(
                        "This content appears to be safe.",
                        "No action is required."
                    )
                    RiskLevel.LOW_RISK -> listOf(
                        "Exercise normal caution.",
                        "Verify the source before taking any action."
                    )
                    RiskLevel.MEDIUM_RISK -> listOf(
                        "Be cautious with this content.",
                        "Do not share personal information.",
                        "Verify with an official source before proceeding."
                    )
                    RiskLevel.HIGH_RISK -> listOf(
                        "Avoid interacting with this content.",
                        "Do not click on any links or download any files.",
                        "Do not share personal or financial information.",
                        "Report this content to the relevant platform."
                    )
                }

                recommendations.forEach { recommendation ->
                    Row(
                        modifier = Modifier.padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(16.dp)
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = recommendation,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                Button(
                    onClick = { /* Report functionality */ },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("Report This Content")
                }
            }
        }

        Spacer(modifier = Modifier.height(32.dp))
    }
}

/**
 * Header showing the risk level and score
 */
@Composable
fun RiskLevelHeader(
    riskLevel: RiskLevel,
    riskScore: Int
) {
    val riskColor = getRiskColor(riskLevel)
    val riskIcon: ImageVector = when (riskLevel) {
        RiskLevel.SAFE, RiskLevel.LOW_RISK -> Icons.Default.CheckCircle
        RiskLevel.MEDIUM_RISK, RiskLevel.HIGH_RISK -> Icons.Default.Warning
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(80.dp)
                .clip(RoundedCornerShape(40.dp))
                .background(riskColor.copy(alpha = 0.1f)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = riskIcon,
                contentDescription = null,
                tint = riskColor,
                modifier = Modifier.size(48.dp)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = riskLevel.name.replace("_", " "),
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = riskColor
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "Risk Score: $riskScore%",
            style = MaterialTheme.typography.titleMedium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = when (riskLevel) {
                RiskLevel.SAFE -> "This content appears to be safe."
                RiskLevel.LOW_RISK -> "This content has a low risk level. Exercise normal caution."
                RiskLevel.MEDIUM_RISK -> "This content has a medium risk level. Be cautious."
                RiskLevel.HIGH_RISK -> "This content has a high risk level. Avoid interacting with it."
            },
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Divider()
    }
}
